package middleware

import (
	"net/http"
	"strconv"

	"visualreadingonline/internal/config"
	"visualreadingonline/internal/models"

	"github.com/gorilla/sessions"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// Config middleware adds configuration to the context
func Config(cfg *config.Config) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("config", cfg)
			return next(c)
		}
	}
}

// Database middleware adds database connection to the context
func Database(db *gorm.DB) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("db", db)
			return next(c)
		}
	}
}

// Session middleware adds session management
func Session(secret string) echo.MiddlewareFunc {
	store := sessions.NewCookieStore([]byte(secret))
	store.Options = &sessions.Options{
		Path:     "/",
		MaxAge:   86400 * 7, // 7 days
		HttpOnly: true,
		Secure:   false, // Set to true in production with HTTPS
		SameSite: http.SameSiteLaxMode,
	}

	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			session, err := store.Get(c.Request(), "session")
			if err != nil {
				// If session is corrupted, create a new one
				session, _ = store.New(c.Request(), "session")
			}
			c.Set("session", session)
			return next(c)
		}
	}
}

// Auth middleware loads the current user from session
func Auth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			session := c.Get("session").(*sessions.Session)
			db := c.Get("db").(*gorm.DB)

			// Check if user ID exists in session
			if userID, ok := session.Values["user_id"]; ok {
				if id, err := strconv.ParseUint(userID.(string), 10, 32); err == nil {
					var user models.User
					if err := db.Preload("Profile.Group").First(&user, uint(id)).Error; err == nil {
						c.Set("user", &user)
					}
				}
			}

			return next(c)
		}
	}
}

// RequireAuth middleware requires authentication
func RequireAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			user := c.Get("user")
			if user == nil {
				// Store the intended URL for redirect after login
				session := c.Get("session").(*sessions.Session)
				session.Values["redirect_url"] = c.Request().URL.String()
				session.Save(c.Request(), c.Response())
				
				return c.Redirect(http.StatusSeeOther, "/auth/login")
			}
			return next(c)
		}
	}
}

// RequireAdmin middleware requires admin role
func RequireAdmin() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			user := c.Get("user")
			if user == nil {
				return c.Redirect(http.StatusSeeOther, "/auth/login")
			}

			u := user.(*models.User)
			if !u.IsAdmin() {
				return echo.NewHTTPError(http.StatusForbidden, "Admin access required")
			}

			return next(c)
		}
	}
}

// RequireProfileCompletion middleware redirects to profile completion if needed
func RequireProfileCompletion() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			user := c.Get("user")
			if user == nil {
				return c.Redirect(http.StatusSeeOther, "/auth/login")
			}

			u := user.(*models.User)
			if !u.HasCompletedProfile() {
				// Don't redirect if already on profile completion page
				if c.Request().URL.Path != "/app/complete-profile" {
					return c.Redirect(http.StatusSeeOther, "/app/complete-profile")
				}
			}

			return next(c)
		}
	}
}
