package handlers

import (
	"net/http"

	"github.com/labstack/echo/v4"
)

// AdminDashboard renders the admin dashboard
func (h *Handlers) AdminDashboard(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Admin Dashboard - Visual Reading Online",
		"Page":  "admin-dashboard",
	}
	return c.Render(http.StatusOK, "admin/dashboard.html", data)
}

// AdminUsers renders the user management page
func (h *Handlers) AdminUsers(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "User Management - Visual Reading Online",
		"Page":  "admin-users",
	}
	return c.Render(http.StatusOK, "admin/users.html", data)
}

// AdminDocuments renders the document management page
func (h *Handlers) AdminDocuments(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Document Management - Visual Reading Online",
		"Page":  "admin-documents",
	}
	return c.Render(http.StatusOK, "admin/documents.html", data)
}

// AdminDocumentUpload handles document upload
func (h *Handlers) AdminDocumentUpload(c echo.Context) error {
	// TODO: Implement document upload logic
	return c.Redirect(http.StatusSeeOther, "/admin/documents")
}

// AdminContacts renders the contact management page
func (h *Handlers) AdminContacts(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Contact Management - Visual Reading Online",
		"Page":  "admin-contacts",
	}
	return c.Render(http.StatusOK, "admin/contacts.html", data)
}

// AdminContactView renders a specific contact view
func (h *Handlers) AdminContactView(c echo.Context) error {
	contactID := c.Param("id")
	data := map[string]interface{}{
		"Title":     "Contact Details - Visual Reading Online",
		"Page":      "admin-contact-view",
		"ContactID": contactID,
	}
	return c.Render(http.StatusOK, "admin/contact-view.html", data)
}

// AdminContactReply handles contact reply submission
func (h *Handlers) AdminContactReply(c echo.Context) error {
	contactID := c.Param("id")
	// TODO: Implement contact reply logic
	return c.Redirect(http.StatusSeeOther, "/admin/contacts/"+contactID)
}
