package handlers

import (
	"net/http"

	"github.com/labstack/echo/v4"
)

// Home renders the home page
func (h *Handlers) Home(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Visual Reading Online - Transform Your Reading",
		"Page":  "home",
	}
	return c.Render(http.StatusOK, "pages/home.html", data)
}

// About renders the about page
func (h *Handlers) About(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "About Us - Visual Reading Online",
		"Page":  "about",
	}
	return c.Render(http.StatusOK, "pages/about.html", data)
}

// LearnMore renders the learn more page
func (h *Handlers) LearnMore(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Learn More - Visual Reading Online",
		"Page":  "learn-more",
	}
	return c.Render(http.StatusOK, "pages/learn-more.html", data)
}

// GetStarted renders the get started page
func (h *Handlers) GetStarted(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Get Started - Visual Reading Online",
		"Page":  "get-started",
	}
	return c.Render(http.StatusOK, "pages/get-started.html", data)
}

// Contact renders the contact page
func (h *Handlers) Contact(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Contact Us - Visual Reading Online",
		"Page":  "contact",
	}
	return c.Render(http.StatusOK, "pages/contact.html", data)
}

// ContactSubmit handles contact form submission
func (h *Handlers) ContactSubmit(c echo.Context) error {
	// TODO: Implement contact form submission
	return c.Redirect(http.StatusSeeOther, "/contact-success")
}

// ContactSuccess renders the contact success page
func (h *Handlers) ContactSuccess(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Thank You - Visual Reading Online",
		"Page":  "contact-success",
	}
	return c.Render(http.StatusOK, "pages/contact-success.html", data)
}

// Get Started sub-pages
func (h *Handlers) GetStartedPrivateStudents(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Private Students - Visual Reading Online",
		"Page":  "get-started-private-students",
	}
	return c.Render(http.StatusOK, "pages/get-started-private-students.html", data)
}

func (h *Handlers) GetStartedPrivateConsultation(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Private Consultation - Visual Reading Online",
		"Page":  "get-started-private-consultation",
	}
	return c.Render(http.StatusOK, "pages/get-started-private-consultation.html", data)
}

func (h *Handlers) GetStartedGift(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Gift a Course - Visual Reading Online",
		"Page":  "get-started-gift",
	}
	return c.Render(http.StatusOK, "pages/get-started-gift.html", data)
}

func (h *Handlers) GetStartedOrganisations(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Organisations - Visual Reading Online",
		"Page":  "get-started-organisations",
	}
	return c.Render(http.StatusOK, "pages/get-started-organisations.html", data)
}

func (h *Handlers) GetStartedCoaches(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Coaches - Visual Reading Online",
		"Page":  "get-started-coaches",
	}
	return c.Render(http.StatusOK, "pages/get-started-coaches.html", data)
}

func (h *Handlers) GetStartedDSAStudents(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "DSA Students - Visual Reading Online",
		"Page":  "get-started-dsa-students",
	}
	return c.Render(http.StatusOK, "pages/get-started-dsa-students.html", data)
}

// Resource pages
func (h *Handlers) DrCooper(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Dr. Cooper - Visual Reading Online",
		"Page":  "dr-cooper",
	}
	return c.Render(http.StatusOK, "pages/dr-cooper.html", data)
}

func (h *Handlers) Articles(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Articles - Visual Reading Online",
		"Page":  "articles",
	}
	return c.Render(http.StatusOK, "pages/articles.html", data)
}

func (h *Handlers) BlogPosts(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Blog Posts - Visual Reading Online",
		"Page":  "blog-posts",
	}
	return c.Render(http.StatusOK, "pages/blog-posts.html", data)
}

func (h *Handlers) FAQs(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "FAQs - Visual Reading Online",
		"Page":  "faqs",
	}
	return c.Render(http.StatusOK, "pages/faqs.html", data)
}

func (h *Handlers) OurResults(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Our Results - Visual Reading Online",
		"Page":  "our-results",
	}
	return c.Render(http.StatusOK, "pages/our-results.html", data)
}

func (h *Handlers) Products(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Products - Visual Reading Online",
		"Page":  "products",
	}
	return c.Render(http.StatusOK, "pages/products.html", data)
}

// Legal pages
func (h *Handlers) TermsOfService(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Terms of Service - Visual Reading Online",
		"Page":  "terms-of-service",
	}
	return c.Render(http.StatusOK, "pages/terms-of-service.html", data)
}

func (h *Handlers) PrivacyPolicy(c echo.Context) error {
	data := map[string]interface{}{
		"Title": "Privacy Policy - Visual Reading Online",
		"Page":  "privacy-policy",
	}
	return c.Render(http.StatusOK, "pages/privacy-policy.html", data)
}
