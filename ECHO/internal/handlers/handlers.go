package handlers

import (
	"visualreadingonline/internal/config"
	"visualreadingonline/internal/services"

	"gorm.io/gorm"
)

// Handlers holds all HTTP handlers and their dependencies
type Handlers struct {
	db       *gorm.DB
	config   *config.Config
	authSvc  *services.AuthService
	emailSvc *services.EmailService
	fileSvc  *services.FileService
}

// New creates a new Handlers instance with all dependencies
func New(db *gorm.DB, cfg *config.Config) *Handlers {
	return &Handlers{
		db:       db,
		config:   cfg,
		authSvc:  services.NewAuthService(db, cfg),
		emailSvc: services.NewEmailService(cfg),
		fileSvc:  services.NewFileService(),
	}
}
