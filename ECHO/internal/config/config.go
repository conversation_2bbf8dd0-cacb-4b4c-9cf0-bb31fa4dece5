package config

import (
	"fmt"
	"os"
	"strconv"
)

// Config holds all configuration for the application
type Config struct {
	// Server configuration
	Port        string
	Environment string
	Domain      string

	// Database configuration
	Database DatabaseConfig

	// Authentication configuration
	Auth AuthConfig

	// Session configuration
	SessionSecret string
}

// DatabaseConfig holds database connection details
type DatabaseConfig struct {
	Host     string
	Port     int
	Database string
	Username string
	Password string
	SSLMode  string
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	GoogleClientID     string
	GoogleClientSecret string
	MagicLinkSecret    string
}

// Load loads configuration from environment variables with sensible defaults
func Load() *Config {
	return &Config{
		Port:        getEnv("PORT", "8000"),
		Environment: getEnv("GO_ENV", "development"),
		Domain:      getEnv("DOMAIN", "https://visualreadingonline.com"),

		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "**********"),
			Port:     getEnvAsInt("DB_PORT", 5432),
			Database: getEnv("DB_NAME", "VRO"),
			Username: getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", "postgres"),
			SSLMode:  getEnv("DB_SSL_MODE", "disable"),
		},

		Auth: AuthConfig{
			GoogleClientID:     getEnv("GOOGLE_CLIENT_ID", ""),
			GoogleClientSecret: getEnv("GOOGLE_CLIENT_SECRET", ""),
			MagicLinkSecret:    getEnv("MAGIC_LINK_SECRET", "your-magic-link-secret-key"),
		},

		SessionSecret: getEnv("SESSION_SECRET", "your-session-secret-key-change-in-production"),
	}
}

// DatabaseURL returns the database connection string
func (c *Config) DatabaseURL() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Database.Host,
		c.Database.Port,
		c.Database.Username,
		c.Database.Password,
		c.Database.Database,
		c.Database.SSLMode,
	)
}

// IsDevelopment returns true if running in development mode
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

// IsProduction returns true if running in production mode
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}

// getEnvAsInt gets an environment variable as integer with a fallback value
func getEnvAsInt(key string, fallback int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return fallback
}
