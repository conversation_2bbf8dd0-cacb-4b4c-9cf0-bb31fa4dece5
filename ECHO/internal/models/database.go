package models

import (
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// InitDB initializes the database connection
func InitDB(databaseURL string) (*gorm.DB, error) {
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	return db, nil
}

// Migra<PERSON> runs all database migrations
func Migrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&User{},
		&UserProfile{},
		&UserGroup{},
		&MagicLink{},
		&Document{},
		&DocumentCategory{},
		&Contact{},
		&Page{},
	)
}
