package models

import (
	"time"

	"gorm.io/gorm"
)

// User represents a user account
type User struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Email     string    `json:"email" gorm:"uniqueIndex;not null"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Provider  string    `json:"provider" gorm:"default:'email'"` // email, google
	ProviderID string   `json:"provider_id"`
	IsActive  bool      `json:"is_active" gorm:"default:true"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	Profile   *UserProfile `json:"profile,omitempty" gorm:"foreignKey:UserID"`
	MagicLinks []MagicLink `json:"-" gorm:"foreignKey:UserID"`
}

// UserGroup represents user groups for role-based access
type UserGroup struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"not null"`
	Code        string `json:"code" gorm:"uniqueIndex;not null"` // site-admins, members, etc.
	Description string `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// Relationships
	Users []UserProfile `json:"users,omitempty" gorm:"foreignKey:GroupID"`
}

// UserProfile represents detailed user profile information
type UserProfile struct {
	ID        uint   `json:"id" gorm:"primaryKey"`
	UserID    uint   `json:"user_id" gorm:"uniqueIndex;not null"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Age       *int   `json:"age"`
	Gender    string `json:"gender"`
	Race      string `json:"race"`
	Mobile    string `json:"mobile"`
	Landline  string `json:"landline"`
	
	// Address information
	AddressLine1 string `json:"address_line1"`
	AddressLine2 string `json:"address_line2"`
	Postcode     string `json:"postcode"`
	
	// Profile completion
	IsCompleted   bool       `json:"is_completed" gorm:"default:false"`
	CompletedAt   *time.Time `json:"completed_at"`
	
	// Group assignment
	GroupID   *uint      `json:"group_id"`
	Group     *UserGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	
	// Conditions and notes
	Conditions      string `json:"conditions"`      // JSON array of conditions
	HasNoConditions bool   `json:"has_no_conditions" gorm:"default:true"`
	Notes           string `json:"notes"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// MagicLink represents magic link tokens for passwordless authentication
type MagicLink struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"not null"`
	Token     string    `json:"token" gorm:"uniqueIndex;not null"`
	ExpiresAt time.Time `json:"expires_at" gorm:"not null"`
	Used      bool      `json:"used" gorm:"default:false"`
	CreatedAt time.Time `json:"created_at"`

	// Relationships
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// IsExpired checks if the magic link has expired
func (ml *MagicLink) IsExpired() bool {
	return time.Now().After(ml.ExpiresAt)
}

// IsValid checks if the magic link is valid (not used and not expired)
func (ml *MagicLink) IsValid() bool {
	return !ml.Used && !ml.IsExpired()
}

// FullName returns the user's full name
func (u *User) FullName() string {
	if u.FirstName != "" && u.LastName != "" {
		return u.FirstName + " " + u.LastName
	}
	if u.FirstName != "" {
		return u.FirstName
	}
	if u.LastName != "" {
		return u.LastName
	}
	return u.Email
}

// IsAdmin checks if the user is an admin
func (u *User) IsAdmin() bool {
	if u.Profile != nil && u.Profile.Group != nil {
		return u.Profile.Group.Code == "site-admins"
	}
	return false
}

// HasCompletedProfile checks if the user has completed their profile
func (u *User) HasCompletedProfile() bool {
	return u.Profile != nil && u.Profile.IsCompleted
}

// BeforeCreate hook to set default values
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.Provider == "" {
		u.Provider = "email"
	}
	return nil
}

// FullName returns the profile owner's full name
func (up *UserProfile) FullName() string {
	if up.FirstName != "" && up.LastName != "" {
		return up.FirstName + " " + up.LastName
	}
	return up.User.FullName()
}
