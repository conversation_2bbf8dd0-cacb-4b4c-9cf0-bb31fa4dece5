package models

import (
	"time"
)

// DocumentCategory represents categories for documents (Articles, Blogs, Pages, etc.)
type DocumentCategory struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"uniqueIndex;not null"`
	Description string `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// Relationships
	Documents []Document `json:"documents,omitempty" gorm:"foreignKey:CategoryID"`
}

// Document represents uploaded documents (DOCX files, articles, blogs, etc.)
type Document struct {
	ID               uint      `json:"id" gorm:"primaryKey"`
	Title            string    `json:"title" gorm:"not null"`
	Slug             string    `json:"slug" gorm:"uniqueIndex;not null"`
	CategoryID       uint      `json:"category_id" gorm:"not null"`
	FilePath         string    `json:"file_path"`
	OriginalFilename string    `json:"original_filename"`
	Author           string    `json:"author"`
	Description      string    `json:"description"`
	Content          string    `json:"content" gorm:"type:text"` // Rendered HTML content
	IsPublished      bool      `json:"is_published" gorm:"default:false"`
	PublishedAt      *time.Time `json:"published_at"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	CreatedByID      *uint     `json:"created_by_id"`

	// Relationships
	Category  DocumentCategory `json:"category" gorm:"foreignKey:CategoryID"`
	CreatedBy *User           `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
}

// Contact represents contact form submissions
type Contact struct {
	ID           uint       `json:"id" gorm:"primaryKey"`
	FirstName    string     `json:"first_name" gorm:"not null"`
	LastName     string     `json:"last_name" gorm:"not null"`
	Email        string     `json:"email" gorm:"not null"`
	Organisation string     `json:"organisation"`
	Subject      string     `json:"subject" gorm:"not null"`
	Message      string     `json:"message" gorm:"type:text;not null"`
	IsRead       bool       `json:"is_read" gorm:"default:false"`
	ReadAt       *time.Time `json:"read_at"`
	IsReplied    bool       `json:"is_replied" gorm:"default:false"`
	RepliedAt    *time.Time `json:"replied_at"`
	ReplyMessage string     `json:"reply_message" gorm:"type:text"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// Page represents static pages with dynamic content
type Page struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"uniqueIndex;not null"`
	Title       string    `json:"title"`
	Content     string    `json:"content" gorm:"type:text"`
	FilePath    string    `json:"file_path"` // Path to uploaded document
	IsPublished bool      `json:"is_published" gorm:"default:false"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// FullName returns the contact's full name
func (c *Contact) FullName() string {
	return c.FirstName + " " + c.LastName
}

// MarkAsRead marks the contact as read
func (c *Contact) MarkAsRead() {
	if !c.IsRead {
		now := time.Now()
		c.IsRead = true
		c.ReadAt = &now
	}
}

// MarkAsReplied marks the contact as replied with the given message
func (c *Contact) MarkAsReplied(replyMessage string) {
	now := time.Now()
	c.IsReplied = true
	c.RepliedAt = &now
	c.ReplyMessage = replyMessage
	c.MarkAsRead() // Also mark as read when replying
}
