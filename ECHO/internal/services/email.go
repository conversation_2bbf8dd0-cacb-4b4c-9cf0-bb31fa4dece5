package services

import (
	"fmt"
	"log"

	"visualreadingonline/internal/config"
)

// EmailService handles email sending operations
type EmailService struct {
	config *config.Config
}

// NewEmailService creates a new EmailService
func NewEmailService(cfg *config.Config) *EmailService {
	return &EmailService{
		config: cfg,
	}
}

// SendMagicLink sends a magic link email to the user
func (s *EmailService) SendMagicLink(email, token string) error {
	magicLinkURL := fmt.Sprintf("%s/auth/magic-link/%s", s.config.Domain, token)
	
	// For now, just log the magic link (in production, use a real email service)
	log.Printf("Magic link for %s: %s", email, magicLinkURL)
	
	// TODO: Implement actual email sending using SendGrid, AWS SES, or similar
	// Example with a hypothetical email service:
	/*
	subject := "Your login link for Visual Reading Online"
	body := fmt.Sprintf(`
		<h2>Login to Visual Reading Online</h2>
		<p>Click the link below to log in to your account:</p>
		<p><a href="%s">Login to Visual Reading Online</a></p>
		<p>This link will expire in 15 minutes.</p>
		<p>If you didn't request this login link, please ignore this email.</p>
	`, magicLinkURL)
	
	return s.sendEmail(email, subject, body)
	*/
	
	return nil
}

// SendWelcomeEmail sends a welcome email to new users
func (s *EmailService) SendWelcomeEmail(email, firstName string) error {
	// For now, just log the welcome email
	log.Printf("Welcome email for %s (%s)", firstName, email)
	
	// TODO: Implement actual email sending
	return nil
}

// SendContactReply sends a reply to a contact form submission
func (s *EmailService) SendContactReply(email, subject, replyMessage string) error {
	// For now, just log the reply
	log.Printf("Contact reply to %s - Subject: %s", email, subject)
	log.Printf("Reply: %s", replyMessage)
	
	// TODO: Implement actual email sending
	return nil
}

// sendEmail is a placeholder for actual email sending implementation
func (s *EmailService) sendEmail(to, subject, body string) error {
	// This would be implemented with your chosen email service
	// Examples:
	// - SendGrid: github.com/sendgrid/sendgrid-go
	// - AWS SES: github.com/aws/aws-sdk-go-v2/service/ses
	// - Mailgun: github.com/mailgun/mailgun-go/v4
	
	log.Printf("Email sent to %s: %s", to, subject)
	return nil
}
