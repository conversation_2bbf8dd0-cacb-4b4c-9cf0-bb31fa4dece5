package services

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"visualreadingonline/internal/config"
	"visualreadingonline/internal/models"

	"gorm.io/gorm"
)

// AuthService handles authentication-related operations
type AuthService struct {
	db     *gorm.DB
	config *config.Config
}

// NewAuthService creates a new AuthService
func NewAuthService(db *gorm.DB, cfg *config.Config) *AuthService {
	return &AuthService{
		db:     db,
		config: cfg,
	}
}

// Create<PERSON>ser creates a new user account
func (s *AuthService) CreateUser(email, firstName, lastName, provider, providerID string) (*models.User, error) {
	user := &models.User{
		Email:      email,
		FirstName:  firstName,
		LastName:   lastName,
		Provider:   provider,
		ProviderID: providerID,
		IsActive:   true,
	}

	if err := s.db.Create(user).Error; err != nil {
		return nil, err
	}

	// Create empty profile
	profile := &models.UserProfile{
		UserID:    user.ID,
		FirstName: firstName,
		LastName:  lastName,
	}

	if err := s.db.Create(profile).Error; err != nil {
		return nil, err
	}

	// Reload user with profile
	if err := s.db.Preload("Profile.Group").First(user, user.ID).Error; err != nil {
		return nil, err
	}

	return user, nil
}

// FindUserByEmail finds a user by email address
func (s *AuthService) FindUserByEmail(email string) (*models.User, error) {
	var user models.User
	err := s.db.Preload("Profile.Group").Where("email = ?", email).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// FindUserByProviderID finds a user by provider and provider ID
func (s *AuthService) FindUserByProviderID(provider, providerID string) (*models.User, error) {
	var user models.User
	err := s.db.Preload("Profile.Group").Where("provider = ? AND provider_id = ?", provider, providerID).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// CreateMagicLink creates a magic link for passwordless authentication
func (s *AuthService) CreateMagicLink(userID uint) (*models.MagicLink, error) {
	// Generate random token
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return nil, err
	}
	token := hex.EncodeToString(tokenBytes)

	// Create magic link with 15-minute expiration
	magicLink := &models.MagicLink{
		UserID:    userID,
		Token:     token,
		ExpiresAt: time.Now().Add(15 * time.Minute),
		Used:      false,
	}

	if err := s.db.Create(magicLink).Error; err != nil {
		return nil, err
	}

	return magicLink, nil
}

// ValidateMagicLink validates and consumes a magic link token
func (s *AuthService) ValidateMagicLink(token string) (*models.User, error) {
	var magicLink models.MagicLink
	err := s.db.Preload("User.Profile.Group").Where("token = ?", token).First(&magicLink).Error
	if err != nil {
		return nil, fmt.Errorf("invalid magic link")
	}

	if !magicLink.IsValid() {
		return nil, fmt.Errorf("magic link expired or already used")
	}

	// Mark as used
	magicLink.Used = true
	if err := s.db.Save(&magicLink).Error; err != nil {
		return nil, err
	}

	return &magicLink.User, nil
}

// CleanupExpiredMagicLinks removes expired magic links from the database
func (s *AuthService) CleanupExpiredMagicLinks() error {
	return s.db.Where("expires_at < ?", time.Now()).Delete(&models.MagicLink{}).Error
}

// GetOrCreateUserGroup gets or creates a user group by code
func (s *AuthService) GetOrCreateUserGroup(code, name, description string) (*models.UserGroup, error) {
	var group models.UserGroup
	err := s.db.Where("code = ?", code).First(&group).Error
	if err == nil {
		return &group, nil
	}

	if err != gorm.ErrRecordNotFound {
		return nil, err
	}

	// Create new group
	group = models.UserGroup{
		Code:        code,
		Name:        name,
		Description: description,
	}

	if err := s.db.Create(&group).Error; err != nil {
		return nil, err
	}

	return &group, nil
}

// AssignUserToGroup assigns a user to a group
func (s *AuthService) AssignUserToGroup(userID uint, groupCode string) error {
	var group models.UserGroup
	if err := s.db.Where("code = ?", groupCode).First(&group).Error; err != nil {
		return err
	}

	return s.db.Model(&models.UserProfile{}).Where("user_id = ?", userID).Update("group_id", group.ID).Error
}

// UpdateUserProfile updates user profile information
func (s *AuthService) UpdateUserProfile(userID uint, updates map[string]interface{}) error {
	return s.db.Model(&models.UserProfile{}).Where("user_id = ?", userID).Updates(updates).Error
}

// CompleteUserProfile marks a user's profile as completed
func (s *AuthService) CompleteUserProfile(userID uint) error {
	now := time.Now()
	updates := map[string]interface{}{
		"is_completed": true,
		"completed_at": &now,
	}
	return s.UpdateUserProfile(userID, updates)
}
