package services

import (
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// FileService handles file upload and processing operations
type FileService struct {
	uploadDir string
}

// NewFileService creates a new FileService
func NewFileService() *FileService {
	return &FileService{
		uploadDir: "uploads",
	}
}

// UploadFile uploads a file and returns the file path
func (s *FileService) UploadFile(file *multipart.FileHeader, category string) (string, error) {
	// Create upload directory if it doesn't exist
	categoryDir := filepath.Join(s.uploadDir, category)
	if err := os.MkdirAll(categoryDir, 0755); err != nil {
		return "", err
	}

	// Generate unique filename
	ext := filepath.Ext(file.Filename)
	filename := fmt.Sprintf("%d_%s%s", time.Now().Unix(), strings.TrimSuffix(file.Filename, ext), ext)
	filePath := filepath.Join(categoryDir, filename)

	// Open uploaded file
	src, err := file.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()

	// Create destination file
	dst, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer dst.Close()

	// Copy file content
	if _, err := io.Copy(dst, src); err != nil {
		return "", err
	}

	return filePath, nil
}

// DeleteFile deletes a file from the filesystem
func (s *FileService) DeleteFile(filePath string) error {
	if filePath == "" {
		return nil
	}
	return os.Remove(filePath)
}

// ProcessDOCXFile processes a DOCX file and extracts content
func (s *FileService) ProcessDOCXFile(filePath string) (string, error) {
	// TODO: Implement DOCX processing using a library like:
	// - github.com/unidoc/unioffice
	// - github.com/nguyenthenguyen/docx
	
	// For now, return a placeholder
	return "<p>Document content will be processed here</p>", nil
}

// IsValidFileType checks if the file type is allowed
func (s *FileService) IsValidFileType(filename string, allowedTypes []string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	for _, allowedType := range allowedTypes {
		if ext == allowedType {
			return true
		}
	}
	return false
}

// GetFileSize returns the size of a file in bytes
func (s *FileService) GetFileSize(filePath string) (int64, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}
