package main

import (
	"context"
	"fmt"
	"html/template"
	"io"
	"log"
	"net/http"
	"os"
	"os/signal"
	"time"

	"visualreadingonline/internal/config"
	"visualreadingonline/internal/handlers"
	"visualreadingonline/internal/middleware"
	"visualreadingonline/internal/models"

	"github.com/labstack/echo/v4"
	echomiddleware "github.com/labstack/echo/v4/middleware"
)

// TemplateRenderer is a custom html/template renderer for Echo framework
type TemplateRenderer struct {
	templates *template.Template
}

// <PERSON><PERSON> renders a template document
func (t *TemplateRenderer) Render(w io.Writer, name string, data interface{}, c echo.Context) error {
	// Add global template data
	if viewContext, isMap := data.(map[string]interface{}); isMap {
		viewContext["IsAuthenticated"] = c.Get("user") != nil
		viewContext["User"] = c.Get("user")
		viewContext["Config"] = c.Get("config")
	}

	// Use the full path as template name
	// e.g., "pages/home.html" stays as "templates/pages/home.html"
	templateName := "templates/" + name

	return t.templates.ExecuteTemplate(w, templateName, data)
}

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := models.InitDB(cfg.DatabaseURL())
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Run migrations
	if err := models.Migrate(db); err != nil {
		log.Fatalf("Failed to run migrations: %v", err)
	}

	// Initialize Echo
	e := echo.New()

	// Custom template renderer - create a map to store individual template instances
	templateMap := make(map[string]*template.Template)

	// Load base templates (layouts and components) that will be shared
	baseTemplates := []string{
		"templates/layouts/base.html",
		"templates/components/footer.html",
		"templates/components/public_header.html",
		"templates/components/user_header.html",
		"templates/components/admin_header.html",
	}

	// Load page templates individually to avoid conflicts
	pageTemplates := []string{
		"templates/pages/home.html",
		"templates/pages/contact.html",
		"templates/pages/contact-success.html",
		"templates/pages/learn-more.html",
		"templates/auth/login.html",
		"templates/auth/register.html",
		"templates/auth/magic-link.html",
		"templates/app/dashboard.html",
		"templates/app/complete-profile.html",
	}

	// Create individual template instances for each page
	for _, pageTemplate := range pageTemplates {
		// Start with base templates
		tmpl := template.New(pageTemplate)

		// Parse base templates first
		for _, baseTemplate := range baseTemplates {
			content, err := os.ReadFile(baseTemplate)
			if err != nil {
				log.Fatalf("Failed to read base template %s: %v", baseTemplate, err)
			}
			_, err = tmpl.New(baseTemplate).Parse(string(content))
			if err != nil {
				log.Fatalf("Failed to parse base template %s: %v", baseTemplate, err)
			}
		}

		// Parse the page template
		content, err := os.ReadFile(pageTemplate)
		if err != nil {
			log.Fatalf("Failed to read page template %s: %v", pageTemplate, err)
		}
		_, err = tmpl.New(pageTemplate).Parse(string(content))
		if err != nil {
			log.Fatalf("Failed to parse page template %s: %v", pageTemplate, err)
		}

		templateMap[pageTemplate] = tmpl
	}

	// Debug: Print loaded template names
	log.Println("Loaded templates:")
	for name := range templateMap {
		log.Printf("  - %s", name)
	}

	renderer := &TemplateRenderer{
		templates: templates,
	}
	e.Renderer = renderer

	// Middleware
	e.Use(echomiddleware.Logger())
	e.Use(echomiddleware.Recover())
	e.Use(echomiddleware.CORS())

	// Custom middleware
	e.Use(middleware.Config(cfg))
	e.Use(middleware.Database(db))
	e.Use(middleware.Session(cfg.SessionSecret))
	e.Use(middleware.Auth())

	// CSRF protection (disable in development for easier testing)
	if cfg.IsProduction() {
		e.Use(echomiddleware.CSRF())
	}

	// Static files
	e.Static("/static", "static")

	// Initialize handlers
	h := handlers.New(db, cfg)

	// Routes
	setupRoutes(e, h)

	// Start server
	go func() {
		address := fmt.Sprintf(":%s", cfg.Port)
		log.Printf("Starting server on %s", address)
		if err := e.Start(address); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit

	log.Println("Shutting down server...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := e.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}

// setupRoutes configures all application routes
func setupRoutes(e *echo.Echo, h *handlers.Handlers) {
	// Public routes
	e.GET("/", h.Home)
	e.GET("/about", h.About)
	e.GET("/learn-more", h.LearnMore)
	e.GET("/get-started", h.GetStarted)
	e.GET("/contact", h.Contact)
	e.POST("/contact", h.ContactSubmit)
	e.GET("/contact-success", h.ContactSuccess)

	// Get Started sub-pages
	getStarted := e.Group("/get-started")
	getStarted.GET("/private-students", h.GetStartedPrivateStudents)
	getStarted.GET("/private-consultation", h.GetStartedPrivateConsultation)
	getStarted.GET("/gift-a-visual-reading-course", h.GetStartedGift)
	getStarted.GET("/organisations", h.GetStartedOrganisations)
	getStarted.GET("/coaches", h.GetStartedCoaches)
	getStarted.GET("/dsa-students", h.GetStartedDSAStudents)

	// Resources
	e.GET("/dr-cooper", h.DrCooper)
	e.GET("/articles", h.Articles)
	e.GET("/blog-posts", h.BlogPosts)
	e.GET("/faqs", h.FAQs)
	e.GET("/our-results", h.OurResults)
	e.GET("/products", h.Products)

	// Legal
	e.GET("/terms-of-service", h.TermsOfService)
	e.GET("/privacy-policy", h.PrivacyPolicy)

	// Authentication routes
	auth := e.Group("/auth")
	auth.GET("/login", h.Login)
	auth.POST("/login", h.LoginSubmit)
	auth.GET("/register", h.Register)
	auth.POST("/register", h.RegisterSubmit)
	auth.GET("/logout", h.Logout)
	auth.GET("/magic-link", h.MagicLinkRequest)
	auth.POST("/magic-link", h.MagicLinkSubmit)
	auth.GET("/magic-link/:token", h.MagicLinkVerify)
	auth.GET("/google", h.GoogleAuth)
	auth.GET("/google/callback", h.GoogleCallback)

	// Protected routes (require authentication)
	protected := e.Group("/app")
	protected.Use(middleware.RequireAuth())
	protected.GET("", h.Dashboard)
	protected.GET("/profile", h.Profile)
	protected.POST("/profile", h.ProfileUpdate)
	protected.GET("/complete-profile", h.CompleteProfile)
	protected.POST("/complete-profile", h.CompleteProfileSubmit)

	// Admin routes (require admin role)
	admin := e.Group("/admin")
	admin.Use(middleware.RequireAuth())
	admin.Use(middleware.RequireAdmin())
	admin.GET("", h.AdminDashboard)
	admin.GET("/users", h.AdminUsers)
	admin.GET("/documents", h.AdminDocuments)
	admin.POST("/documents", h.AdminDocumentUpload)
	admin.GET("/contacts", h.AdminContacts)
	admin.GET("/contacts/:id", h.AdminContactView)
	admin.POST("/contacts/:id/reply", h.AdminContactReply)
}
