{{template "base.html" .}}

{{define "content"}}
<div class="max-w-4xl mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-blue-900 mb-8 text-center">Contact Us</h1>

    {{if .Error}}
    <div class="alert alert-error mb-6">
        <span>{{.Error}}</span>
        <button class="alert-close ml-4 text-lg">&times;</button>
    </div>
    {{end}}

    <div class="grid md:grid-cols-2 gap-8">
        <div>
            <h2 class="text-xl font-bold text-blue-900 mb-4">Get in Touch</h2>
            <p class="text-blue-800 mb-6">
                We'd love to hear from you. Send us a message and we'll respond as soon as possible.
            </p>
            
            <form method="POST" action="/contact" class="space-y-4">
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-blue-900 mb-2">First Name</label>
                        <input type="text" id="first_name" name="first_name" required class="form-input"
                               value="{{if .FormData}}{{.FormData.first_name}}{{end}}">
                    </div>
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-blue-900 mb-2">Last Name</label>
                        <input type="text" id="last_name" name="last_name" required class="form-input"
                               value="{{if .FormData}}{{.FormData.last_name}}{{end}}">
                    </div>
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-blue-900 mb-2">Email</label>
                    <input type="email" id="email" name="email" required class="form-input"
                           value="{{if .FormData}}{{.FormData.email}}{{end}}">
                </div>

                <div>
                    <label for="organisation" class="block text-sm font-medium text-blue-900 mb-2">Organisation (Optional)</label>
                    <input type="text" id="organisation" name="organisation" class="form-input"
                           value="{{if .FormData}}{{.FormData.organisation}}{{end}}">
                </div>

                <div>
                    <label for="subject" class="block text-sm font-medium text-blue-900 mb-2">Subject</label>
                    <input type="text" id="subject" name="subject" required class="form-input"
                           value="{{if .FormData}}{{.FormData.subject}}{{end}}">
                </div>

                <div>
                    <label for="message" class="block text-sm font-medium text-blue-900 mb-2">Message</label>
                    <textarea id="message" name="message" rows="5" required class="form-input">{{if .FormData}}{{.FormData.message}}{{end}}</textarea>
                </div>
                
                <button type="submit" class="btn-primary">
                    Send Message
                </button>
            </form>
        </div>
        
        <div>
            <h2 class="text-xl font-bold text-blue-900 mb-4">Contact Information</h2>
            <div class="space-y-4">
                <div>
                    <h3 class="font-semibold text-blue-900">Email</h3>
                    <p class="text-blue-800"><EMAIL></p>
                </div>
                <div>
                    <h3 class="font-semibold text-blue-900">Support</h3>
                    <p class="text-blue-800">We typically respond within 24 hours</p>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}
