{{template "base.html" .}}

{{define "content"}}
<div class="max-w-screen-xl mx-auto px-4 py-8">
    <!-- Welcome Section -->
    <div class="bg-white p-6 rounded-lg shadow-lg border border-blue-200 mb-8">
        <h1 class="text-3xl font-bold text-blue-900 mb-4">
            Welcome back, {{.User.FullName}}!
        </h1>
        <p class="text-blue-800">
            {{if eq .DashboardType "admin"}}
                You have administrator access to Visual Reading Online.
            {{else}}
                Welcome to your Visual Reading Online dashboard.
            {{end}}
        </p>
    </div>

    <!-- Quick Actions -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {{if eq .DashboardType "admin"}}
        <!-- Admin Actions -->
        <a href="/admin" class="card p-6 hover:shadow-lg transition-shadow">
            <div class="text-blue-900 mb-4">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-blue-900 mb-2">Admin Dashboard</h3>
            <p class="text-blue-800 text-sm">Manage users, content, and system settings</p>
        </a>

        <a href="/admin/users" class="card p-6 hover:shadow-lg transition-shadow">
            <div class="text-blue-900 mb-4">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-blue-900 mb-2">User Management</h3>
            <p class="text-blue-800 text-sm">View and manage user accounts</p>
        </a>

        <a href="/admin/documents" class="card p-6 hover:shadow-lg transition-shadow">
            <div class="text-blue-900 mb-4">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-blue-900 mb-2">Document Management</h3>
            <p class="text-blue-800 text-sm">Upload and manage documents</p>
        </a>

        <a href="/admin/contacts" class="card p-6 hover:shadow-lg transition-shadow">
            <div class="text-blue-900 mb-4">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-blue-900 mb-2">Contact Management</h3>
            <p class="text-blue-800 text-sm">View and respond to contact messages</p>
        </a>
        {{else}}
        <!-- Member Actions -->
        <a href="/app/profile" class="card p-6 hover:shadow-lg transition-shadow">
            <div class="text-blue-900 mb-4">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-blue-900 mb-2">Your Profile</h3>
            <p class="text-blue-800 text-sm">View and update your profile information</p>
        </a>

        <a href="/articles" class="card p-6 hover:shadow-lg transition-shadow">
            <div class="text-blue-900 mb-4">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-blue-900 mb-2">Resources</h3>
            <p class="text-blue-800 text-sm">Access articles and learning materials</p>
        </a>

        <a href="/contact" class="card p-6 hover:shadow-lg transition-shadow">
            <div class="text-blue-900 mb-4">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-blue-900 mb-2">Support</h3>
            <p class="text-blue-800 text-sm">Get help and contact our team</p>
        </a>
        {{end}}
    </div>

    <!-- Profile Status -->
    {{if not .User.HasCompletedProfile}}
    <div class="bg-yellow-50 border border-yellow-200 p-6 rounded-lg mb-8">
        <div class="flex items-start">
            <div class="text-yellow-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-bold text-yellow-800 mb-2">Complete Your Profile</h3>
                <p class="text-yellow-700 mb-4">
                    Please complete your profile to access all features and get the most out of Visual Reading Online.
                </p>
                <a href="/app/complete-profile" class="bg-yellow-600 text-white px-4 py-2 rounded-lg font-bold hover:bg-yellow-700 transition-colors">
                    Complete Profile
                </a>
            </div>
        </div>
    </div>
    {{end}}

    <!-- Recent Activity or Information -->
    <div class="bg-white p-6 rounded-lg shadow-lg border border-blue-200">
        <h2 class="text-xl font-bold text-blue-900 mb-4">Getting Started</h2>
        <div class="space-y-4">
            <div class="flex items-start">
                <div class="text-green-500 mr-3 mt-1">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="font-semibold text-blue-900">Account Created</h3>
                    <p class="text-blue-800 text-sm">Welcome to Visual Reading Online!</p>
                </div>
            </div>
            
            {{if .User.HasCompletedProfile}}
            <div class="flex items-start">
                <div class="text-green-500 mr-3 mt-1">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="font-semibold text-blue-900">Profile Completed</h3>
                    <p class="text-blue-800 text-sm">Your profile information has been saved.</p>
                </div>
            </div>
            {{else}}
            <div class="flex items-start">
                <div class="text-yellow-500 mr-3 mt-1">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="font-semibold text-blue-900">Complete Your Profile</h3>
                    <p class="text-blue-800 text-sm">Add your details to get personalized recommendations.</p>
                </div>
            </div>
            {{end}}
            
            <div class="flex items-start">
                <div class="text-blue-500 mr-3 mt-1">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="font-semibold text-blue-900">Explore Resources</h3>
                    <p class="text-blue-800 text-sm">Check out our articles and learning materials.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}
