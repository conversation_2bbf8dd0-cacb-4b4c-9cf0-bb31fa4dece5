{{template "templates/layouts/base.html" .}}

{{define "content"}}
<div class="max-w-2xl mx-auto px-4 py-8">
    <div class="bg-white p-8 rounded-lg shadow-lg border border-blue-200">
        <h1 class="text-2xl font-bold text-blue-900 mb-6 text-center">Complete Your Profile</h1>
        
        <p class="text-blue-800 mb-6 text-center">
            Please provide some additional information to help us personalize your experience.
        </p>
        
        {{if .Error}}
        <div class="alert alert-error mb-6">
            <span>{{.Error}}</span>
            <button class="alert-close ml-4 text-lg">&times;</button>
        </div>
        {{end}}
        
        <form method="POST" action="/app/complete-profile" class="space-y-6">
            <!-- Personal Information -->
            <div class="border-b border-gray-200 pb-6">
                <h2 class="text-lg font-semibold text-blue-900 mb-4">Personal Information</h2>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-blue-900 mb-2">First Name *</label>
                        <input type="text" id="first_name" name="first_name" required 
                               class="form-input" 
                               value="{{if .FormData}}{{.FormData.first_name}}{{else if .User.FirstName}}{{.User.FirstName}}{{end}}">
                    </div>
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-blue-900 mb-2">Last Name *</label>
                        <input type="text" id="last_name" name="last_name" required 
                               class="form-input"
                               value="{{if .FormData}}{{.FormData.last_name}}{{else if .User.LastName}}{{.User.LastName}}{{end}}">
                    </div>
                </div>
                
                <div class="grid md:grid-cols-3 gap-4 mt-4">
                    <div>
                        <label for="age" class="block text-sm font-medium text-blue-900 mb-2">Age</label>
                        <input type="number" id="age" name="age" min="1" max="120"
                               class="form-input" 
                               value="{{if .FormData}}{{.FormData.age}}{{end}}">
                    </div>
                    <div>
                        <label for="gender" class="block text-sm font-medium text-blue-900 mb-2">Gender</label>
                        <select id="gender" name="gender" class="form-input">
                            <option value="">Select...</option>
                            <option value="male" {{if and .FormData (eq .FormData.gender "male")}}selected{{end}}>Male</option>
                            <option value="female" {{if and .FormData (eq .FormData.gender "female")}}selected{{end}}>Female</option>
                            <option value="other" {{if and .FormData (eq .FormData.gender "other")}}selected{{end}}>Other</option>
                            <option value="prefer_not_to_say" {{if and .FormData (eq .FormData.gender "prefer_not_to_say")}}selected{{end}}>Prefer not to say</option>
                        </select>
                    </div>
                    <div>
                        <label for="race" class="block text-sm font-medium text-blue-900 mb-2">Ethnicity</label>
                        <select id="race" name="race" class="form-input">
                            <option value="">Select...</option>
                            <option value="white" {{if and .FormData (eq .FormData.race "white")}}selected{{end}}>White</option>
                            <option value="black" {{if and .FormData (eq .FormData.race "black")}}selected{{end}}>Black or African American</option>
                            <option value="asian" {{if and .FormData (eq .FormData.race "asian")}}selected{{end}}>Asian</option>
                            <option value="hispanic" {{if and .FormData (eq .FormData.race "hispanic")}}selected{{end}}>Hispanic or Latino</option>
                            <option value="native" {{if and .FormData (eq .FormData.race "native")}}selected{{end}}>Native American</option>
                            <option value="pacific" {{if and .FormData (eq .FormData.race "pacific")}}selected{{end}}>Pacific Islander</option>
                            <option value="mixed" {{if and .FormData (eq .FormData.race "mixed")}}selected{{end}}>Mixed Race</option>
                            <option value="other" {{if and .FormData (eq .FormData.race "other")}}selected{{end}}>Other</option>
                            <option value="prefer_not_to_say" {{if and .FormData (eq .FormData.race "prefer_not_to_say")}}selected{{end}}>Prefer not to say</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="border-b border-gray-200 pb-6">
                <h2 class="text-lg font-semibold text-blue-900 mb-4">Contact Information</h2>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label for="mobile" class="block text-sm font-medium text-blue-900 mb-2">Mobile Phone</label>
                        <input type="tel" id="mobile" name="mobile" 
                               class="form-input" 
                               value="{{if .FormData}}{{.FormData.mobile}}{{end}}">
                    </div>
                    <div>
                        <label for="landline" class="block text-sm font-medium text-blue-900 mb-2">Landline</label>
                        <input type="tel" id="landline" name="landline" 
                               class="form-input"
                               value="{{if .FormData}}{{.FormData.landline}}{{end}}">
                    </div>
                </div>
            </div>
            
            <!-- Address Information -->
            <div class="border-b border-gray-200 pb-6">
                <h2 class="text-lg font-semibold text-blue-900 mb-4">Address</h2>
                
                <div class="space-y-4">
                    <div>
                        <label for="address_line1" class="block text-sm font-medium text-blue-900 mb-2">Address Line 1 *</label>
                        <input type="text" id="address_line1" name="address_line1" required 
                               class="form-input" 
                               value="{{if .FormData}}{{.FormData.address_line1}}{{end}}">
                    </div>
                    <div>
                        <label for="address_line2" class="block text-sm font-medium text-blue-900 mb-2">Address Line 2</label>
                        <input type="text" id="address_line2" name="address_line2" 
                               class="form-input"
                               value="{{if .FormData}}{{.FormData.address_line2}}{{end}}">
                    </div>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <label for="postcode" class="block text-sm font-medium text-blue-900 mb-2">Postcode *</label>
                            <input type="text" id="postcode" name="postcode" required 
                                   class="form-input"
                                   value="{{if .FormData}}{{.FormData.postcode}}{{end}}">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Conditions -->
            <div>
                <h2 class="text-lg font-semibold text-blue-900 mb-4">Learning Conditions</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="has_no_conditions" name="has_no_conditions" 
                                   class="mr-2" onchange="toggleConditions()">
                            <span class="text-blue-900">I have no learning conditions or difficulties</span>
                        </label>
                    </div>
                    
                    <div id="conditions_section">
                        <label for="conditions" class="block text-sm font-medium text-blue-900 mb-2">
                            Please describe any learning conditions or difficulties (optional)
                        </label>
                        <textarea id="conditions" name="conditions" rows="3" 
                                  class="form-input" 
                                  placeholder="e.g., Dyslexia, ADHD, Visual processing difficulties...">{{if .FormData}}{{.FormData.conditions}}{{end}}</textarea>
                        <p class="text-sm text-blue-600 mt-1">
                            This information helps us provide better support and resources.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-between pt-6">
                <a href="/app" class="text-blue-600 hover:underline">Skip for now</a>
                <button type="submit" class="btn-primary">
                    Complete Profile
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function toggleConditions() {
    const checkbox = document.getElementById('has_no_conditions');
    const conditionsSection = document.getElementById('conditions_section');
    const conditionsTextarea = document.getElementById('conditions');
    
    if (checkbox.checked) {
        conditionsSection.style.display = 'none';
        conditionsTextarea.value = '';
    } else {
        conditionsSection.style.display = 'block';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleConditions();
});
</script>
{{end}}
