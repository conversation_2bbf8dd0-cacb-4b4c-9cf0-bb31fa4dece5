<!-- HEADER AND MENU -->
<div class="mt-2 xl:mt-4 bg-blue-900 py-0.5 max-w-screen-xl mx-2 xl:mx-auto rounded">
  <img
    alt="Visual Reading Online Header"
    src="/static/images/vro-heading.png"
    class="mx-auto"
  />
</div>

<!-- Navigation wrapper with Alpine.js state management -->
<div x-data="{ mobileMenuOpen: false, resourcesDropdownOpen: false }">
  <!-- Desktop Menu (md and above) -->
  <div class="mx-2 mt-1">
    <div class="max-w-screen-xl px-0 mx-auto md:px-0">
      <!-- Main Navigation Bar -->
      <div class="hidden mx-auto mt-0 text-lg bg-blue-900 rounded-md text-yellow-50 xl:block">
        <div class="flex justify-around items-center font-bold gap-0.5 p-0.5">
          <!-- Home -->
          <a href="/" 
             class="nav-link {{if eq .<PERSON> "home"}}nav-link-active{{else}}nav-link-inactive{{end}}">
            Home
          </a>

          <!-- Learn More -->
          <a href="/learn-more" 
             class="nav-link {{if eq .Page "learn-more"}}nav-link-active{{else}}nav-link-inactive{{end}}">
            Learn More
          </a>

          <!-- Get Started -->
          <a href="/get-started" 
             class="nav-link {{if eq .Page "get-started"}}nav-link-active{{else}}nav-link-inactive{{end}}">
            Get Started
          </a>

          <!-- Resources Dropdown -->
          <div class="relative" x-data="{ open: false }" @mouseenter="open = true" @mouseleave="open = false">
            <button class="nav-link nav-link-inactive flex items-center">
              Resources
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            
            <div x-show="open" 
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="transition ease-in duration-75"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95"
                 class="absolute top-full left-0 mt-1 w-48 bg-blue-900 rounded-md shadow-lg z-50">
              <div class="py-1">
                <a href="/dr-cooper" 
                   class="dropdown-item {{if eq .Page "dr-cooper"}}dropdown-item-active{{end}}">
                  About Dr. Cooper
                </a>
                <a href="/articles" 
                   class="dropdown-item {{if eq .Page "articles"}}dropdown-item-active{{end}}">
                  Articles & Blogs
                </a>
                <a href="/faqs" 
                   class="dropdown-item {{if eq .Page "faqs"}}dropdown-item-active{{end}}">
                  FAQs
                </a>
                <a href="/our-results" 
                   class="dropdown-item {{if eq .Page "our-results"}}dropdown-item-active{{end}}">
                  Our Results
                </a>
                <a href="/products" 
                   class="dropdown-item {{if eq .Page "products"}}dropdown-item-active{{end}}">
                  Products
                </a>
              </div>
            </div>
          </div>

          <!-- Support -->
          <a href="/contact" 
             class="nav-link {{if eq .Page "contact"}}nav-link-active{{else}}nav-link-inactive{{end}}">
            Support
          </a>

          <!-- Join Us -->
          <a href="/auth/register" 
             class="nav-link {{if eq .Page "register"}}nav-link-active{{else}}nav-link-inactive{{end}}">
            Join Us
          </a>

          <!-- Log In -->
          <a href="/auth/login" 
             class="nav-link {{if eq .Page "login"}}nav-link-active{{else}}nav-link-inactive{{end}}">
            Log In
          </a>
        </div>
      </div>

      <!-- Mobile Menu Button -->
      <div class="xl:hidden">
        <button @click="mobileMenuOpen = !mobileMenuOpen" 
                class="w-full p-4 text-lg font-bold text-yellow-50 bg-blue-900 rounded-md hover:bg-blue-700">
          <div class="flex items-center justify-between">
            <span>Menu</span>
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
              <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
        </button>
      </div>

      <!-- Mobile Menu -->
      <div
        x-show="mobileMenuOpen"
        x-transition:enter="transition ease-out duration-200"
        x-transition:enter-start="opacity-0 transform -translate-y-2"
        x-transition:enter-end="opacity-100 transform translate-y-0"
        x-transition:leave="transition ease-in duration-150"
        x-transition:leave-start="opacity-100 transform translate-y-0"
        x-transition:leave-end="opacity-0 transform -translate-y-2"
        class="xl:hidden absolute z-50 w-full left-0 right-0"
      >
        <div class="mt-2 bg-blue-900 rounded-md mx-2">
          <nav class="flex flex-col">
            <a href="/" 
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {{if eq .Page "home"}}bg-blue-700{{end}}">
               Home
            </a>
            <a href="/learn-more" 
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {{if eq .Page "learn-more"}}bg-blue-700{{end}}">
               Learn More
            </a>
            <a href="/get-started" 
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {{if eq .Page "get-started"}}bg-blue-700{{end}}">
               Get Started
            </a>

            <!-- Mobile Resources Dropdown -->
            <div x-data="{ mobileResourcesOpen: false }">
              <button @click="mobileResourcesOpen = !mobileResourcesOpen" 
                      class="w-full p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 text-left flex items-center justify-between">
                Resources
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>

              <div x-show="mobileResourcesOpen" class="bg-blue-800">
                <a href="/dr-cooper" 
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {{if eq .Page "dr-cooper"}}bg-blue-700{{end}}">
                   About Dr. Cooper
                </a>
                <a href="/articles" 
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {{if eq .Page "articles"}}bg-blue-700{{end}}">
                   Articles & Blogs
                </a>
                <a href="/faqs" 
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {{if eq .Page "faqs"}}bg-blue-700{{end}}">
                   FAQs
                </a>
                <a href="/our-results" 
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {{if eq .Page "our-results"}}bg-blue-700{{end}}">
                   Our Results
                </a>
                <a href="/products" 
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {{if eq .Page "products"}}bg-blue-700{{end}}">
                   Products
                </a>
              </div>
            </div>

            <a href="/contact" 
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {{if eq .Page "contact"}}bg-blue-700{{end}}">
               Support
            </a>
            <a href="/auth/register" 
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {{if eq .Page "register"}}bg-blue-700{{end}}">
               Join Us
            </a>
            <a href="/auth/login" 
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {{if eq .Page "login"}}bg-blue-700{{end}}">
               Log In
            </a>
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- END OF HEADER AND MENU -->
