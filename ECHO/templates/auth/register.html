{{template "base.html" .}}

{{define "content"}}
<div class="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg">
    <h1 class="text-2xl font-bold text-blue-900 mb-6 text-center">Join Visual Reading Online</h1>
    
    {{if .Error}}
    <div class="alert alert-error mb-6">
        <span>{{.Error}}</span>
        <button class="alert-close ml-4 text-lg">&times;</button>
    </div>
    {{end}}
    
    <form method="POST" action="/auth/register" class="space-y-4">
        <div>
            <label for="first_name" class="block text-sm font-medium text-blue-900 mb-2">First Name</label>
            <input type="text" id="first_name" name="first_name" required 
                   class="form-input" placeholder="Enter your first name"
                   value="{{if .FormData}}{{.FormData.first_name}}{{end}}">
        </div>
        
        <div>
            <label for="last_name" class="block text-sm font-medium text-blue-900 mb-2">Last Name</label>
            <input type="text" id="last_name" name="last_name" required 
                   class="form-input" placeholder="Enter your last name"
                   value="{{if .FormData}}{{.FormData.last_name}}{{end}}">
        </div>
        
        <div>
            <label for="email" class="block text-sm font-medium text-blue-900 mb-2">Email</label>
            <input type="email" id="email" name="email" required 
                   class="form-input" placeholder="Enter your email"
                   value="{{if .FormData}}{{.FormData.email}}{{end}}">
        </div>
        
        <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-sm text-blue-800">
                <strong>Note:</strong> We use magic link authentication for security. After registration, you'll receive login links via email instead of using passwords.
            </p>
        </div>
        
        <button type="submit" class="w-full btn-primary">
            Create Account
        </button>
    </form>
    
    <div class="mt-6 text-center">
        <p class="text-sm text-blue-800">
            Already have an account? 
            <a href="/auth/login" class="font-medium hover:underline">Log In</a>
        </p>
        <p class="text-sm text-blue-800 mt-2">
            <a href="/auth/magic-link" class="font-medium hover:underline">Login with Magic Link</a>
        </p>
        <p class="text-sm text-blue-800 mt-2">
            <a href="/auth/google" class="font-medium hover:underline">Continue with Google</a>
        </p>
    </div>
</div>
{{end}}
