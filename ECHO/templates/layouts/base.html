<!DOCTYPE html>
<html lang="en">
<head>
    <title>{{if .Title}}{{.Title}}{{else}}Visual Reading Online{{end}}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'vro-yellow': '#fef9c3',
                        'vro-blue': '#1e3a8a',
                        'vro-blue-light': '#3b82f6',
                    }
                }
            }
        }
    </script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/styles.css">
    
    <style>
        /* Fade-in animation */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content-fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }

        /* Navigation styles */
        .nav-link {
            @apply w-full py-2 px-4 flex items-center justify-center h-full rounded-md transition-colors duration-200 font-bold;
            color: white !important;
        }

        .nav-link-active {
            @apply bg-blue-700 w-full h-full;
        }

        .nav-link-inactive {
            @apply hover:bg-blue-700 w-full h-full;
        }

        /* Dropdown styles */
        .dropdown-item {
            @apply block w-full px-4 py-2 whitespace-nowrap transition-colors duration-200 font-bold;
            color: white !important;
        }

        .dropdown-item:hover {
            @apply bg-blue-700;
        }

        .dropdown-item-active {
            @apply bg-blue-700;
        }

        /* Base styles */
        body {
            @apply text-blue-900;
        }

        a {
            @apply text-blue-900 hover:text-blue-700 transition-colors duration-200;
        }

        /* Input styles */
        input[type="email"],
        input[type="password"],
        input[type="text"],
        textarea,
        select {
            @apply border-blue-900 rounded-md;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }
    </style>
</head>

<body class="min-h-screen bg-vro-yellow text-blue-900">
    <!-- Header -->
    {{if .IsAuthenticated}}
        {{if and .User .User.IsAdmin}}
            {{template "admin_header.html" .}}
        {{else}}
            {{template "user_header.html" .}}
        {{end}}
    {{else}}
        {{template "public_header.html" .}}
    {{end}}

    <!-- Main content -->
    <main class="content-fade-in">
        {{template "content" .}}
    </main>

    <!-- Footer -->
    {{template "footer.html" .}}

    <!-- JavaScript -->
    <script src="/static/js/main.js"></script>
    
    <!-- Logout handling -->
    <script>
        function handleLogout(event) {
            event.preventDefault();
            window.location.href = "/auth/logout";
        }

        document.addEventListener('DOMContentLoaded', function() {
            const logoutLinks = document.querySelectorAll('[data-logout-link], .logout-link');
            logoutLinks.forEach(link => {
                link.addEventListener('click', handleLogout);
            });
        });
    </script>
</body>
</html>
