# Visual Reading Online - Echo Project Plan & Progress

## Project Timeline: One Day Development Sprint

### Phase 1: Foundation Setup (2 hours) ⏳
- [x] Create project structure
- [x] Initialize Go module
- [ ] Setup Echo server with middleware
- [ ] Database connection and models
- [ ] Basic template system
- [ ] Static file serving
- [ ] Environment configuration

### Phase 2: Authentication System (2 hours)
- [ ] User model and database schema
- [ ] Session management
- [ ] Google OAuth integration
- [ ] Magic link authentication
- [ ] Registration/login handlers
- [ ] Authentication middleware

### Phase 3: Core User Features (3 hours)
- [ ] User profile system
- [ ] Group allocation system
- [ ] Profile completion workflow
- [ ] Dashboard routing based on groups
- [ ] Contact form system
- [ ] Basic admin interface

### Phase 4: Content Management (1 hour)
- [ ] File upload system
- [ ] Document management
- [ ] Admin CRUD operations
- [ ] Document rendering (DOCX)

### Phase 5: Polish & Testing (2 hours)
- [ ] Template migration and styling
- [ ] Error handling
- [ ] Basic testing
- [ ] Documentation updates
- [ ] Final testing

## Feature Requirements Checklist

### Authentication ✅ Priority 1
- [ ] Google OAuth login
- [ ] Magic link email authentication
- [ ] User registration with email
- [ ] Session management
- [ ] Logout functionality

### User Management ✅ Priority 1
- [ ] Structured user signup
- [ ] Profile completion workflow
- [ ] User groups (site-admins, members, etc.)
- [ ] Group-based dashboard routing
- [ ] User profile editing

### Content Management ✅ Priority 1
- [ ] Admin dashboard
- [ ] File upload (DOCX files)
- [ ] Document management (CRUD)
- [ ] Document rendering on pages
- [ ] Contact form with admin replies

### Advanced Features 🔄 Priority 2 (Future)
- [ ] Video call scheduling
- [ ] Third-party video integration
- [ ] Calendar system
- [ ] Advanced admin features
- [ ] Email notifications

## Database Schema Progress

### Core Tables
- [ ] users (id, email, first_name, last_name, provider, created_at, updated_at)
- [ ] user_profiles (user_id, age, gender, race, address, group_id, completed_at)
- [ ] user_groups (id, name, code, description)
- [ ] sessions (id, user_id, token, expires_at)
- [ ] magic_links (id, user_id, token, expires_at)

### Content Tables
- [ ] documents (id, title, slug, category, file_path, author, published)
- [ ] document_categories (id, name, description)
- [ ] contacts (id, name, email, subject, message, replied_at)
- [ ] pages (id, name, content, published)

## Template Migration Status

### Layouts
- [ ] Base layout (navbar, footer, meta tags)
- [ ] Admin layout
- [ ] Auth layout (login/register pages)

### Public Pages
- [ ] Home page
- [ ] About/Learn More
- [ ] Get Started pages
- [ ] Contact page
- [ ] Articles/Blog pages
- [ ] Legal pages (Terms, Privacy)

### Authenticated Pages
- [ ] Dashboard (group-based routing)
- [ ] Profile pages
- [ ] Admin dashboard
- [ ] Document management

### Authentication Pages
- [ ] Login page
- [ ] Register page
- [ ] Magic link request
- [ ] Profile completion

## Technical Decisions Made

### Framework Stack
- **Web Framework**: Echo v4 (chosen for performance and simplicity)
- **Database**: PostgreSQL with GORM ORM
- **Templates**: Go html/template
- **Frontend**: Tailwind CSS + Alpine.js (migrated from Django)
- **Authentication**: Custom implementation with Google OAuth

### Project Structure
- Clean separation of concerns
- Internal package for private code
- Clear handler organization
- Template organization by feature

## Current Blockers & Solutions
- None currently identified

## Next Steps After Day 1
1. Video call integration (WebRTC or third-party)
2. Advanced scheduling system
3. Email notification system
4. Production deployment setup
5. Performance optimization
6. Comprehensive testing

## Notes & Decisions
- Using existing color scheme and styling from Django version
- Maintaining same URL structure where possible
- Focusing on MVP for day 1, advanced features later
- Database hosted remotely at **********
- Development on local system, production behind nginx-proxy-manager

## Resources & References
- Django templates: `/VRO-DJANGO/templates/`
- Buffalo templates: `/GOVRO/visualreadingonline/templates/`
- Static assets: Copy from Django version
- Database schema: Reference Django models
