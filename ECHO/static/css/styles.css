/* Visual Reading Online - Custom Styles */

/* Base styles for text and links */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

/* Navigation styles */
.nav-link {
    transition: all 0.2s ease-in-out;
}

.nav-link:hover {
    transform: translateY(-1px);
}

/* Button styles */
.btn-primary {
    background-color: #1e3a8a;
    color: #fef9c3;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: bold;
    transition: all 0.2s ease-in-out;
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(30, 58, 138, 0.3);
}

.btn-secondary {
    background-color: #fbbf24;
    color: #1e3a8a;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: bold;
    transition: all 0.2s ease-in-out;
    border: none;
    cursor: pointer;
}

.btn-secondary:hover {
    background-color: #f59e0b;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(251, 191, 36, 0.3);
}

/* Form styles */
.form-input {
    border: 2px solid #1e3a8a;
    border-radius: 0.5rem;
    padding: 0.75rem;
    width: 100%;
    transition: all 0.2s ease-in-out;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Card styles */
.card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

/* Alert styles */
.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.alert-warning {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
}

.alert-info {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

/* Loading spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1e3a8a;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive utilities */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none;
    }
}
