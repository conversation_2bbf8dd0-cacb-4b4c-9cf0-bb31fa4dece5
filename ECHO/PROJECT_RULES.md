# Visual Reading Online - Echo Project Rules & Configuration

## Project Overview
- **Domain**: https://visualreadingonline.com
- **Framework**: Go Echo v4
- **Database**: PostgreSQL 17 at **********
- **Database Name**: VRO
- **Database User**: postgres
- **Deployment**: Behind nginx-proxy-manager (handles SSL)

## Project Structure Rules

### Directory Layout
```
ECHO/
├── cmd/
│   └── server/          # Main application entry point
├── internal/
│   ├── config/          # Configuration management
│   ├── handlers/        # HTTP handlers (controllers)
│   ├── middleware/      # Custom middleware
│   ├── models/          # Database models
│   ├── services/        # Business logic
│   └── utils/           # Utility functions
├── templates/           # HTML templates
│   ├── layouts/         # Base layouts
│   ├── pages/           # Page templates
│   ├── components/      # Reusable components
│   └── auth/            # Authentication templates
├── static/              # Static assets (CSS, JS, images)
│   ├── css/
│   ├── js/
│   └── images/
├── migrations/          # Database migrations
├── docs/                # Documentation
└── scripts/             # Build and deployment scripts
```

## Coding Standards

### Naming Conventions
- **Files**: snake_case (user_handler.go)
- **Functions**: PascalCase for exported, camelCase for private
- **Variables**: camelCase
- **Constants**: UPPER_SNAKE_CASE
- **Database Tables**: snake_case (user_profiles)
- **Database Columns**: snake_case (first_name)

### Handler Organization
- One handler per file when complex
- Group related simple handlers
- Clear function names: `GetUserProfile`, `CreateUser`, `UpdateUserProfile`

### Template Organization
- Base layout: `layouts/base.html`
- Page-specific: `pages/home.html`
- Reusable components: `components/navbar.html`

## Color Scheme (From Django/Buffalo)
- **Primary Blue**: #1e3a8a (blue-900)
- **Secondary Blue**: #3b82f6 (blue-500)
- **Accent Yellow**: #fef3c7 (yellow-100), #fbbf24 (yellow-400)
- **Success Green**: #10b981 (emerald-500)
- **Error Red**: #ef4444 (red-500)
- **Gray Scale**: #f9fafb (gray-50), #6b7280 (gray-500), #1f2937 (gray-800)

## Database Configuration
```go
Host: "**********"
Port: 5432
Database: "VRO"
Username: "postgres"
Password: "postgres"
SSLMode: "disable" // Dev environment
```

## Environment Variables
- `GO_ENV`: development/production
- `PORT`: Server port (default: 8000)
- `DB_HOST`: Database host
- `DB_PORT`: Database port
- `DB_NAME`: Database name
- `DB_USER`: Database username
- `DB_PASSWORD`: Database password
- `SESSION_SECRET`: Session encryption key
- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret

## Development Workflow
1. **Feature Development**: Create feature branch
2. **Testing**: Test locally before committing
3. **Documentation**: Update relevant docs
4. **Migration**: Create migration if DB changes needed

## Template Migration Notes
- Copy existing Tailwind classes from Django templates
- Maintain existing color scheme and layout
- Use Go template syntax: `{{.Title}}` instead of `{{title}}`
- Keep Alpine.js for interactive components

## Performance Guidelines
- Use database indexes for frequently queried fields
- Implement proper error handling
- Use context for request timeouts
- Optimize template rendering with caching where appropriate

## Security Rules
- Always validate user input
- Use CSRF protection
- Implement proper session management
- Sanitize file uploads
- Use parameterized queries (GORM handles this)

## Deployment Notes
- Single binary deployment
- Environment-specific configuration
- Database migrations run automatically
- Static files served by Echo in development, nginx in production
