from pathlib import Path
from datetime import timedelta
import os
import logging.handlers

# Set BASE_DIR to point to F:\DOCUMENTS\VRO2025
BASE_DIR = Path(__file__).resolve().parent.parent

# Create logs directory if it doesn't exist
LOGS_DIR = BASE_DIR / 'logs'
LOGS_DIR.mkdir(exist_ok=True)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

# Remove the MagicLinkForm class from here since it's now in VRO_App2/forms.py

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure---%!^l9_+#_rmebcp^e2&*6eo911pzt%(i+nnryj@d76yp=8jh" if DEBUG else "django-secure---%!^l9_+#_rmebcp^e$$£V6U!!$£$2&EE*6eo911pzt%(i+nnryj@d76yp=8jh"

# Conditional hosts based on DEBUG
if DEBUG:
    ALLOWED_HOSTS = ['*']
else:
    ALLOWED_HOSTS = ['visualreadingonline.com', 'www.visualreadingonline.com', 'localhost', '127.0.0.1']

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',
    'VRO_App0',
    'VRO_App1',
    'VRO_App2',
    'VRO_App3',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',
    'django_browser_reload',
    'tailwind',
    'tailwindcss',
    'crispy_forms',
    'crispy_tailwind',
]

# Add this if it's not already there
SITE_ID = 1

TAILWIND_APP_NAME = 'tailwindcss'

# NPM path for Windows
if os.name == 'nt':
    NPM_BIN_PATH = r"C:\Program Files\nodejs\npm.cmd"
else:
    NPM_BIN_PATH = '/usr/bin/npm'

# Development-specific settings
if DEBUG:
    INTERNAL_IPS = ["127.0.0.1"]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django_browser_reload.middleware.BrowserReloadMiddleware",
    'VRO_App1.middleware.UserActivityMiddleware',
]

ROOT_URLCONF = "VRO_Project.urls"

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            os.path.join(BASE_DIR, 'templates'),  # Make sure this path is correct
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# Add this to ensure debug templates are never used
DEBUG_PROPAGATE_EXCEPTIONS = False

WSGI_APPLICATION = "VRO_Project.wsgi.application"

# Static files configuration
STATIC_URL = 'static/'
STATICFILES_DIRS = [BASE_DIR / "static"]
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Database configuration
if DEBUG:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": "VRO",
            "USER": "postgres",
            "PASSWORD": "postgres",
            "HOST": "**********",
            "PORT": "5432",
        }
    }
else:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": "VRO",
            "USER": "postgres",
            "PASSWORD": "postgres",
            "HOST": "**********",
            "PORT": "5432",
        }
    }

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {"NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator"},
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
]

# Internationalization
LANGUAGE_CODE = "en-gb"
TIME_ZONE = "UTC"
USE_I18N = True
USE_TZ = True

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Remove the urlpatterns line from here

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Site ID required for allauth
SITE_ID = 1

# Authentication settings
AUTHENTICATION_BACKENDS = (
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
)

# Email configuration
EMAIL_BACKEND = 'anymail.backends.mailersend.EmailBackend'
ANYMAIL = {
    "MAILERSEND_API_TOKEN": "mlsn.b36d527d2edf140cdc9b5d3b3ba1cf97babbd49b5d0eb084ce4183c5dc5e0a07",
    "MAILERSEND_API_URL": "https://api.mailersend.com/v1/",
    "MAILERSEND_BATCH_SEND_MODE": "batch",
    "MAILERSEND_DEBUG": DEBUG,
}
DEFAULT_FROM_EMAIL = "<EMAIL>"
SERVER_EMAIL = DEFAULT_FROM_EMAIL
ANYMAIL_IGNORE_UNSUPPORTED_FEATURES = True

# Google OAuth2 settings
GOOGLE_CLIENT_ID = '************-5r51hbu6gqkl5ps9ona179ub023d2lsn.apps.googleusercontent.com'
GOOGLE_CLIENT_SECRET = '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'

# AllAuth settings
ACCOUNT_SESSION_REMEMBER = False
ACCOUNT_AUTHENTICATION_METHOD = 'email'
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_USER_MODEL_USERNAME_FIELD = None
ACCOUNT_LOGIN_ON_EMAIL_CONFIRMATION = True
ACCOUNT_LOGIN_ON_PASSWORD_RESET = True
ACCOUNT_DEFAULT_HTTP_PROTOCOL = 'http' if DEBUG else 'https'

# Login/Logout URLs
LOGIN_URL = 'VRO_App1:please_login'
LOGIN_REDIRECT_URL = '/app/'
LOGOUT_URL = 'account_logout'
LOGOUT_REDIRECT_URL = 'VRO_App1:logged_out'

# Social Account Settings
SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'APP': {
            'client_id': '************-5r51hbu6gqkl5ps9ona179ub023d2lsn.apps.googleusercontent.com',
            'secret': '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',
            'key': ''
        },
        'SCOPE': [
            'profile',
            'email',
        ],
        'AUTH_PARAMS': {
            'access_type': 'offline',  # Change this to offline
            'prompt': 'consent'  # Change this to consent
        }
    }
}

SOCIALACCOUNT_LOGIN_ON_GET = True  # Skip the intermediate page
ACCOUNT_LOGIN_ON_EMAIL_CONFIRMATION = True
ACCOUNT_LOGIN_ON_PASSWORD_RESET = True
SOCIALACCOUNT_AUTO_SIGNUP = True
SOCIALACCOUNT_EMAIL_REQUIRED = True
SOCIALACCOUNT_EMAIL_VERIFICATION = 'none'
SOCIALACCOUNT_QUERY_EMAIL = True

# Security settings
if not DEBUG:
    # Comment out SSL-related settings for local testing
    # SECURE_SSL_REDIRECT = True
    # SESSION_COOKIE_SECURE = True
    # CSRF_COOKIE_SECURE = True
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    X_FRAME_OPTIONS = 'DENY'
    # SECURE_HSTS_SECONDS = ********
    # SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    # SECURE_HSTS_PRELOAD = True

# Crispy Forms Configuration
CRISPY_ALLOWED_TEMPLATE_PACKS = "tailwind"
CRISPY_TEMPLATE_PACK = "tailwind"

# JWT Configuration
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
}

# JWT Settings
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=1),  # Changed from 60 minutes
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
}

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': str(LOGS_DIR / 'vro.log'),  # Convert Path to string
            'maxBytes': 1024 * 1024 * 5,  # 5 MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        '': {  # Root logger
            'handlers': ['console', 'file'],
            'level': 'INFO',
        },
        'VRO_App3': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Add Whitenoise settings
if not DEBUG:
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
    WHITENOISE_COMPRESSION_ENABLED = True
    WHITENOISE_BROTLI_ENABLED = True
    WHITENOISE_MANIFEST_STRICT = False

if not DEBUG:
    CSRF_TRUSTED_ORIGINS = [
        'https://visualreadingonline.com',
        'https://www.visualreadingonline.com',
    ]

if not DEBUG:
    CSRF_COOKIE_SECURE = True

# Add this to your settings.py
TEST_RUNNER = 'VRO_App1.test_runner.CustomTestRunner'

# Add this near the top of the file, with other key settings
AUTH_USER_MODEL = 'VRO_App2.User'

SOCIALACCOUNT_ADAPTER = 'VRO_App2.adapters.CustomSocialAccountAdapter'
ACCOUNT_ADAPTER = 'allauth.account.adapter.DefaultAccountAdapter'

SOCIALACCOUNT_AUTO_SIGNUP = True
SOCIALACCOUNT_EMAIL_REQUIRED = True
SOCIALACCOUNT_EMAIL_VERIFICATION = 'none'
SOCIALACCOUNT_QUERY_EMAIL = True

# Force HTTPS for OAuth callbacks
ACCOUNT_DEFAULT_HTTP_PROTOCOL = 'https'

# Update/add these settings
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
USE_X_FORWARDED_HOST = True
USE_X_FORWARDED_PORT = True

# Session settings
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 86400  # 24 hours
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
SESSION_SAVE_EVERY_REQUEST = True

# Cookie settings
SESSION_COOKIE_NAME = 'vrosessionid'  # Custom session cookie name
SESSION_COOKIE_SECURE = True if not DEBUG else False  # Only use HTTPS in production
CSRF_COOKIE_SECURE = True if not DEBUG else False
SESSION_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_SAMESITE = 'Lax'
SESSION_COOKIE_DOMAIN = None  # Let Django set this automatically

# Social auth settings
ACCOUNT_SESSION_REMEMBER = True
SOCIALACCOUNT_SESSION_LIFETIME = timedelta(days=1)
ACCOUNT_DEFAULT_HTTP_PROTOCOL = 'https' if not DEBUG else 'http'

SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'APP': {
            'client_id': '************-5r51hbu6gqkl5ps9ona179ub023d2lsn.apps.googleusercontent.com',
            'secret': '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',
            'key': ''
        },
        'SCOPE': [
            'profile',
            'email',
        ],
        'AUTH_PARAMS': {
            'access_type': 'offline',
            'prompt': 'consent'
        }
    }
}


