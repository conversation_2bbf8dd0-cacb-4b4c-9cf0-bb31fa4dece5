from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView
from .views import custom_404, custom_500

handler404 = 'VRO_Project.views.custom_404'
handler500 = 'VRO_Project.views.custom_500'

urlpatterns = [
    path('', include('VRO_App1.urls', namespace='VRO_App1')),
    path('app/', include('VRO_App2.urls')),
    path('', include('VRO_App3.urls', namespace='VRO_App3')),
    path('accounts/', include('allauth.urls')),
    path("__reload__/", include("django_browser_reload.urls")),
]

if settings.DEBUG:
    urlpatterns += [
        path('404/', TemplateView.as_view(template_name='errors/404.html')),
        path('500/', TemplateView.as_view(template_name='errors/500.html')),
    ] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)




