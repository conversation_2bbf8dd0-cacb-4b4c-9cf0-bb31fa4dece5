import logging
from django.shortcuts import render

logger = logging.getLogger(__name__)

def custom_404(request, exception):
    try:
        return render(request, 'errors/404.html', status=404)
    except Exception as e:
        logger.error(f"Error in custom_404 view: {str(e)}")
        return render(request, 'errors/500.html', status=500)

def custom_500(request):
    return render(request, 'errors/500.html', status=500)