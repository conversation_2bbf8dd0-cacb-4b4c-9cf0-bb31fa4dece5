{"name": "vro2025", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "vro2025", "version": "1.0.0", "license": "ISC", "devDependencies": {"prettier": "^3.5.3", "prettier-plugin-django-template": "^0.1.1"}}, "node_modules/prettier": {"version": "3.5.3", "resolved": "https://registry.npmjs.org/prettier/-/prettier-3.5.3.tgz", "integrity": "sha512-QQtaxnoDJeAkDvDKWCLiwIXkTgRhwYDEQCghU9Z6q03iyek/rxRh/2lC3HB7P8sWT2xC/y5JDctPLBIGzHKbhw==", "dev": true, "license": "MIT", "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-plugin-django-template": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/prettier-plugin-django-template/-/prettier-plugin-django-template-0.1.1.tgz", "integrity": "sha512-TvBuOQMOngDwEiP8tiR3P6Kw2Umb97N6/GUiJkd2FZ44zVxjl2pCLIAPVAhLUAgtu8OXkezWHWDLG35/wqeQew==", "dev": true, "license": "MIT", "peerDependencies": {"prettier": "^3.0.0"}}}}