{% extends 'base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-900">Manage Articles</h1>
        <a href="{% url 'VRO_App3:upload_document' 'article' %}" class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-800">
            Upload New Article
        </a>
    </div>

    <div class="bg-white shadow rounded-lg">
        <table class="min-w-full">
            <thead>
                <tr class="bg-gray-50">
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Author</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Posted Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for article in articles %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">{{ article.title }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ article.author|default:"-" }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ article.posted_date|date:"Y-m-d"|default:"-" }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <a href="{% url 'VRO_App1:article' article.url_name %}" target="_blank" class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                        <a href="{% url 'VRO_App3:delete_document' article.id %}" class="text-red-600 hover:text-red-900">Delete</a>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="4" class="px-6 py-4 text-center text-gray-500">No articles found</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}