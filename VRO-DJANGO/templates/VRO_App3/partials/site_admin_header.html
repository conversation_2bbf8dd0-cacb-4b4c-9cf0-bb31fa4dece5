{% load static %}

{% if user.is_authenticated and user.userprofile.group.code == 'site-admins' %}
<!-- Your existing header content -->
{% else %}
<meta http-equiv="refresh" content="0;url={% url 'VRO_App1:please_login' %}">
{% endif %}

<!-- Main wrapper with background color -->
<div class="bg-[#fef9c3]">
  <!-- HEADER AND MENU -->
  <div class="mt-2 xl:mt-4 bg-blue-900 py-0.5 max-w-screen-xl mx-2 xl:mx-auto rounded">
    <img
      alt="Visual Reading Online Header"
      src="{% static 'images/vro-heading.png' %}"
      class="mx-auto"
    />
  </div>

  <!-- Navigation wrapper with Alpine.js state management -->
  <div class="mx-2 mt-1" x-data="{ mobileMenuOpen: false, siteContentOpen: false, usersGroupsOpen: false }">
    <div class="max-w-screen-xl px-0 mx-auto md:px-0">
      <!-- Desktop Menu (md and above) -->
      <div class="hidden mx-auto mt-0 text-lg bg-blue-900 rounded-md text-yellow-50 xl:block">
        <div class="grid grid-cols-4 gap-0.5 p-0.5">
          <!-- Dashboard -->
          <a href="{% url 'VRO_App3:site-admins' %}" 
             class="nav-link {% if request.resolver_match.url_name == 'site-admins' %}nav-link-active{% else %}nav-link-inactive{% endif %}">
            My Dashboard
          </a>

          <!-- Users & Groups Dropdown -->
          <div class="relative" @mouseleave="usersGroupsOpen = false">
            <button @mouseover="usersGroupsOpen = true"
                    class="w-full nav-link {% if request.resolver_match.url_name in 'profile,manage_group_allocations' %}nav-link-active{% else %}nav-link-inactive{% endif %}">
              Users & Groups
            </button>
            
            <!-- Dropdown menu -->
            <div x-show="usersGroupsOpen"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform translate-y-0"
                 x-transition:leave-end="opacity-0 transform -translate-y-2"
                 class="absolute z-10 w-full bg-blue-900 rounded-lg shadow-lg"
                 @mouseover="usersGroupsOpen = true">
              <div class="py-2">
                <a href="{% url 'VRO_App3:manage_groups' %}" 
                   class="dropdown-item {% if request.resolver_match.url_name == 'manage_groups' %}dropdown-item-active{% endif %}">
                  Groups
                </a>
                <a href="{% url 'VRO_App3:manage_group_allocations' %}" 
                   class="dropdown-item {% if request.resolver_match.url_name == 'manage_group_allocations' %}dropdown-item-active{% endif %}">
                  Group Allocations
                </a>
                <a href="{% url 'VRO_App3:manage_users' %}" 
                   class="dropdown-item {% if request.resolver_match.url_name == 'manage_users' %}dropdown-item-active{% endif %}">
                  Users
                </a>
              </div>
            </div>
          </div>

          <!-- Site Content Dropdown -->
          <div class="relative" @mouseleave="siteContentOpen = false">
            <button @mouseover="siteContentOpen = true"
                    class="w-full nav-link {% if request.resolver_match.url_name in 'manage_articles,manage_blogs,manage_pricing,manage_genders,manage_races,manage_conditions,public_messages' %}nav-link-active{% else %}nav-link-inactive{% endif %}">
              Site Content
            </button>
            
            <!-- Dropdown menu -->
            <div x-show="siteContentOpen"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform translate-y-0"
                 x-transition:leave-end="opacity-0 transform -translate-y-2"
                 class="absolute z-10 w-full bg-blue-900 rounded-lg shadow-lg"
                 @mouseover="siteContentOpen = true">
              <div class="py-2">
                <a href="{% url 'VRO_App3:manage_articles' %}" 
                   class="dropdown-item font-bold {% if request.resolver_match.url_name == 'manage_articles' %}dropdown-item-active{% endif %}">
                  Articles
                </a>
                <a href="{% url 'VRO_App3:manage_blogs' %}" 
                   class="dropdown-item font-bold {% if request.resolver_match.url_name == 'manage_blogs' %}dropdown-item-active{% endif %}">
                  Blogs
                </a>
                <a href="{% url 'VRO_App3:manage_conditions' %}" 
                   class="dropdown-item font-bold {% if request.resolver_match.url_name == 'manage_conditions' %}dropdown-item-active{% endif %}">
                  Conditions
                </a>
                <a href="{% url 'VRO_App3:manage_genders' %}" 
                   class="dropdown-item font-bold {% if request.resolver_match.url_name == 'manage_genders' %}dropdown-item-active{% endif %}">
                  Genders
                </a>
                <a href="{% url 'VRO_App3:internal_messages' %}" 
                   class="dropdown-item font-bold {% if request.resolver_match.url_name == 'internal_messages' %}dropdown-item-active{% endif %}">
                  Internal Messages
                </a>
                <a href="{% url 'VRO_App3:manage_pages' %}" 
                   class="dropdown-item font-bold {% if request.resolver_match.url_name == 'manage_pages' %}dropdown-item-active{% endif %}">
                  Pages
                </a>
                <a href="{% url 'VRO_App3:manage_pricing' %}" 
                   class="dropdown-item font-bold {% if request.resolver_match.url_name == 'manage_pricing' %}dropdown-item-active{% endif %}">
                  Pricing
                </a>
                <a href="{% url 'VRO_App3:public_messages' %}" 
                   class="dropdown-item font-bold {% if request.resolver_match.url_name == 'public_messages' %}dropdown-item-active{% endif %}">
                  Public Messages
                </a>
                <a href="{% url 'VRO_App3:manage_races' %}" 
                   class="dropdown-item font-bold {% if request.resolver_match.url_name == 'manage_races' %}dropdown-item-active{% endif %}">
                  Races
                </a>
              </div>
            </div>
          </div>

          <!-- Logout -->
          <a href="{% url 'VRO_App1:logout' %}" 
             data-logout-link
             class="nav-link nav-link-inactive logout-link">
            Log Out
          </a>
        </div>
      </div>

      <!-- Mobile Menu Button -->
      <div class="xl:hidden">
        <button
          @click="mobileMenuOpen = !mobileMenuOpen"
          class="w-full p-3 mb-2 text-base font-bold text-yellow-50 bg-blue-900 rounded-md hover:bg-blue-700"
        >
          Menu
        </button>
      </div>

      <!-- Mobile Menu -->
      <div
        x-show="mobileMenuOpen"
        x-transition:enter="transition ease-out duration-200"
        x-transition:enter-start="opacity-0 transform -translate-y-2"
        x-transition:enter-end="opacity-100 transform translate-y-0"
        x-transition:leave="transition ease-in duration-150"
        x-transition:leave-start="opacity-100 transform translate-y-0"
        x-transition:leave-end="opacity-0 transform -translate-y-2"
        class="xl:hidden"
      >
        <nav class="bg-blue-900 rounded-md">
          <!-- Dashboard -->
          <a href="{% url 'VRO_App3:site-admins' %}" 
             class="block p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'site-admins' %}bg-blue-700{% endif %}">
            My Dashboard
          </a>
          
          <!-- Mobile Users & Groups Section -->
          <div x-data="{ mobileUsersGroupsOpen: false }">
            <button
              @click="mobileUsersGroupsOpen = !mobileUsersGroupsOpen"
              class="flex items-center justify-between w-full p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name in 'profile' %}bg-blue-700{% endif %}"
            >
              <span>Users & Groups</span>
              <svg
                class="w-5 h-5 transition-transform"
                :class="{ 'rotate-180': mobileUsersGroupsOpen }"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
              </svg>
            </button>

            <div x-show="mobileUsersGroupsOpen" class="bg-blue-800">
              <a href="{% url 'VRO_App2:profile' %}" 
                 class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'profile' %}bg-blue-700{% endif %}">
                Edit My Profile
              </a>
            </div>
          </div>

          <!-- Mobile Site Content Section -->
          <div x-data="{ mobileSiteContentOpen: false }">
            <button
              @click="mobileSiteContentOpen = !mobileSiteContentOpen"
              class="flex items-center justify-between w-full p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name in 'manage_group_allocations,manage_articles,manage_blogs,manage_pricing' %}bg-blue-700{% endif %}"
            >
              <span>Site Content</span>
              <svg
                class="w-5 h-5 transition-transform"
                :class="{ 'rotate-180': mobileSiteContentOpen }"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
              </svg>
            </button>

            <div x-show="mobileSiteContentOpen" class="bg-blue-800">
              <a href="{% url 'VRO_App3:manage_group_allocations' %}" 
                 class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'manage_group_allocations' %}bg-blue-700{% endif %}">
                Group Allocations
              </a>
              <a href="{% url 'VRO_App3:manage_articles' %}" 
                 class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'manage_articles' %}bg-blue-700{% endif %}">
                Articles
              </a>
              <a href="{% url 'VRO_App3:manage_blogs' %}" 
                 class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'manage_blogs' %}bg-blue-700{% endif %}">
                Blogs
              </a>
              <a href="{% url 'VRO_App3:manage_pricing' %}" 
                 class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'manage_pricing' %}bg-blue-700{% endif %}">
                Pricing
              </a>
              <a href="{% url 'VRO_App3:public_messages' %}" 
                 class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'public_messages' %}bg-blue-700{% endif %}">
                Public Messages
              </a>
            </div>
          </div>

          <!-- Logout -->
          <a href="{% url 'VRO_App1:logout' %}" 
             data-logout-link
             class="block p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700">
            Log Out
          </a>
        </nav>
      </div>
    </div>
  </div>
</div>















