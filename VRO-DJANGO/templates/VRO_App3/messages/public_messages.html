{% extends 'base.html' %}
{% load static %}

{% block title %}Contact Messages - Site Admin{% endblock %}

{% block content %}
{% csrf_token %}
<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-blue-900">Contact Messages</h1>
        <p class="mt-2 text-gray-600">Manage messages received through the public contact form</p>
    </div>

    <!-- Message Controls -->
    <div class="mb-6 flex justify-between items-center">
        <div class="flex gap-4">
            <select id="messageFilter" class="border border-blue-900 rounded-md px-3 py-2 w-64">
                <option value="all">All Messages</option>
                <option value="unread">Unread</option>
                <option value="read">Read</option>
                <option value="replied">Replied</option>
            </select>
            <input type="text" 
                   id="messageSearch" 
                   placeholder="Search messages..." 
                   class="border border-blue-900 rounded-md px-3 py-2 w-64">
        </div>
    </div>

    <!-- Messages List -->
    <div class="bg-white rounded-lg shadow overflow-hidden border border-gray-300">
        <div class="divide-y divide-gray-200">
            {% for message in messages %}
                <div class="p-6 {% if not message.mail_read %}bg-blue-50{% endif %} border-b border-gray-200" data-message-id="{{ message.id }}">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-semibold text-blue-900">{{ message.subject }}</h3>
                            <p class="text-sm text-gray-600">From: {{ message.first_name }} {{ message.last_name }}</p>
                            <p class="text-sm text-gray-600">Email: {{ message.email }}</p>
                            {% if message.organisation %}
                                <p class="text-sm text-gray-600">Organisation: {{ message.organisation }}</p>
                            {% endif %}
                            <p class="text-sm text-gray-600">Received: {{ message.submission_time|date:"d/m/Y H:i" }}</p>
                        </div>
                        <div class="flex gap-2">
                            <a href="{% url 'VRO_App3:view_message' message.id %}" 
                               class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700 border border-blue-900">
                                View & Reply
                            </a>
                            <button onclick="deleteMessage({{ message.id }})"
                                    class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 border border-red-600">
                                Delete
                            </button>
                        </div>
                    </div>
                    <div class="mt-4">
                        <p class="text-gray-700">{{ message.message|truncatewords:50 }}</p>
                    </div>
                    {% if message.have_replied %}
                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                                Replied: {{ message.have_replied|date:"d/m/Y H:i" }}
                            </span>
                        </div>
                    {% endif %}
                </div>
            {% empty %}
                <div class="p-6 text-center text-gray-500 border-b border-gray-200">
                    No messages available
                </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
    function deleteMessage(messageId) {
        fetch(`/site-admins/messages/delete/${messageId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                // Find and remove the message element
                const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
                if (messageElement) {
                    messageElement.remove();
                    
                    // Check if there are any messages left
                    const messagesList = document.querySelector('.divide-y');
                    if (!messagesList.querySelector('[data-message-id]')) {
                        // If no messages left, show the empty state
                        messagesList.innerHTML = `
                            <div class="p-6 text-center text-gray-500 border-b border-gray-200">
                                No messages available
                            </div>`;
                    }
                }
            } else {
                throw new Error('Failed to delete message');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete message. Please try again.');
        });
    }
</script>
{% endblock %}












