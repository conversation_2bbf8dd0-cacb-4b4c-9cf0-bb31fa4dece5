{% extends "base.html" %}
{% load static %}

{% block title %}Internal Messages{% endblock %}

{% block content %}
<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-blue-900">Internal Messages</h1>
            <p class="mt-2 text-gray-600">Communicate with other users within the system</p>
        </div>
        <a href="{% url 'VRO_App3:send_internal_message' %}" 
           class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700">
            Send New Message
        </a>
    </div>

    <!-- Messages List -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="divide-y divide-gray-200">
            {% for message in messages %}
                <div class="p-6 {% if not message.read_at %}bg-blue-50{% endif %}">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-semibold text-blue-900">{{ message.subject }}</h3>
                            <p class="text-sm text-gray-600">From: {{ message.sender.userprofile.get_full_name }}</p>
                            <p class="text-sm text-gray-600">Sent: {{ message.sent_at|date:"d/m/Y H:i" }}</p>
                        </div>
                        <a href="{% url 'VRO_App3:view_internal_message' message.id %}" 
                           class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700">
                            View Message
                        </a>
                    </div>
                    <div class="mt-4">
                        <p class="text-gray-700">{{ message.message|truncatewords:50 }}</p>
                    </div>
                </div>
            {% empty %}
                <div class="p-6 text-center text-gray-500">
                    No messages available
                </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
