{% extends "base.html" %}
{% load static %}

{% block title %}Send Internal Message{% endblock %}

{% block content %}
<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-blue-900">Send Internal Message</h1>
        <p class="mt-2 text-gray-600">Send a message to another user in the system</p>
    </div>

    <!-- Message Form -->
    <div class="bg-white rounded-lg shadow p-6">
        <form method="POST" class="space-y-6">
            {% csrf_token %}
            
            <!-- Recipient Selection -->
            <div>
                <label for="recipient" class="block text-sm font-medium text-gray-700">Recipient</label>
                <select name="recipient" id="recipient" required
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">Select a recipient</option>
                    {% for user in users %}
                        <option value="{{ user.id }}">{{ user.userprofile.get_full_name }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Subject -->
            <div>
                <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
                <input type="text" name="subject" id="subject" required
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>

            <!-- Message -->
            <div>
                <label for="message" class="block text-sm font-medium text-gray-700">Message</label>
                <textarea name="message" id="message" rows="6" required
                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
            </div>

            <!-- Submit and Cancel Buttons -->
            <div class="flex justify-end space-x-4">
                <a href="{% url 'VRO_App3:internal_messages' %}" 
                   class="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400 transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                    Send Message
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
