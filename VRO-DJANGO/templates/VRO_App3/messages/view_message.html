{% extends "base.html" %}
{% load static %}

{% block title %}View Message - Site Admin{% endblock %}

{% block content %}
<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="mb-6">
            <a href="{% url 'VRO_App3:public_messages' %}" 
               class="text-blue-900 hover:underline">&larr; Back to Messages</a>
        </div>
        
        <!-- Message Details -->
        <div class="mb-8">
            <h1 class="text-2xl font-bold text-blue-900 mb-4">{{ message.subject }}</h1>
            <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                <p><strong>From:</strong> {{ message.first_name }} {{ message.last_name }}</p>
                <p><strong>Email:</strong> {{ message.email }}</p>
                {% if message.organisation %}
                    <p><strong>Organisation:</strong> {{ message.organisation }}</p>
                {% endif %}
                <p><strong>Received:</strong> {{ message.submission_time|date:"d/m/Y H:i" }}</p>
            </div>
        </div>

        <!-- Message Content -->
        <div class="mb-8 p-4 bg-gray-50 rounded">
            <p class="whitespace-pre-wrap">{{ message.message }}</p>
        </div>

        <!-- Reply Form -->
        {% if not message.have_replied %}
            <form method="POST" class="mt-6">
                {% csrf_token %}
                <div class="mb-4">
                    <label for="reply" class="block text-sm font-medium text-gray-700 mb-2">
                        Your Reply
                    </label>
                    <textarea name="reply" 
                              id="reply" 
                              rows="6" 
                              class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                              required></textarea>
                </div>
                <button type="submit" 
                        class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Send Reply
                </button>
            </form>
        {% else %}
            <div class="bg-green-50 border border-green-200 rounded p-4">
                <p class="text-green-800">
                    Replied on {{ message.have_replied|date:"d/m/Y H:i" }}
                </p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}