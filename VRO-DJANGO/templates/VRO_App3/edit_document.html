{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="max-w-screen-xl mx-auto px-4 py-8">
    <div class="border border-blue-900 rounded-lg p-6 bg-white">
        <h1 class="text-3xl font-bold text-blue-900 mb-8">Edit {{ document.category.name }}</h1>
        
        <form method="POST" enctype="multipart/form-data" class="space-y-6">
            {% csrf_token %}
            
            {% for field in form %}
            <div class="space-y-2">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                    <div class="text-red-500 text-sm">{{ field.errors }}</div>
                {% endif %}
            </div>
            {% endfor %}
            
            <div class="flex gap-4">
                <button type="submit" class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-700">
                    Save Changes
                </button>
                <a href="{% url 'VRO_App3:manage_articles' %}" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}