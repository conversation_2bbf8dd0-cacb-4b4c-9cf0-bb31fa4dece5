<!DOCTYPE html>
<html>
<head>
    <title>Debug Page</title>
</head>
<body>

{% extends "base.html" %}
{% load static %}

{% block title %}Site Admin Dashboard{% endblock %}

{% block content %}

    <div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header with Profile Link -->
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-blue-900">Site Admin Dashboard</h1>
                <p class="mt-2 text-gray-600">Welcome back, {{ user.userprofile.get_full_name }}</p>
            </div>

        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <a href="{% url 'VRO_App3:manage_users' %}" 
               class="bg-white p-6 rounded-lg shadow-md border border-blue-900 hover:bg-blue-50 transition-colors cursor-pointer">
                <h3 class="text-lg font-semibold text-blue-900 mb-2">Total Users</h3>
                <p class="text-3xl font-bold">{{ debug_info.total_users|default:"0" }}</p>
            </a>
            <a href="{% url 'VRO_App3:public_messages' %}" 
               class="bg-white p-6 rounded-lg shadow-md border border-blue-900 hover:bg-blue-50 transition-colors cursor-pointer">
                <h3 class="text-lg font-semibold text-blue-900 mb-2">Unread Public Messages</h3>
                <p class="text-3xl font-bold">{{ debug_info.unread_messages|default:"0" }}</p>
            </a>
            <a href="{% url 'VRO_App3:internal_messages' %}" 
               class="bg-white p-6 rounded-lg shadow-md border border-blue-900 hover:bg-blue-50 transition-colors cursor-pointer">
                <h3 class="text-lg font-semibold text-blue-900 mb-2">Unread Internal Messages</h3>
                <p class="text-3xl font-bold">{{ debug_info.unread_internal_messages|default:"0" }}</p>
            </a>
        </div>


    </div>
{% endblock %}

</body>
</html>






