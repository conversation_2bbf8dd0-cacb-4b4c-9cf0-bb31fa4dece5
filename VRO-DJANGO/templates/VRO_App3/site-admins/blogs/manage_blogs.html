{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="max-w-screen-xl mx-auto px-4 py-8">
    <div class="border-2 border-blue-900 rounded-lg p-6 bg-white shadow-lg">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-blue-900">Manage Blogs</h1>
            <a href="{% url 'VRO_App3:upload_document' 'blog' %}" 
               class="bg-blue-900 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Upload New Blog Post
            </a>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Author</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for blog in blogs %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ blog.title }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ blog.author|default:"" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ blog.posted_date|date:"d/m/Y"|default:"" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex space-x-2">
                                <a href="{% url 'VRO_App3:edit_document' blog.id %}" 
                                   class="bg-blue-900 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors">
                                    Edit
                                </a>
                                <a href="{% url 'VRO_App3:download_document' blog.id %}" 
                                   class="bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700 transition-colors">
                                    Download
                                </a>
                                <form method="POST" 
                                      action="{% url 'VRO_App3:upload_document_version' blog.id %}"
                                      enctype="multipart/form-data"
                                      class="inline-block">
                                    {% csrf_token %}
                                    <input type="file" 
                                           name="document" 
                                           accept=".docx"
                                           class="hidden"
                                           id="file-{{ blog.id }}"
                                           onchange="this.form.submit()">
                                    <label for="file-{{ blog.id }}"
                                           class="bg-yellow-600 text-white px-3 py-1 rounded hover:bg-yellow-700 transition-colors cursor-pointer">
                                        Upload New
                                    </label>
                                </form>
                                <a href="{% url 'VRO_App3:delete_document' blog.id %}" 
                                   class="bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 transition-colors"
                                   onclick="return confirm('Are you sure you want to delete this blog post?')">
                                    Delete
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">No blog posts found</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
