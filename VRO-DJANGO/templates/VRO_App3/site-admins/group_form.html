{% extends "base.html" %}
{% load static %}

{% block title %}{{ action }} Group - Visual Reading Online{% endblock %}

{% block content %}
<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-blue-900 mb-8">{{ action }} Group</h1>

    <div class="bg-white p-6 rounded-lg shadow-md border border-blue-900 mb-8">
        <form method="POST" class="space-y-4">
            {% csrf_token %}
            
            <div>
                <label class="block text-sm font-medium text-gray-700">Name</label>
                {{ form.name }}
                {% if form.name.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Code</label>
                {{ form.code }}
                {% if form.code.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.code.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Description</label>
                {{ form.description }}
                {% if form.description.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label class="flex items-center">
                    {{ form.is_active }}
                    <span class="ml-2 text-sm text-gray-700">Active</span>
                </label>
            </div>

            <div class="flex justify-end space-x-4">
                <a href="{% url 'VRO_App3:manage_groups' %}" 
                   class="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400 transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                    {{ action }} Group
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
