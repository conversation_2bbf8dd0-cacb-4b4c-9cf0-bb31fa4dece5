{% extends "base.html" %}
{% load static %}

{% block title %}Manage Pages - Visual Reading Online{% endblock %}

{% block content %}
<div class="max-w-screen-xl mx-2 xl:mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-blue-900 mb-8">Manage Pages</h1>

    <div class="bg-white shadow-lg rounded-lg overflow-hidden border-2 border-blue-900">
        <ul class="divide-y divide-gray-200">
            {% for page in pages %}
            <li class="hover:bg-gray-50">
                <div class="px-6 py-6">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-blue-900">{{ page.title }}</h2>
                        <div class="flex space-x-4">
                            <a href="{% url 'VRO_App3:download_page_document' page.url_name %}"
                               class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                                Download
                            </a>
                            
                            <form method="POST" 
                                  action="{% url 'VRO_App3:upload_page_document' page.url_name %}"
                                  enctype="multipart/form-data"
                                  class="flex items-center space-x-2">
                                {% csrf_token %}
                                <input type="file" 
                                       name="document" 
                                       accept=".docx"
                                       class="hidden"
                                       id="file-{{ page.url_name }}"
                                       onchange="this.form.submit()">
                                <label for="file-{{ page.url_name }}"
                                       class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors cursor-pointer">
                                    Upload New Version
                                </label>
                            </form>
                        </div>
                    </div>
                    <div class="mt-2 text-sm text-gray-600">
                        Last updated: {{ page.updated_at|date:"F j, Y, g:i a" }}
                    </div>
                </div>
            </li>
            {% endfor %}
        </ul>
    </div>
</div>
{% endblock %}
