{% extends "base.html" %}
{% load static %}

{% block title %}Edit User{% endblock %}

{% block content %}
<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-md border border-blue-900">
        <div class="p-6 border-b border-blue-900">
            <h1 class="text-3xl font-bold text-blue-900">Edit User</h1>
        </div>
        
        <div class="p-6">
            <form method="POST" class="space-y-6">
                {% csrf_token %}
                
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <div class="mt-1">
                            <input type="email" name="email" value="{{ edit_user.email }}" 
                                   class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">First Name</label>
                        <div class="mt-1">
                            <input type="text" name="first_name" value="{{ edit_user.userprofile.first_name }}" 
                                   class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Last Name</label>
                        <div class="mt-1">
                            <input type="text" name="last_name" value="{{ edit_user.userprofile.last_name }}" 
                                   class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Group</label>
                        <div class="mt-1">
                            <select name="group" 
                                    class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                <option value="">-- No Group --</option>
                                {% for group in available_groups %}
                                <option value="{{ group.id }}" 
                                        {% if edit_user.userprofile.group_id == group.id %}selected{% endif %}>
                                    {{ group.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        {% if edit_user.userprofile.group %}
                        <p class="mt-2 text-sm text-gray-500">
                            Current group: {{ edit_user.userprofile.group.name }}
                        </p>
                        {% endif %}
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Login Access</label>
                        <div class="mt-1">
                            <select name="is_active" 
                                    class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                <option value="1" {% if edit_user.is_active %}selected{% endif %}>Allow Login</option>
                                <option value="0" {% if not edit_user.is_active %}selected{% endif %}>Disable Login</option>
                            </select>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">
                            {% if edit_user.is_active %}
                            User can currently log in. Setting to "Disable Login" will prevent the user from accessing their account.
                            {% else %}
                            Login is currently disabled - user cannot access their account
                            {% endif %}
                        </p>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    <a href="{% url 'VRO_App3:manage_users' %}" 
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

