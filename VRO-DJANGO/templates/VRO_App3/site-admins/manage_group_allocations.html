{% extends "base.html" %}
{% load static %}

{% block title %}Manage Group Allocations - Visual Reading Online{% endblock %}

{% block content %}
<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-blue-900 mb-8">Manage Group Allocations</h1>

    <!-- Add New Allocation Form -->
    <div class="bg-white p-6 rounded-lg shadow-md border border-blue-900 mb-8">
        <h2 class="text-xl font-bold text-blue-900 mb-4">Add New Allocation</h2>
        <form method="POST" class="space-y-4">
            {% csrf_token %}
            <div>
                <label class="block text-sm font-medium text-gray-700">Email</label>
                {{ form.email }}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Group</label>
                {{ form.group }}
            </div>
            <button type="submit" class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                Add Allocation
            </button>
        </form>
    </div>

    <!-- Existing Allocations Table -->
    <div class="bg-white p-6 rounded-lg shadow-md border border-blue-900">
        <h2 class="text-xl font-bold text-blue-900 mb-4">Existing Allocations</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Group</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for allocation in allocations %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ allocation.email }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ allocation.group.name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if allocation.is_used %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                Used
                            </span>
                            {% else %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                Pending
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ allocation.created_at|date:"Y-m-d H:i" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if allocation.created_by %}
                                {{ allocation.created_by.email }}
                            {% else %}
                                <span class="text-gray-400">Unknown</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if allocation.email != current_user_email %}
                                <button onclick="confirmDelete('{{ allocation.id }}', '{{ allocation.email }}', {{ allocation.is_used|yesno:'true,false' }})"
                                        class="{% if allocation.is_used %}text-red-600 hover:text-red-900{% else %}text-red-600 hover:text-red-900{% endif %} font-medium">
                                    Delete{% if allocation.is_used %} Access{% endif %}
                                </button>
                                <form id="deleteForm{{ allocation.id }}" method="POST" action="{% url 'VRO_App3:delete_group_allocation' allocation.id %}" class="hidden">
                                    {% csrf_token %}
                                </form>
                            {% else %}
                                <span class="text-gray-400 italic">Cannot delete own allocation</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500 italic">No allocations found</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(allocationId, email, isUsed) {
        const title = isUsed ? 'Confirm Access Removal' : 'Confirm Deletion';
        const message = isUsed 
            ? `This will remove group access for ${email} and they will no longer be able to access this group's resources. Are you sure?`
            : `Are you sure you want to delete the pending allocation for ${email}?`;

        showConfirmModal(title, message, () => {
            document.getElementById(`deleteForm${allocationId}`).submit();
        });
    }
</script>
{% endblock %}


