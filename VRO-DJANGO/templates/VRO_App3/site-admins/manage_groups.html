{% extends "base.html" %}
{% load static %}

{% block title %}Manage Groups - Visual Reading Online{% endblock %}

{% block content %}
<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-blue-900 mb-8">Manage Groups</h1>

    <!-- Add New Group Button -->
    <div class="mb-8">
        <a href="{% url 'VRO_App3:create_group' %}" 
           class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
            Add New Group
        </a>
    </div>

    <!-- Groups Table -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for group in groups %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">{{ group.name }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ group.code }}</td>
                    <td class="px-6 py-4">{{ group.description|default:"-" }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if group.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                            {{ group.is_active|yesno:"Active,Inactive" }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <a href="{% url 'VRO_App3:edit_group' group.id %}" class="text-blue-600 hover:text-blue-900 mr-4">Edit</a>
                        <button onclick="confirmDelete('{{ group.id }}', '{{ group.name }}')"
                                class="text-red-600 hover:text-red-900">
                            Delete
                        </button>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                        No groups found. Click "Add New Group" to create one.
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

{% block extra_js %}
<script>
    function confirmDelete(groupId, groupName) {
        showConfirmModal(
            'Confirm Deletion',
            `Are you sure you want to delete the group "${groupName}"? This action cannot be undone.`,
            () => {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/site-admins/groups/${groupId}/delete/`;
                
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrfmiddlewaretoken';
                csrfInput.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
                
                form.appendChild(csrfInput);
                document.body.appendChild(form);
                form.submit();
            }
        );
    }
</script>
{% endblock %}
{% endblock %}