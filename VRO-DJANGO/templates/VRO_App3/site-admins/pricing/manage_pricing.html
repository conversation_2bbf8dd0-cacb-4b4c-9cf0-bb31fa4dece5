{% extends 'base.html' %}
{% load pricing_tags %}

{% block content %}
<div class="max-w-screen-xl mx-2 xl:mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-blue-900 mb-8">Manage Pricing Cards</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for card in pricing_cards %}
        <div class="bg-white shadow-lg rounded-lg overflow-hidden border-2 border-blue-900">
            <div class="p-6">
                <h3 class="text-xl font-semibold mb-2">{{ card.Card_Title }}</h3>
                <p class="text-gray-600 mb-2">{{ card.Card_Sub_Title }}</p>
                <p class="text-2xl font-bold mb-4">{{ card.Price|format_price }}</p>
                
                <div class="space-y-2 mb-4">
                    {% if card.Point1 %}<p>• {{ card.Point1 }}</p>{% endif %}
                    {% if card.Point2 %}<p>• {{ card.Point2 }}</p>{% endif %}
                    {% if card.Point3 %}<p>• {{ card.Point3 }}</p>{% endif %}
                </div>
                
                <div class="mt-4">
                    <p class="text-sm text-gray-600">Button: {{ card.Card_Button_Text }}</p>
                </div>
                
                <div class="mt-4 flex justify-end">
                    <a href="{% url 'VRO_App3:edit_pricing_card' card.Card_ID %}" 
                       class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                        Edit
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}
