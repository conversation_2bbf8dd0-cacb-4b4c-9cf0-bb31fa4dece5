{% extends "base.html" %}
{% load static %}

{% block title %}{{ action }} {{ item_type }} - Visual Reading Online{% endblock %}

{% block content %}
<div class="max-w-screen-xl mx-2 xl:mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-blue-900 mb-8">{{ action }} {{ item_type }}</h1>

    <div class="max-w-2xl mx-auto">
        <div class="bg-white p-6 rounded-lg shadow-lg border-2 border-blue-900">
            <form method="POST" class="space-y-4">
                {% csrf_token %}
                
                <div>
                    <label class="block text-sm font-medium text-gray-700">Name</label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">Description</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label class="flex items-center">
                        {{ form.is_active }}
                        <span class="ml-2 text-sm text-gray-700">Active</span>
                    </label>
                </div>

                <div class="flex justify-end space-x-4 pt-4">
                    <a href="{% if item_type == 'Gender' %}{% url 'VRO_App3:manage_genders' %}{% else %}{% url 'VRO_App3:manage_races' %}{% endif %}" 
                       class="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                        {{ action }} {{ item_type }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
