{% extends "base.html" %}
{% load static %}

{% block title %}Manage Genders - Visual Reading Online{% endblock %}

{% block content %}
<div class="max-w-screen-xl mx-2 xl:mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-blue-900">Manage Genders</h1>
        <a href="{% url 'VRO_App3:create_gender' %}" 
           class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
            Add New Gender
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-lg overflow-hidden border-2 border-blue-900">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for gender in genders %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">{{ gender.name }}</td>
                    <td class="px-6 py-4">{{ gender.description|default:"-" }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if gender.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                            {{ gender.is_active|yesno:"Active,Inactive" }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <a href="{% url 'VRO_App3:edit_gender' gender.id %}" class="text-blue-600 hover:text-blue-900">Edit</a>
                        <a href="{% url 'VRO_App3:delete_gender' gender.id %}" 
                           class="ml-3 text-red-600 hover:text-red-900"
                           onclick="return confirm('Are you sure you want to delete this gender?')">Delete</a>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="4" class="px-6 py-4 text-center text-gray-500">No genders found</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
