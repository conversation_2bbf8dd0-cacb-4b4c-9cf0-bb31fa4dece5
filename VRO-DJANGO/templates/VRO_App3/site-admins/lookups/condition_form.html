{% extends "base.html" %}
{% load static %}

{% block title %}{% if condition %}Edit{% else %}Create{% endif %} Reading Condition - Visual Reading Online{% endblock %}

{% block content %}
<div class="max-w-screen-xl mx-2 xl:mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-blue-900">
            {% if condition %}Edit{% else %}Create{% endif %} Reading Condition
        </h1>
    </div>

    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6 border-2 border-blue-900">
            <form method="POST">
                {% csrf_token %}
                <div class="space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                        <input type="text" 
                               name="name" 
                               id="name" 
                               value="{{ condition.name|default:'' }}"
                               required
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea name="description" 
                                  id="description" 
                                  rows="3"
                                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ condition.description|default:'' }}</textarea>
                    </div>

                    <div>
                        <label class="inline-flex items-center">
                            <input type="checkbox" 
                                   name="is_active" 
                                   {% if condition.is_active|default:True %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Active</span>
                        </label>
                    </div>

                    <div class="flex justify-end space-x-4 pt-4">
                        <a href="{% url 'VRO_App3:manage_conditions' %}" 
                           class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                            {% if condition %}Update{% else %}Create{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
