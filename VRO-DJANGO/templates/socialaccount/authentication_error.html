{% extends "base.html" %}
{% load static %}

{% block title %}Login Failed - Visual Reading Online{% endblock %}

{% block content %}
<div class="max-w-screen-xl px-0 py-0 mx-auto sm:py-0 lg:py-0">
  <div class="p-0 mt-0 mb-8 md:p-0">
    <div class="py-4">
      <div class="max-w-screen-xl px-0 mx-auto lg:px-0">
        <div class="grid gap-8 xl:grid-cols-2 xl:gap-12">
          <!-- Image Section -->
          <div>
            <div class="h-64 overflow-hidden rounded-lg md:h-auto">
              <img
                src="{% static 'images/error.jpg' %}"
                loading="lazy"
                alt="Login Error"
                class="object-cover object-center w-full h-full" />
            </div>
          </div>

          <!-- Content Section -->
          <div class="md:pt-2">
            <h2 class="mb-4 text-2xl font-bold text-blue-900 md:text-3xl">
              Social Network Login Failed
            </h2>

            <div class="prose max-w-none">
              <p class="text-blue-900 mb-6">
                We encountered an error while attempting to log you in through your social network account. This could be due to:
              </p>
              
              <ul class="list-disc list-inside text-blue-900 mb-6">
                <li>Temporary connection issues</li>
                <li>Account permissions not granted</li>
                <li>Authentication service disruption</li>
              </ul>

              <p class="text-blue-900 mb-8">
                Please try again or use an alternative login method.
              </p>
            </div>

            <div class="flex flex-col sm:flex-row gap-4">
              <a href="{% url 'account_login' %}" 
                 class="inline-flex justify-center items-center px-6 py-3 bg-blue-900 text-white font-medium rounded-md hover:bg-blue-700 transition-colors duration-200">
                Try Again
              </a>
              
              <a href="{% url 'VRO_App1:index' %}" 
                 class="inline-flex justify-center items-center px-6 py-3 border border-blue-900 text-blue-900 font-medium rounded-md hover:bg-blue-100 transition-colors duration-200">
                Return to Home
              </a>
            </div>

            <div class="mt-8 p-4 bg-yellow-50 rounded-md border border-yellow-200">
              <p class="text-sm text-blue-900">
                <strong>Need help?</strong> If you continue to experience issues, please contact our support team for assistance.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}