{% load static %}

<!-- HEADER AND MENU -->
<div class="mt-2 xl:mt-4 bg-blue-900 py-0.5 max-w-screen-xl mx-2 xl:mx-auto rounded">
  <img
    alt="Visual Reading Online Header"
    src="{% static 'images/vro-heading.png' %}"
    class="mx-auto"
  />
</div>

<!-- Navigation wrapper with Alpine.js state management -->
<div x-data="{ mobileMenuOpen: false, resourcesDropdownOpen: false }">
  <!-- Desktop Menu (md and above) -->
  <div class="mx-2 mt-1">
    <div class="max-w-screen-xl px-0 mx-auto md:px-0">
      <!-- Main Navigation Bar -->
      <div class="hidden mx-auto mt-0 text-lg bg-blue-900 rounded-md text-yellow-50 xl:block">
        <div class="flex justify-around items-center font-bold gap-0.5 p-0.5">
          <!-- Home -->
          <a href="{% url 'VRO_App1:index' %}" 
             class="nav-link {% if request.resolver_match.url_name == 'index' %}nav-link-active{% else %}nav-link-inactive{% endif %}">
            Home
          </a>

          <!-- Learn More -->
          <a href="{% url 'VRO_App1:learn_more' %}" 
             class="nav-link {% if request.resolver_match.url_name == 'learn_more' %}nav-link-active{% else %}nav-link-inactive{% endif %}">
            Learn More
          </a>

          <!-- Get Started -->
          <a href="{% url 'VRO_App1:get_started' %}" 
             class="nav-link {% if request.resolver_match.url_name == 'get_started' %}nav-link-active{% else %}nav-link-inactive{% endif %}">
            Get Started
          </a>

          <!-- Resources Dropdown -->
          <div class="relative w-full" @mouseleave="resourcesDropdownOpen = false">
            <button
              @mouseover="resourcesDropdownOpen = true"
              class="nav-link {% if request.resolver_match.url_name in 'articles_and_blogs,articles,blog_posts,blog_demo,our_results,pay_per_view,presentations,our_products_and_services,social_media,student_feedback,useful_links,youtube,word_test,faqs,dr_cooper' %}nav-link-active{% else %}nav-link-inactive{% endif %}"
              type="button"
            >
              Resources
            </button>

            <!-- Dropdown menu -->
            <div
              x-show="resourcesDropdownOpen"
              x-transition:enter="transition ease-out duration-200"
              x-transition:enter-start="opacity-0 transform -translate-y-2"
              x-transition:enter-end="opacity-100 transform translate-y-0"
              x-transition:leave="transition ease-in duration-150"
              x-transition:leave-start="opacity-100 transform translate-y-0"
              x-transition:leave-end="opacity-0 transform -translate-y-2"
              class="absolute z-10 w-60 bg-blue-900 divide-y divide-blue-700 rounded-lg shadow-lg"
              @mouseover="resourcesDropdownOpen = true"
            >
              <ul class="py-2 text-yellow-50">
                <li>
                  <a href="{% url 'VRO_App1:dr_cooper' %}" 
                     class="dropdown-item {% if request.resolver_match.url_name == 'dr_cooper' %}dropdown-item-active{% endif %}">
                     About Dr. Cooper
                  </a>
                </li>
                <li>
                  <a href="{% url 'VRO_App1:articles_and_blogs' %}" 
                     class="dropdown-item {% if request.resolver_match.url_name in 'articles_and_blogs,articles,blog_posts' %}dropdown-item-active{% endif %}">
                     Articles & Blogs
                  </a>
                </li>
                <li>
                  <a href="{% url 'VRO_App1:faqs' %}" 
                     class="dropdown-item {% if request.resolver_match.url_name == 'faqs' %}dropdown-item-active{% endif %}">
                     FAQs
                  </a>
                </li>
                <li>
                  <a href="{% url 'VRO_App1:our_results' %}" 
                     class="dropdown-item {% if request.resolver_match.url_name == 'our_results' %}dropdown-item-active{% endif %}">
                     Our Results
                  </a>
                </li>
                <li>
                  <a href="{% url 'VRO_App1:products' %}" 
                     class="dropdown-item {% if request.resolver_match.url_name == 'products' %}dropdown-item-active{% endif %}">
                     Products & Services
                  </a>
                </li>
                <li>
                  <a href="{% url 'VRO_App1:student_feedback' %}" 
                     class="dropdown-item {% if request.resolver_match.url_name == 'student_feedback' %}dropdown-item-active{% endif %}">
                     Student Feedback
                  </a>
                </li>
                <li>
                  <a href="{% url 'VRO_App1:useful_links' %}" 
                     class="dropdown-item {% if request.resolver_match.url_name == 'useful_links' %}dropdown-item-active{% endif %}">
                     Useful Links
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Support -->
          <a href="{% url 'VRO_App1:support' %}" 
             class="nav-link {% if request.resolver_match.url_name == 'support' %}nav-link-active{% else %}nav-link-inactive{% endif %}">
            Support
          </a>

          <!-- Join Us -->
          <a href="{% url 'VRO_App1:signup' %}" 
             class="nav-link {% if request.resolver_match.url_name == 'signup' %}nav-link-active{% else %}nav-link-inactive{% endif %}">
            Join Us
          </a>

          <!-- Log In -->
          <a href="{% url 'VRO_App1:login' %}" 
             class="nav-link {% if request.resolver_match.url_name == 'login' %}nav-link-active{% else %}nav-link-inactive{% endif %}">
            Log In
          </a>
        </div>
      </div>

      <!-- Mobile Menu Button -->
      <div class="xl:hidden">
        <button
          @click="mobileMenuOpen = !mobileMenuOpen"
          class="w-full p-3 mb-2 text-base font-bold text-yellow-50 bg-blue-900 rounded-md hover:bg-blue-700"
        >
          Main Menu
        </button>
      </div>

      <!-- Mobile Menu -->
      <div
        x-show="mobileMenuOpen"
        x-transition:enter="transition ease-out duration-200"
        x-transition:enter-start="opacity-0 transform -translate-y-2"
        x-transition:enter-end="opacity-100 transform translate-y-0"
        x-transition:leave="transition ease-in duration-150"
        x-transition:leave-start="opacity-100 transform translate-y-0"
        x-transition:leave-end="opacity-0 transform -translate-y-2"
        class="xl:hidden absolute z-50 w-full left-0 right-0"
      >
        <div class="mt-2 bg-blue-900 rounded-md mx-2">
          <nav class="flex flex-col">
            <!-- Mobile menu items -->
            <a href="{% url 'VRO_App1:index' %}" 
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'index' %}bg-blue-700{% endif %}">
               Home
            </a>
            <a href="{% url 'VRO_App1:learn_more' %}" 
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'learn_more' %}bg-blue-700{% endif %}">
               Learn More
            </a>
            <a href="{% url 'VRO_App1:get_started' %}" 
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'get_started' %}bg-blue-700{% endif %}">
               Get Started
            </a>

            <!-- Mobile Resources Section -->
            <div x-data="{ mobileResourcesOpen: false }">
              <button
                @click="mobileResourcesOpen = !mobileResourcesOpen"
                class="flex items-center justify-between w-full p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name in 'articles_and_blogs,articles,blog_posts,blog_demo,our_results,pay_per_view,presentations,our_products_and_services,social_media,student_feedback,useful_links,youtube,word_test' %}bg-blue-700{% endif %}"
              >
                <span>Resources</span>
                <svg
                  class="w-5 h-5 transition-transform"
                  :class="{ 'rotate-180': mobileResourcesOpen }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>

              <div x-show="mobileResourcesOpen" class="bg-blue-800">
                <a href="{% url 'VRO_App1:dr_cooper' %}" 
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'dr_cooper' %}bg-blue-700{% endif %}">
                   About Dr. Cooper
                </a>
                <a href="{% url 'VRO_App1:articles_and_blogs' %}" 
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name in 'articles_and_blogs,articles,blog_posts' %}bg-blue-700{% endif %}">
                   Articles & Blogs
                </a>
                <a href="{% url 'VRO_App1:faqs' %}" 
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'faqs' %}bg-blue-700{% endif %}">
                   FAQs
                </a>
                <a href="{% url 'VRO_App1:our_results' %}" 
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'our_results' %}bg-blue-700{% endif %}">
                   Our Results
                </a>
                <a href="{% url 'VRO_App1:products' %}" 
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'products' %}bg-blue-700{% endif %}">
                   Products & Services
                </a>
                <a href="{% url 'VRO_App1:student_feedback' %}" 
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'student_feedback' %}bg-blue-700{% endif %}">
                   Student Feedback
                </a>
                <a href="{% url 'VRO_App1:useful_links' %}" 
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'useful_links' %}bg-blue-700{% endif %}">
                   Useful Links
                </a>
              </div>
            </div>

            <a href="{% url 'VRO_App1:support' %}" 
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'support' %}bg-blue-700{% endif %}">
               Support
            </a>
            <a href="{% url 'VRO_App1:signup' %}" 
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'signup' %}bg-blue-700{% endif %}">
               Join Us
            </a>
            <a href="{% url 'VRO_App1:login' %}" 
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'login' %}bg-blue-700{% endif %}">
               Log In
            </a>
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- END OF HEADER AND MENU -->
