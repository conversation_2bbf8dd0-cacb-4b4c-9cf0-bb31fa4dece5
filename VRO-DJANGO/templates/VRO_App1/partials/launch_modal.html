<div id="launchModal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
    <!-- Modal backdrop -->
    <div class="fixed inset-0 bg-black opacity-50"></div>
    
    <!-- Modal content -->
    <div class="relative z-50 w-full max-w-md p-6 bg-white rounded-lg shadow-xl">
        <div class="text-center">
            <h3 class="mb-4 text-xl font-bold text-blue-900">Website Launch Notice</h3>
            <p class="mb-6 text-gray-600">
                We apologise for the delay, but we are not yet ready to accept new signups or logins. 
                We expect our website to be fully live by the 13th March 2025.
            </p>
            <button
                onclick="closeModal()"
                class="px-6 py-2 text-white bg-blue-900 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
                Okay
            </button>
        </div>
    </div>
</div>

<script>
    function showModal() {
        document.getElementById('launchModal').classList.remove('hidden');
    }

    function closeModal() {
        document.getElementById('launchModal').classList.add('hidden');
    }

    // Show modal when page loads
    document.addEventListener('DOMContentLoaded', showModal);
</script>