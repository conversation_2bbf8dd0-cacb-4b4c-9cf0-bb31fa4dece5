{% extends 'base.html' %}
{% load static %}

{% block title %}Articles - Visual Reading Online{% endblock %}

{% block content %}
<div class="max-w-screen-xl mx-auto px-4 py-8">
    <div class="border border-blue-900 rounded-lg p-6 bg-white">
        <h1 class="text-3xl font-bold text-blue-900 mb-8">Articles</h1>
        
        {% if user.is_authenticated and user.groups.site-admin %}
        <div class="mb-8">
            <a href="{% url 'VRO_App3:upload_document' 'article' %}" 
               class="bg-blue-900 text-white px-4 py-2 rounded hover:bg-blue-700">
                Upload New Article
            </a>
        </div>
        {% endif %}

        <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {% for article in articles %}
                <div class="border border-blue-900 rounded-lg p-6 bg-white shadow hover:shadow-lg">
                    <h2 class="text-xl font-semibold text-blue-900 mb-4">{{ article.title }}</h2>
                    {% if article.author %}
                        <p class="text-sm text-gray-600 mb-2">Author: {{ article.author }}</p>
                    {% endif %}
                    {% if article.publication_date %}
                        <p class="text-sm text-gray-600 mb-4">Published: {{ article.publication_date|date:"F j, Y" }}</p>
                    {% endif %}
                    <div class="flex gap-2">
                        <a href="{% url 'VRO_App1:article' article.url_name %}" 
                           class="px-3 py-1 bg-blue-900 text-white rounded-md hover:bg-blue-700">
                            Read More
                        </a>
                        {% if user.is_authenticated and user.groups.site-admin %}
                            <a href="{% url 'VRO_App3:edit_document' article.id %}" 
                               class="px-3 py-1 bg-blue-900 text-white rounded-md hover:bg-blue-700">
                                Edit
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% empty %}
                <p class="text-gray-500">No articles found.</p>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
