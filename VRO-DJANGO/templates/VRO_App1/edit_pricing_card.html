{% extends "base.html" %}
{% load pricing_tags %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">Edit Pricing Card</h1>
    
    <form method="POST" class="max-w-2xl">
        {% csrf_token %}
        <div class="space-y-4">
            <div>
                <label class="block mb-1">Title</label>
                <input type="text" name="title" value="{{ card.Card_Title }}" 
                       class="w-full border rounded px-3 py-2">
            </div>
            <div>
                <label class="block mb-1">Subtitle</label>
                <input type="text" name="subtitle" value="{{ card.Card_Sub_Title }}"
                       class="w-full border rounded px-3 py-2">
            </div>
            <div>
                <label class="block mb-1">Price (enter TBA or amount without £)</label>
                <input type="text" name="price" value="{% if card.Price == 0 %}TBA{% else %}{{ card.Price }}{% endif %}"
                       class="w-full border rounded px-3 py-2">
            </div>
            <div>
                <label class="block mb-1">Point 1</label>
                <input type="text" name="point1" value="{{ card.Point1 }}"
                       class="w-full border rounded px-3 py-2">
            </div>
            <div>
                <label class="block mb-1">Point 2</label>
                <input type="text" name="point2" value="{{ card.Point2 }}"
                       class="w-full border rounded px-3 py-2">
            </div>
            <div>
                <label class="block mb-1">Point 3</label>
                <input type="text" name="point3" value="{{ card.Point3 }}"
                       class="w-full border rounded px-3 py-2">
            </div>
            <div>
                <label class="block mb-1">Button Text</label>
                <input type="text" name="button_text" value="{{ card.Card_Button_Text }}"
                       class="w-full border rounded px-3 py-2">
            </div>
            <div>
                <label class="block mb-1">Button Link</label>
                <input type="text" name="button_link" value="{{ card.Card_Button_Link }}"
                       class="w-full border rounded px-3 py-2">
            </div>
            
            <div class="flex justify-end space-x-4">
                <a href="{% url 'manage_pricing' %}" 
                   class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Save Changes
                </button>
            </div>
        </div>
    </form>
</div>
{% endblock %}
