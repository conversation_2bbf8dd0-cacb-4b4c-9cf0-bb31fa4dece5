{% extends 'base.html' %}
{% load static %}

{% block title %}Contact Us - Visual Reading Online{% endblock %}

{% block content %}
  <div class="max-w-screen-xl px-0 mx-auto xl:px-0 sm:py-0 lg:py-2">
    <div class="p-0 mb-0 md:p-0">
      <div class="py-2 sm:py-4 lg:py-4">
        <div class="max-w-screen-xl px-0 mx-auto lg:px-0">
          <div class="grid gap-8 xl:grid-cols-2 xl:gap-12">
            <div>
              <div class="h-64 overflow-hidden rounded-lg md:h-auto">
                <img
                  src="{% static 'images/contact.jpg' %}"
                  loading="lazy"
                  alt="Contact Us"
                  class="object-cover object-center w-full h-full"
                />
              </div>
            </div>

            <div class="md:pt-2">
              <p class="max-w-screen-xl mx-auto mb-8 text-left text-blue-900 md:text-lg">
                Please contact us (after you have checked our searchable FAQs on our
                <a href="/support" class="font-bold hover:underline">Support page</a>) if you have
                any questions or issues with our website, our courses or any of our other products
                and services. We aim to respond to all messages as quickly as is possible!
              </p>

              <form
                method="post"
                id="contact-form"
                class="mx-2"
                action="{% url 'VRO_App1:contact' %}"
              >
                {% csrf_token %}

                {% if messages %}
                  <div class="mb-4">
                    {% for message in messages %}
                      {% if 'contact_form' in message.extra_tags %}
                        <div class="p-4 mb-4 text-red-700 bg-red-100 rounded-lg">{{ message }}</div>
                      {% endif %}
                    {% endfor %}
                  </div>
                {% endif %}

                {% if form.non_field_errors %}
                  <div class="mb-4">
                    {% for error in form.non_field_errors %}
                      <div class="p-4 mb-4 text-red-700 bg-red-100 rounded-lg">{{ error }}</div>
                    {% endfor %}
                  </div>
                {% endif %}

                <div class="mb-4">
                  <label for="{{ form.first_name.id_for_label }}" class="block font-semibold text-blue-900">
                    First Name
                  </label>
                  {{ form.first_name }}
                  {% if form.first_name.errors %}
                    <div class="text-red-600">{{ form.first_name.errors }}</div>
                  {% endif %}
                </div>

                <div class="mb-4">
                  <label for="{{ form.last_name.id_for_label }}" class="block font-semibold text-blue-900">
                    Last Name
                  </label>
                  {{ form.last_name }}
                  {% if form.last_name.errors %}
                    <div class="text-red-600">{{ form.last_name.errors }}</div>
                  {% endif %}
                </div>

                <div class="mb-4">
                  <label for="{{ form.organisation.id_for_label }}" class="block font-semibold text-blue-900">
                    Organisation (Optional)
                  </label>
                  {{ form.organisation }}
                  {% if form.organisation.errors %}
                    <div class="text-red-600">{{ form.organisation.errors }}</div>
                  {% endif %}
                </div>

                <div class="mb-4">
                  <label for="{{ form.subject.id_for_label }}" class="block font-semibold text-blue-900">
                    Subject
                  </label>
                  {{ form.subject }}
                  {% if form.subject.errors %}
                    <div class="text-red-600">{{ form.subject.errors }}</div>
                  {% endif %}
                </div>

                <div class="mb-4">
                  <label for="{{ form.email.id_for_label }}" class="block font-semibold text-blue-900">
                    Email
                  </label>
                  {{ form.email }}
                  {% if form.email.errors %}
                    <div class="text-red-600">{{ form.email.errors }}</div>
                  {% endif %}
                </div>

                <!-- Message field section -->
                <div class="mb-4">
                  <label for="{{ form.message.id_for_label }}" class="block font-semibold text-blue-900">
                    Your Message
                  </label>
                  {{ form.message }}
                  {% if form.message.errors %}
                    <div class="text-red-600">{{ form.message.errors }}</div>
                  {% endif %}
                </div>

                <div class="block w-full mx-auto mb-4">
                  <input
                    type="checkbox"
                    id="agree"
                    name="agree"
                    required
                    class="mb-1 mr-2 rounded"
                  />
                  <label for="agree" class="text-blue-900">
                    I agree to the Visual Reading Online
                    <a href="/terms-of-service" class="font-bold text-blue-900 hover:text-blue-800 hover:underline-offset-4 hover:underline">
                      Terms of Service
                    </a>
                    and
                    <a href="/privacy-policy" class="font-bold text-blue-900 hover:text-blue-800 hover:underline-offset-4 hover:underline">
                      Privacy Policy
                    </a>
                  </label>
                </div>

                <button
                  type="submit"
                  class="block w-full px-4 py-2 text-xl font-bold text-white bg-blue-900 rounded hover:bg-blue-700"
                >
                  Send us your message
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}
