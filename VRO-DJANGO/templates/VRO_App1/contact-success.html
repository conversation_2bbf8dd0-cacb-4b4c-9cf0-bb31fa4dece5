{% extends 'base.html' %}
{% load static %}

{% block title %}Contact Success - Visual Reading Online{% endblock %}

{% block content %}
  <div class="max-w-screen-xl px-0 mx-auto xl:px-0 sm:py-0">
    <div class="p-0 mt-0 mb-8 md:p-0">
      <!-- support intro -->
      <div class="py-1.5 sm:py-3">
        <div class="max-w-screen-xl px-0 mx-auto lg:px-0">
          <div class="grid gap-8 xl:grid-cols-2 xl:gap-12">
            <div>
              <div class="h-64 overflow-hidden rounded-lg md:h-auto">
                <img
                  src="{% static 'images/thankyou.jpg' %}"
                  loading="lazy"
                  alt="Log In To Our Website"
                  class="object-cover object-center w-full h-full"
                />
              </div>
            </div>

            <div class="md:pt-2">
              <h2
                class="mb-2 text-3xl font-extrabold leading-none tracking-tight text-left md:max-w-full lg:mb-4 md:text-4xl xl:text-4xl"
              >
                Thank you!
              </h2>

              <p class="my-6 text-blue-900 text-md md:text-lg">We have received your message.</p>

              <p class="my-6 text-blue-900 text-md md:text-lg">
                We will do our best to respond to you as soon as possible, in the meantime please
                check our FAQs on our support page.
              </p>

              <p class="my-6 text-blue-900 text-md md:text-lg">
                Please note that all of our products and services are managed via your user account
                on this website.
              </p>

              <p class="my-6 text-blue-900 text-md md:text-lg">
                You should have received a welcome email from us when you made a purchase from us or
                were enrolled via the DSA process.
              </p>

              <p class="my-6 text-blue-900 text-md md:text-lg">
                Our welcome email contains a link that you need to click on to set your website
                password - once this is done, you will then be able to login to our website using
                your email address and the password you set.
              </p>

              <p class="my-6 text-blue-900 text-md md:text-lg">
                Within your website account you can do things like select your preferred coaching
                schedule, check and confirm your delivery address for our Saccade overlays, download
                our software, access our training videos, upload any documents required for your
                consultation session or access our Pay-Per-View video content (depending on what you
                have purchased or have signed up for via the DSA).
              </p>

              <p class="my-6 text-blue-900 text-md md:text-lg">
                If you have forgotten your password you can request a new one on our Log In page and
                we will email you a link to reset your password.
              </p>

              <p class="my-6 text-blue-900 text-md md:text-lg">
                Please note that our digital Saccade software can only be downloaded once you have
                completed the first session of our coach-led or distance learning course, and this
                is to ensure that you receive some guidance on their correct usage before starting
                to use them.
              </p>

              <p class="my-6 text-blue-900 text-md md:text-lg">
                Thank you for using VisualReadingOnline.com!
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- END OF support intro -->
    </div>
  </div>

  <script>
    const emailInput = document.getElementById("emailInput")
    const passwordInput = document.getElementById("passwordInput")
    const emailError = document.getElementById("emailError")
    const passwordError = document.getElementById("passwordError")
    const submitButton = document.getElementById("submitButton")

    submitButton.addEventListener("click", function (event) {
      event.preventDefault()

      const email = emailInput.value
      const password = passwordInput.value

      // Email validation using a simple pattern
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      const isEmailValid = emailPattern.test(email)

      // Password validation
      const passwordPattern = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[a-zA-Z\d]{8,}$/
      const isPasswordValid = passwordPattern.test(password)

      // Display error messages if validation fails
      if (!isEmailValid) {
        emailError.classList.remove("hidden")
      } else {
        emailError.classList.add("hidden")
      }

      if (!isPasswordValid) {
        passwordError.classList.remove("hidden")
      } else {
        passwordError.classList.add("hidden")
      }

      // If both fields are valid, proceed with form submission
      if (isEmailValid && isPasswordValid) {
        // You can add code here to submit the form or perform other actions
        console.log("Form submitted successfully!")
      }
    })
  </script>
{% endblock content %}
