{% extends 'base.html' %}
{% load static %}
{% block title %}500 Server Error{% endblock %}

{% block content %}
<div class="max-w-screen-xl px-0 mx-auto xl:px-0 sm:py-0 lg:py-0">
    <div class="p-0 mb-0 md:p-0">
      <!-- legal info -->
      <div class="py-0 sm:py-4 lg:py-4">
        <div class="max-w-screen-xl px-0 mx-auto lg:px-0">
          <div class="grid gap-8 xl:grid-cols-2 xl:gap-12">
            <div>
              <div class="h-64 overflow-hidden rounded-lg md:h-auto">
                <img
                  src="{% static 'images/error.jpg' %}"
                  loading="lazy"
                  alt="Support"
                  class="object-cover object-center w-full h-full"
                />
              </div>
            </div>

            <div class="md:pt-2">
              <h2
                class="mb-4 text-xl font-bold sm:text-2xl md:mb-6 md:text-3xl"
              >
                500 ERROR
              </h2>

              <h2
              class="mb-4 text-xl font-bold sm:text-2xl md:mb-6 md:text-3xl"
            >
              It's Not You, It's Us!
            </h2>

            <p class="mb-6 sm:text-lg md:mb-8">
                A server error has occurred on our end so we can't show you what you were looking for.
              </p>

              <p class="mb-6 sm:text-lg md:mb-8">
                Thats all we can tell you at the moment...
              </p>

              
              <p class="mb-6 sm:text-lg md:mb-8">
                If this error message persists, please
                <a href="/contact" class="font-bold hover:underline">contact us</a> and we will look into the issue.
              </p>
              <p class="mb-6 sm:text-lg md:mb-8">
                Other than that, please use the menu above to navigate through our website. We have a lot of content so hopefully you will find what you are looking for.
              </p>
              <p class="mb-6 sm:text-lg md:mb-8">
                Sorry about that.
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- END OF support intro -->
</div>
{% endblock %}
