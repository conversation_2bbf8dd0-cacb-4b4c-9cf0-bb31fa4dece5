{% extends "base.html" %}
{% load static %}

{% block content %}
<div class="max-w-screen-xl px-0 py-0 mx-auto sm:py-0 lg:py-0">
  <div class="p-0 mt-0 mb-8 md:p-0">
    <div class="py-4">
      <div class="max-w-screen-xl px-0 mx-auto lg:px-0">
        <div class="grid gap-8 xl:grid-cols-2 xl:gap-12">
          <!-- Image Section -->
          <div>
            <div class="h-64 overflow-hidden rounded-lg md:h-auto">
              <img
                src="{% static 'images/dashboard.jpg' %}"
                loading="lazy"
                alt="Magic Link"
                class="object-cover object-center w-full h-full" />
            </div>
          </div>

          <!-- Content Section -->
          <div class="flex justify-center">
            <div class="w-full">
              {% if not user.userprofile.is_profile_completed %}
                <!-- Show Profile Completion Form -->
                <div class="bg-white p-6 rounded-lg shadow-md border border-blue-900">
                  <h2 class="text-2xl font-bold text-blue-900 mb-4">Complete Your Profile</h2>
                  <p class="mb-4">Please complete your profile to access all features.</p>
                  <a href="{% url 'VRO_App2:profile' %}" 
                     class="inline-block px-6 py-3 text-white bg-blue-900 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    Complete Profile
                  </a>
                </div>
              {% else %}
                {% if not user.userprofile.group %}
                  <!-- New User Dashboard -->
                  <div class="space-y-6">
                    <!-- Welcome Section -->
                    <div class="bg-white p-6 rounded-lg shadow-md  border border-blue-900">
                      <h2 class="text-2xl font-bold text-blue-900 mb-4">Welcome, {{ user.userprofile.first_name }}!</h2>
                      <p class="text-gray-600 mb-4">Thank you for completing your profile it will help us better understand your needs and deliver a more personalized experience.</p>
                      
                      <!-- User Info Summary -->
                      <div class="mt-4 p-4 bg-gray-50 rounded-md  border border-blue-900">
                        <h3 class="font-semibold text-blue-900 mb-4">Your Details</h3>
                        <ul class="space-y-4">
                          <li><span class="font-medium">Name:</span> {{ user.userprofile.get_full_name }}</li>
                          <li><span class="font-medium">Email:</span> {{ user.email }}</li>
                        </ul>
                      </div>
                    </div>

                    <!-- Resources Section -->
                    <div class="bg-white p-6 rounded-lg shadow-md border border-blue-900">
                      <h2 class="text-2xl font-bold text-blue-900 mb-4">Lets Get Started!</h2>
                      <div class="space-y-4">
                        <div class="p-4 bg-blue-50 rounded-md border border-blue-900">
                          <h3 class="font-semibold text-blue-900 mb-4">What's Next?</h3>
                          <ul class="list-disc list-inside space-y-4 text-gray-700 mb-6">
                            <li>Been invited or made a Stripe purchase? click 'Link My Account'</li>
                            <li>Want more infromation? click 'Learn More'</li>
                            <li>Ready to purchase a Course or Consultation? click 'Get Started'</li>
                          </ul>
                          
                          <!-- Action Buttons -->
                          <div class="space-y-3 mt-6">
                            <button id="linkAccountButton" 
                                    class="w-full px-6 py-3 text-white bg-blue-900 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                    data-modal="linkAccountModal">
                              Link My Account
                            </button>
                            <a href="{% url 'VRO_App2:learn_more' %}" 
                               class="block w-full px-6 py-3 text-center text-white bg-blue-900 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 hover:text-white">
                              Learn More
                            </a>
                            <a href="{% url 'VRO_App2:purchase_options' %}" 
                               class="block w-full px-6 py-3 text-center text-white bg-blue-900 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 hover:text-white">
                              Get Started
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <p>Not sure what to do next?</p>
                    <a href="{% url 'VRO_App2:members_faq' %}" 
                       class="inline-block w-full mt-4 px-6 py-3 text-white bg-blue-900 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 hover:text-white">
                      New Members FAQ
                    </a>
                  </div>
                {% else %}
                  <!-- Redirect to specific group dashboard will be implemented later -->
                  <div class="bg-yellow-100 border-l-4 border-yellow-500 p-4">
                    <p class="text-yellow-700">
                      Group-specific dashboard coming soon. Your assigned group: {{ user.userprofile.group }}
                    </p>
                  </div>
                {% endif %}
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal Structure -->
<div id="linkAccountModal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-4xl max-h-full">
        <div class="relative bg-white rounded-lg shadow">
            <div class="p-4 md:p-5">
                <div id="linkAccountContent">
                    <!-- Content will be dynamically inserted here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const button = document.getElementById('linkAccountButton');
    const modal = new Modal(document.getElementById('linkAccountModal'));
    const content = document.getElementById('linkAccountContent');

    // Set up click handler for modal trigger
    button.addEventListener('click', function() {
        modal.show();
        
        // Show loading state
        content.innerHTML = `
            <div class="text-center">
                <h2 class="text-2xl font-bold text-blue-900 mb-6">Checking Account Status</h2>
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-900 mx-auto"></div>
                <p class="mt-4 text-gray-600">Please wait while we check your account...</p>
            </div>
        `;
        
        // Make the API call
        fetch('{% url "VRO_App2:check_pending_group" %}')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(data => {
                if (data.found) {
                    content.innerHTML = `
                        <h2 class="text-2xl font-bold text-blue-900 mb-6 text-center">Link Account</h2>
                        <p class="mb-6">Your email address is allocated to the "${data.group_name}" group. Would you like to link your account now?</p>
                        <div class="flex justify-center space-x-4">
                            <button id="confirmLinkButton" class="px-6 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-700">Yes</button>
                            <button id="cancelLinkButton" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">No</button>
                        </div>
                    `;
                    
                    // Add event listeners to the new buttons
                    document.getElementById('confirmLinkButton').addEventListener('click', linkAccount);
                    document.getElementById('cancelLinkButton').addEventListener('click', () => modal.hide());
                } else {
                    content.innerHTML = `
                        <h2 class="text-2xl font-bold text-blue-900 mb-6 text-center">No Link Found</h2>
                        <p class="mb-6 text-center">Your email address is not currently linked to any Product or Service, you will need to make a purchase!</p>
                        <div class="flex justify-center">
                            <button id="closeButton" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">Close</button>
                        </div>
                    `;
                    
                    // Add event listener to the close button
                    document.getElementById('closeButton').addEventListener('click', () => modal.hide());
                }
            })
            .catch(error => {
                console.error('Error:', error);
                content.innerHTML = `
                    <h2 class="text-2xl font-bold text-blue-900 mb-6 text-center">Error</h2>
                    <p class="mb-6">Sorry, there was an error checking your account status. Please try again later.</p>
                    <div class="flex justify-center">
                        <button id="closeButton" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">Close</button>
                    </div>
                `;
                
                // Add event listener to the close button
                document.getElementById('closeButton').addEventListener('click', () => modal.hide());
            });
    });

    function linkAccount() {
        content.innerHTML = `
            <div class="text-center">
                <h2 class="text-2xl font-bold text-blue-900 mb-6">Linking Account</h2>
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-900 mx-auto"></div>
                <p class="mt-4 text-gray-600">Please wait...</p>
            </div>
        `;

        fetch('{% url "VRO_App2:link_account_to_group" %}')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(data => {
                content.innerHTML = `
                    <h2 class="text-2xl font-bold text-blue-900 mb-6 text-center">Success!</h2>
                    <p class="mb-6">Your account has been successfully linked.</p>
                    <p class="mb-6 text-red-600 font-semibold">Important: Please log out and log back in to access your new features.</p>
                    <div class="flex justify-center gap-4">
                        <button id="logoutButton" class="px-6 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-700">Logout Now</button>
                        <button id="closeButton" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">Close</button>
                    </div>
                `;
                
                // Add event listeners to the buttons
                document.getElementById('logoutButton').addEventListener('click', () => {
                    window.location.href = "{% url 'VRO_App1:logout' %}";
                });
                document.getElementById('closeButton').addEventListener('click', () => modal.hide());
            })
            .catch(error => {
                console.error('Error:', error);
                content.innerHTML = `
                    <h2 class="text-2xl font-bold text-blue-900 mb-6 text-center">Error</h2>
                    <p class="mb-6">Sorry, there was an error linking your account. Please try again later.</p>
                    <div class="flex justify-center">
                        <button id="closeButton" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">Close</button>
                    </div>
                `;
                
                // Add event listener to the close button
                document.getElementById('closeButton').addEventListener('click', () => modal.hide());
            });
    }
});
</script>
{% endblock %}


