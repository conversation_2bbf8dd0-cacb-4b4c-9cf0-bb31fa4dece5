{% extends "base.html" %}
{% load static %}
{% load pricing_tags %}

{% block content %}
<div class="max-w-screen-xl px-0 py-0 mx-auto sm:py-0 lg:py-0">
  <!-- Main content section remains the same -->
  <div class="p-0 mt-0 mb-8 md:p-0">
    <div class="py-4">
      <div class="max-w-screen-xl px-0 mx-auto lg:px-0">
        <div class="grid gap-8 xl:grid-cols-2 xl:gap-12">
          <!-- Image Section -->
          <div>
            <div class="h-64 overflow-hidden rounded-lg md:h-auto">
              <img
                src="{% static 'images/get-started.jpg' %}"
                loading="lazy"
                alt="Purchase Options"
                class="object-cover object-center w-full h-full" />
            </div>
          </div>

          <!-- Content Section -->
          <div class="flex justify-center">
            <div class="w-full">
              <div class="bg-white p-6 rounded-lg shadow-md border border-blue-900">
                <h2 class="text-2xl font-bold text-blue-900 mb-4">How Do You Get Started?</h2>
                <div class="space-y-4">
                  <p class="text-blue-900">
                    If you haven't been invited to join Visual Reading Online by a Coach or as part of the DSA process, then you will need to purchase one of our courses or consultations by clicking on one of the options below.
                  </p>
                  <p class="text-blue-900">
                    You will then be presented with a Stripe payment screen. Once your purchase is complete you will then gain access to the course or consultation booking area. Please make sure you complete your Stripe purchase using the same email address that you will use to login to our website.
                  </p>
                  <!-- Course Options -->
                  <div class="mt-6 space-y-6">
                    <!-- Individual Courses Card -->
                    <div class="block cursor-pointer group transition-transform duration-200 hover:scale-[1.02]" 
                         data-modal="individualCourseModal">
                      <div class="p-4 bg-yellow-100 rounded-md border border-blue-900 transition-colors duration-300 group-hover:bg-yellow-200">
                        <h3 class="text-xl font-semibold text-blue-900 mb-3">Individual Courses</h3>
                        <p class="text-blue-900">Our most popular option. Access to all course materials and support.</p>
                      </div>
                    </div>

                    <!-- Distance Learning Card -->
                    <div class="block cursor-pointer group transition-transform duration-200 hover:scale-[1.02]" 
                         data-modal="distanceLearningModal">
                      <div class="p-4 bg-yellow-100 rounded-md border border-blue-900 transition-colors duration-300 group-hover:bg-yellow-200">
                        <h3 class="text-xl font-semibold text-blue-900 mb-3">Distance Learning Courses</h3>
                        <p class="text-blue-900">Our self-paced 100% online course accessed via this website.</p>
                      </div>
                    </div>

                    <!-- Consultation Sessions Card -->
                    <div class="block cursor-pointer group transition-transform duration-200 hover:scale-[1.02]" 
                         data-modal="consultationModal">
                      <div class="p-4 bg-yellow-100 rounded-md border border-blue-900 transition-colors duration-300 group-hover:bg-yellow-200">
                        <h3 class="text-xl font-semibold text-blue-900 mb-3">Consultation Sessions</h3>
                        <p class="text-blue-900">Book a confidential consultation session with a qualified professional to discuss your or your child's Dyslexia Report.</p>
                      </div>
                    </div>

                    <!-- Become a Coach Card -->
                    <div class="block cursor-pointer group transition-transform duration-200 hover:scale-[1.02]" 
                         data-modal="coachModal">
                      <div class="p-4 bg-yellow-100 rounded-md border border-blue-900 transition-colors duration-300 group-hover:bg-yellow-200">
                        <h3 class="text-xl font-semibold text-blue-900 mb-3">Become a Visual Reading Coach</h3>
                        <p class="text-blue-900">Our training course for existing reading professionals who want to become a Visual Reading Coach.</p>
                      </div>
                    </div>
                  </div>
                  <a href="{% url 'VRO_App2:home' %}" 
                     class="block cursor-pointer group transition-transform duration-200 hover:scale-[1.02]">
                     <div class="">
                       <h3 class="text-xl font-semibold text-blue-900 mb-3 text-center">Back</h3>
                     </div>
                   </a>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>



<!-- Modal Templates -->
<div id="individualCourseModal" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-50"></div>
    <div class="fixed inset-0 z-50 flex items-center justify-center">
        <div class="relative bg-white rounded-lg p-8 max-w-4xl w-full mx-4 border-2 border-blue-900">
            <h2 class="text-2xl font-bold text-blue-900 mb-6 text-center">Choose Your Individual Course</h2>
            <div class="flex justify-center">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-3xl">
                    {% get_individual_course_cards as individual_cards %}
                    {% for card in individual_cards %}
                        {% render_pricing_card card %}
                    {% endfor %}
                </div>
            </div>
            <div class="flex justify-center mt-6">
                <button class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400" 
                        data-action="closeModal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="distanceLearningModal" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-50"></div>
    <div class="fixed inset-0 z-50 flex items-center justify-center">
        <div class="relative bg-white rounded-lg p-8 max-w-4xl w-full mx-4 border-2 border-blue-900">
            <h2 class="text-2xl font-bold text-blue-900 mb-6 text-center">Choose Your Distance Learning Course</h2>
            <div class="flex justify-center">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-3xl">
                    {% get_distance_learning_cards as distance_cards %}
                    {% for card in distance_cards %}
                        {% render_pricing_card card %}
                    {% endfor %}
                </div>
            </div>
            <div class="flex justify-center mt-6">
                <button class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400" 
                        data-action="closeModal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="consultationModal" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-50"></div>
    <div class="fixed inset-0 z-50 flex items-center justify-center">
        <div class="relative bg-white rounded-lg p-8 max-w-4xl w-full mx-4 border-2 border-blue-900">
            <h2 class="text-2xl font-bold text-blue-900 mb-6 text-center">Choose Your Consultation Session</h2>
            <div class="flex justify-center">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-3xl">
                    {% get_consultation_cards as consultation_cards %}
                    {% for card in consultation_cards %}
                        {% render_pricing_card card %}
                    {% endfor %}
                </div>
            </div>
            <div class="flex justify-center mt-6">
                <button class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400" 
                        data-action="closeModal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="coachModal" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-50"></div>
    <div class="fixed inset-0 z-50 flex items-center justify-center">
        <div class="relative bg-white rounded-lg p-8 max-w-4xl w-full mx-4 border-2 border-blue-900">
            <h2 class="text-2xl font-bold text-blue-900 mb-6 text-center">Become a Visual Reading Coach</h2>
            <div class="flex justify-center">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-3xl">
                    {% get_coach_cards as coach_cards %}
                    {% for card in coach_cards %}
                        {% render_pricing_card card %}
                    {% endfor %}
                </div>
            </div>
            <div class="flex justify-center mt-6">
                <button class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400" 
                        data-action="closeModal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
window.showModal = function(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        
        modal.querySelectorAll('a').forEach(link => {
            link.setAttribute('target', '_blank');
            link.setAttribute('rel', 'noopener noreferrer');
        });
    }
}

window.closeModal = function(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = '';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Set up click handlers for modal triggers
    document.querySelectorAll('[data-modal]').forEach(trigger => {
        trigger.addEventListener('click', (e) => {
            const modalId = e.currentTarget.getAttribute('data-modal');
            window.showModal(modalId);
        });
    });

    // Close modal when clicking outside
    document.addEventListener('click', (e) => {
        const modals = document.querySelectorAll('[id$="Modal"]');
        modals.forEach(modal => {
            if (!modal.classList.contains('hidden') && 
                !e.target.closest('.relative.bg-white') && 
                !e.target.closest('[data-modal]')) {
                window.closeModal(modal.id);
            }
        });
    });

    // Close buttons
    document.querySelectorAll('[data-action="closeModal"]').forEach(button => {
        button.addEventListener('click', (e) => {
            const modal = e.target.closest('[id]');
            if (modal) {
                window.closeModal(modal.id);
            }
        });
    });
});
</script>
{% endblock content %}







