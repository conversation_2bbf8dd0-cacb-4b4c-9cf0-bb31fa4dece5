{% extends "base.html" %}
{% load static %}

{% block content %}
<div class="max-w-screen-xl px-4 mx-auto mt-6 xl:px-0">
  <div class="grid gap-8 md:grid-cols-2">
    <!-- Image Section -->
    <div class="md:sticky md:top-4 md:h-fit">
      <div class="overflow-hidden rounded-lg shadow-lg">
        <img
          src="{% static 'images/profile.jpg' %}"
          loading="lazy"
          alt="Complete your profile"
          class="object-cover object-center w-full h-full"
        />
      </div>
    </div>

    <!-- Profile Form Section -->
    <div class="p-6 bg-white border border-blue-900 rounded-md">
      <h2 class="mb-6 text-2xl font-bold text-blue-900 md:text-3xl text-center">
        Your Visual Reading Online Profile
      </h2>

      <form method="POST" class="space-y-6" id="profileForm">
        {% csrf_token %}
        
        <!-- Personal Information -->
        <div class="space-y-4">
          <!-- Name Fields -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label for="first_name" class="block text-sm font-medium text-blue-900">First Name</label>
              <input type="text" name="first_name" id="first_name" required
                     value="{{ profile.first_name|default:'' }}"
                     class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div>
              <label for="last_name" class="block text-sm font-medium text-blue-900">Last Name</label>
              <input type="text" name="last_name" id="last_name" required
                     value="{{ profile.last_name|default:'' }}"
                     class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
          </div>

          <!-- Age, Gender, Race -->
          <div class="grid grid-cols-3 gap-4">
            <div>
              <label for="age" class="block text-sm font-medium text-blue-900">Age *</label>
              <input type="number" name="age" id="age" required min="6" max="99"
                     value="{{ profile.age|default:'' }}"
                     placeholder="Enter your age (1-99)"
                     class="mt-1 block w-full !rounded-md !border-blue-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 !important">
              <p class="mt-1 text-sm text-gray-500">Gaurdian needed if below 18</p>
            </div>
            <div>
              <label for="gender" class="block text-sm font-medium text-blue-900">Gender</label>
              <select name="gender" id="gender" required
                      class="mt-1 block w-full rounded-md !border-blue-900 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <option value="">Select gender</option>
                {% for gender in genders %}
                <option value="{{ gender.id }}" {% if profile.gender_id == gender.id %}selected{% endif %}>
                  {{ gender.name }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div>
              <label for="race" class="block text-sm font-medium text-blue-900">Race</label>
              <select name="race" id="race" required
                      class="mt-1 block w-full rounded-md !border-blue-900 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <option value="">Select race</option>
                {% for race in races %}
                <option value="{{ race.id }}" {% if profile.race_id == race.id %}selected{% endif %}>
                  {{ race.name }}
                </option>
                {% endfor %}
              </select>
            </div>
          </div>

          <!-- Contact Numbers -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label for="mobile" class="block text-sm font-medium text-blue-900">Mobile (optional)</label>
              <input type="tel" name="mobile" id="mobile"
                     value="{{ profile.mobile|default:'' }}"
                     class="mt-1 block w-full !rounded-md !border-blue-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 !important">
            </div>
            <div>
              <label for="landline" class="block text-sm font-medium text-blue-900">Landline (optional)</label>
              <input type="tel" name="landline" id="landline"
                     value="{{ profile.landline|default:'' }}"
                     class="mt-1 block w-full !rounded-md !border-blue-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 !important">
            </div>
          </div>

          <!-- Address -->
          <div class="space-y-4">
            <div>
              <label for="address_line1" class="block text-sm font-medium text-blue-900">Address Line 1</label>
              <input type="text" name="address_line1" id="address_line1" required
                     value="{{ profile.address_line1|default:'' }}"
                     class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div>
              <label for="address_line2" class="block text-sm font-medium text-blue-900">Address Line 2 (optional)</label>
              <input type="text" name="address_line2" id="address_line2"
                     value="{{ profile.address_line2|default:'' }}"
                     class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div class="w-1/2">
              <label for="postcode" class="block text-sm font-medium text-blue-900">Postcode</label>
              <input type="text" name="postcode" id="postcode" required
                     value="{{ profile.postcode|default:'' }}"
                     class="mt-1 block w-full uppercase rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
          </div>

          <!-- Reading/Learning Conditions -->
          <div class="p-4 bg-gray-50 rounded-lg border border-blue-900">
            <label class="block mb-3 text-sm font-medium text-blue-900">Reading/Learning Conditions</label>
            <div class="space-y-2">
              <div>
                <label class="inline-flex items-center cursor-pointer">
                  <input type="checkbox" 
                         name="has_no_conditions" 
                         id="has_no_conditions"
                         {% if profile.has_no_conditions %}checked{% endif %}
                         class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                  <span class="ml-2 font-medium">None</span>
                </label>
              </div>
              
              <div id="conditions-list" class="grid gap-2 mt-2 {% if conditions|length > 6 %}md:grid-cols-2{% else %}grid-cols-1{% endif %}">
                {% for condition in conditions %}
                <div>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" 
                           name="conditions" 
                           value="{{ condition.id }}"
                           {% if condition in profile.conditions.all %}checked{% endif %}
                           class="condition-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <span class="ml-2">{{ condition.name }}</span>
                  </label>
                </div>
                {% endfor %}
              </div>
            </div>
          </div>

          <!-- Notes -->
          <div>
            <label for="notes" class="block text-sm font-medium text-blue-900">Notes</label>
            <textarea name="notes" id="notes" rows="4"
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ profile.notes|default:'' }}</textarea>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end pt-4">
          <button type="submit"
                  class="px-6 py-2 text-white bg-blue-900 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full">
            Save Your Profile
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="{% static 'js/profile.js' %}"></script>
{% endblock %}





