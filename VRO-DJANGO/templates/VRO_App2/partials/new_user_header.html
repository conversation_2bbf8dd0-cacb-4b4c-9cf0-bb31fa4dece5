{% load static %}

<!-- Main wrapper with background color -->
<div class="bg-[#fef9c3]">
  <!-- HEADER AND MENU -->
  <div class="mt-2 xl:mt-4 bg-blue-900 py-0.5 max-w-screen-xl mx-2 xl:mx-auto rounded">
    <img
      alt="Visual Reading Online Header"
      src="{% static 'images/vro-heading.png' %}"
      class="mx-auto"
    />
  </div>

  <!-- Navigation wrapper -->
  <div class="mx-2 mt-1">
    <div class="max-w-screen-xl px-0 mx-auto md:px-0">
      <!-- Desktop Menu (md and above) -->
      <div class="hidden mx-auto mt-0 text-lg bg-blue-900 rounded-md text-yellow-50 xl:block">
        <div class="flex justify-end items-center font-bold gap-0.5 p-0.5">
          <a href="{% url 'VRO_App2:home' %}" 
             class="nav-link {% if request.resolver_match.url_name == 'home' %}nav-link-active{% else %}nav-link-inactive{% endif %}">
            Your Dashboard
          </a>
          <a href="{% url 'VRO_App2:profile' %}" 
             class="nav-link {% if request.resolver_match.url_name == 'profile' %}nav-link-active{% else %}nav-link-inactive{% endif %}">
            Edit Your Profile
          </a>
          <a href="{% url 'VRO_App2:members_faq' %}" 
             class="nav-link {% if request.resolver_match.url_name == 'members_faq' %}nav-link-active{% else %}nav-link-inactive{% endif %}">
            Members FAQ
          </a>
          <a href="{% url 'VRO_App1:logout' %}" 
             data-logout-link
             class="nav-link nav-link-inactive logout-link">
            Log Out
          </a>
        </div>
      </div>

      <!-- Mobile Menu Button -->
      <div class="xl:hidden">
        <button
          x-data="{ mobileMenuOpen: false }"
          @click="mobileMenuOpen = !mobileMenuOpen"
          class="w-full p-3 mb-2 text-base font-bold text-yellow-50 bg-blue-900 rounded-md hover:bg-blue-700"
        >
          Menu
        </button>
      </div>

      <!-- Mobile Menu -->
      <div
        x-show="mobileMenuOpen"
        x-transition:enter="transition ease-out duration-200"
        x-transition:enter-start="opacity-0 transform -translate-y-2"
        x-transition:enter-end="opacity-100 transform translate-y-0"
        x-transition:leave="transition ease-in duration-150"
        x-transition:leave-start="opacity-100 transform translate-y-0"
        x-transition:leave-end="opacity-0 transform -translate-y-2"
        class="xl:hidden"
      >
        <nav class="bg-blue-900 rounded-md">
          <a href="{% url 'VRO_App2:home' %}" 
             class="block p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'home' %}bg-blue-700{% endif %}">
            Your Dashboard
          </a>
          <a href="{% url 'VRO_App2:profile' %}" 
             class="block p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'profile' %}bg-blue-700{% endif %}">
            Edit Profile
          </a>
          <a href="{% url 'VRO_App2:members_faq' %}" 
             class="block p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 {% if request.resolver_match.url_name == 'members_faq' %}bg-blue-700{% endif %}">
            Members FAQ
          </a>
          <a href="{% url 'VRO_App1:logout' %}" 
             data-logout-link
             class="block p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 logout-link">
            Log Out
          </a>
        </nav>
      </div>
    </div>
  </div>
</div>






