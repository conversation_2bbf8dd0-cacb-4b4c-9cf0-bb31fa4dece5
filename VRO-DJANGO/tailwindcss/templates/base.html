{% load static %}
{% load tailwind_tags %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>{% block title %}Visual Reading Online{% endblock title %}</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    {% tailwind_css %}
    <!-- Additional CSS -->
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    
    <!-- JavaScript Libraries -->
    <script src="{% static 'js/htmx.js' %}"></script>
    <script defer src="{% static 'js/alpine.js' %}"></script>
    {% block extra_head %}{% endblock %}

    <style>
      /* Fade-in animation */
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .content-fade-in {
        animation: fadeIn 0.5s ease-out forwards;
      }
    </style>
  </head>

  <body class="min-h-screen bg-[#fef9c3] text-blue-900">


    <!-- Add this script block right after the modal HTML -->
    <script>
        function handleLogout(event) {
            event.preventDefault();
            window.location.href = "{% url 'VRO_App1:logout' %}";
        }

        document.addEventListener('DOMContentLoaded', function() {
            const logoutLinks = document.querySelectorAll('[data-logout-link], .logout-link');
            logoutLinks.forEach(link => {
                link.addEventListener('click', handleLogout);
            });
        });
    </script>

    <!-- Your existing session check script -->
    {% if user.is_authenticated %}
    <!--
    <script>
        function checkSessionStatus() {
            fetch('/check-session/', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (!data.is_authenticated || data.force_reload) {
                    if (window.caches) {
                        caches.keys().then(function(names) {
                            for (let name of names)
                                caches.delete(name);
                        });
                    }
                    window.location.href = "{% url 'VRO_App1:logged_out' %}";
                }
            })
            .catch(error => {
                console.error('Session check failed:', error);
                window.location.href = "{% url 'VRO_App1:logged_out' %}";
            });
        }

        // Check session every 5 minutes
        setInterval(checkSessionStatus, 300000); // 300000 ms = 5 minutes
    </script>
    -->
    {% endif %}

    <!-- Header includes -->
    {% if user.is_authenticated %}
        {% if user.userprofile.group.code == 'site-admins' %}
            <!-- Loading site admin header -->
            {% include "VRO_App3/partials/site_admin_header.html" %}
        {% elif not user.userprofile.group %}
            <!-- Loading new user header (no group) -->
            {% include "VRO_App2/partials/new_user_header.html" %}
        {% else %}
            <!-- Loading new user header (has group: {{ user.userprofile.group }}) -->
            {% include "VRO_App2/partials/new_user_header.html" %}
        {% endif %}
    {% else %}
        <!-- Loading public header -->
        {% include "VRO_App1/partials/header.html" %}
    {% endif %}

    <!-- Main content -->
    {% block content %}{% endblock %}





    <!-- Flowbite JS for modals -->
    <script src="{% static 'js/flowbite.min.js' %}"></script>
    {% block extra_js %}{% endblock %}
  </body>
</html>


