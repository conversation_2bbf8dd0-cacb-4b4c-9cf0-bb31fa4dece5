@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles for text and links */
@layer base {
  body {
    @apply text-blue-900;
  }

  a {
    @apply text-blue-900 hover:text-blue-700 transition-colors duration-200;
  }

  /* Override default browser input styles */
  input[type="email"],
  input[type="password"],
  input[type="text"] {
    @apply border-blue-900 rounded-md !important;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }
}

/* Navigation styles */
@layer components {
  .nav-link {
    @apply w-full py-2 px-4 flex items-center justify-center h-full rounded-md transition-colors duration-200 font-bold;
    color: white !important;
  }

  .nav-link-active {
    @apply bg-blue-700 w-full h-full;
  }

  .nav-link-inactive {
    @apply hover:bg-blue-700 w-full h-full;
  }

  /* Dropdown styles */
  .dropdown-item {
    @apply block w-full px-4 py-2 whitespace-nowrap transition-colors duration-200 font-bold;
    color: white !important;
  }

  .dropdown-item:hover {
    @apply bg-blue-700;
  }

  .dropdown-item-active {
    @apply bg-blue-700;
  }
}

/* Form styles */
@layer components {
  .form-input,
  input[type="email"],
  input[type="password"],
  input[type="text"],
  textarea {
    @apply w-full px-4 py-3 border border-blue-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-900 focus:border-blue-900 shadow-sm placeholder-gray-400 !important;
  }

  .form-container {
    @apply p-4 bg-gray-50 rounded-md border border-blue-900;
  }
}

/* Prose styles */
.prose h1 {
    @apply text-3xl font-bold text-blue-900 mb-4;
}

.prose h2 {
    @apply text-2xl font-bold text-blue-900 mb-3;
}

.prose h3 {
    @apply text-xl font-bold text-blue-900 mb-2;
}

.prose p {
    @apply mb-4 text-blue-900;
}

