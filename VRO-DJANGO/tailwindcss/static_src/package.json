{"name": "static_src", "version": "1.0.0", "description": "", "scripts": {"start": "npm run dev", "build": "npm run build:clean && npm run build:sass && npm run build:postcss", "build:clean": "rimraf ../static/css/dist", "build:sass": "sass --style compressed src/styles.scss ../static/css/dist/styles.css", "build:postcss": "postcss ../static/css/dist/styles.css -o ../static/css/dist/styles.css", "dev": "cross-env NODE_ENV=development tailwindcss --postcss -i ./src/styles.css -o ../static/css/dist/styles.css -w", "lint": "eslint ."}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.17", "cross-env": "^7.0.3", "postcss": "^8.4.35", "postcss-cli": "^11.0.0", "postcss-import": "^16.0.1", "postcss-nested": "^6.0.1", "postcss-simple-vars": "^7.0.1", "rimraf": "^5.0.5", "tailwindcss": "^3.4.1"}}