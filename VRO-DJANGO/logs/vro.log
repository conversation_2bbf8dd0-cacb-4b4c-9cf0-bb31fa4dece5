INFO 2025-03-25 17:47:42,604 autoreload 28564 26116 Watching for file changes with StatReloader
ERROR 2025-03-25 17:57:15,114 log 28564 19004 Internal Server Error: /dashboard/site-admins/group-allocations/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 84, in manage_group_allocations
    return render(request, 'VRO_App3/site-admins/manage_group_allocations.html', {
        'form': form,
        'allocations': allocations
    })
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 238, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'delete_group_allocation' not found. 'delete_group_allocation' is not a valid view function or pattern name.
INFO 2025-03-25 17:57:18,870 autoreload 28564 26116 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-25 17:57:21,858 autoreload 21876 24456 Watching for file changes with StatReloader
INFO 2025-03-25 17:57:26,852 autoreload 21876 24456 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-25 17:57:27,704 autoreload 19052 6848 Watching for file changes with StatReloader
INFO 2025-03-25 17:59:15,685 autoreload 19052 6848 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-25 17:59:16,659 autoreload 26648 19744 Watching for file changes with StatReloader
INFO 2025-03-25 18:03:14,889 autoreload 26648 19744 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-25 18:03:18,062 autoreload 18452 11352 Watching for file changes with StatReloader
INFO 2025-03-25 18:03:35,436 autoreload 18452 11352 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-25 18:03:36,348 autoreload 27084 19128 Watching for file changes with StatReloader
ERROR 2025-03-25 18:03:46,535 views 27084 21120 Error deleting allocation: cannot access local variable 'User' where it is not associated with a value
INFO 2025-03-25 18:06:29,104 autoreload 27084 19128 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-25 18:06:30,078 autoreload 27876 23796 Watching for file changes with StatReloader
INFO 2025-03-25 18:06:38,780 autoreload 27876 23796 F:\DOCUMENTS\VRO2025\VRO_App2\views.py changed, reloading.
INFO 2025-03-25 18:06:39,724 autoreload 21108 14784 Watching for file changes with StatReloader
INFO 2025-03-25 18:07:02,190 views 21108 23608 Group access removed <NAME_EMAIL> by <EMAIL>
INFO 2025-03-25 18:07:53,610 views 21108 8796 Group allocation <NAME_EMAIL> by <EMAIL>
INFO 2025-03-25 18:10:21,496 autoreload 21108 14784 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-25 18:10:38,427 views 21108 11364 Group allocation <NAME_EMAIL> by <EMAIL>
INFO 2025-03-25 18:10:45,174 autoreload 28584 22412 Watching for file changes with StatReloader
INFO 2025-03-25 18:10:58,050 views 28584 21672 Group allocation <NAME_EMAIL> by <EMAIL>
INFO 2025-03-25 18:12:52,884 autoreload 28584 22412 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-25 18:12:53,858 autoreload 16704 21660 Watching for file changes with StatReloader
INFO 2025-03-25 18:15:20,977 views 16704 27480 Group allocation <NAME_EMAIL> by <EMAIL>
INFO 2025-03-25 18:16:04,299 views 16704 26684 Group allocation <NAME_EMAIL> by <EMAIL>
INFO 2025-03-26 08:32:23,872 autoreload 1308 3984 Watching for file changes with StatReloader
WARNING 2025-03-26 08:32:33,742 log 1308 17648 Not Found: /favicon.ico
INFO 2025-03-26 08:40:13,997 autoreload 16564 17428 Watching for file changes with StatReloader
WARNING 2025-03-26 08:51:02,204 log 16564 16088 Not Found: /favicon.ico
INFO 2025-03-26 08:52:27,199 autoreload 16564 17428 F:\DOCUMENTS\VRO2025\VRO_Project\settings.py changed, reloading.
INFO 2025-03-26 08:52:28,236 autoreload 5276 3544 Watching for file changes with StatReloader
WARNING 2025-03-26 08:53:13,205 log 5276 7780 Not Found: /favicon.ico
INFO 2025-03-26 08:54:47,493 autoreload 472 5536 Watching for file changes with StatReloader
WARNING 2025-03-26 08:54:55,166 log 472 1736 Not Found: /favicon.ico
WARNING 2025-03-26 08:54:57,524 log 472 1736 Not Found: /favicon.ico
INFO 2025-03-26 08:59:20,718 autoreload 472 5536 F:\DOCUMENTS\VRO2025\VRO_Project\settings.py changed, reloading.
INFO 2025-03-26 09:03:16,327 autoreload 18212 4276 Watching for file changes with StatReloader
INFO 2025-03-26 09:06:51,853 autoreload 18212 4276 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 09:06:52,846 autoreload 7680 8116 Watching for file changes with StatReloader
WARNING 2025-03-26 09:07:08,987 log 7680 6716 Not Found: /favicon.ico
WARNING 2025-03-26 09:07:19,619 log 7680 6716 Not Found: /favicon.ico
WARNING 2025-03-26 09:07:45,300 log 7680 18812 Not Found: /favicon.ico
WARNING 2025-03-26 09:07:47,019 log 7680 18812 Not Found: /favicon.ico
WARNING 2025-03-26 09:08:33,120 log 7680 18812 Not Found: /favicon.ico
WARNING 2025-03-26 09:08:53,583 log 7680 18436 Not Found: /favicon.ico
INFO 2025-03-26 09:13:00,646 autoreload 7680 8116 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 09:13:01,649 autoreload 20244 20248 Watching for file changes with StatReloader
ERROR 2025-03-26 09:13:03,397 log 20244 10420 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: ''

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 39, in site_admins_dashboard
    'active_groups': UserProfile.objects.values('group').exclude(group='').distinct().count(),
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 1444, in exclude
    return self._filter_or_exclude(True, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 1459, in _filter_or_exclude_inplace
    self._query.add_q(~Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\fields\related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got ''.
WARNING 2025-03-26 09:13:03,573 log 20244 10420 Not Found: /favicon.ico
ERROR 2025-03-26 09:13:19,651 log 20244 16976 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: ''

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 39, in site_admins_dashboard
    'active_groups': UserProfile.objects.values('group').exclude(group='').distinct().count(),
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 1444, in exclude
    return self._filter_or_exclude(True, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 1459, in _filter_or_exclude_inplace
    self._query.add_q(~Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\fields\related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got ''.
INFO 2025-03-26 09:14:16,758 autoreload 20244 20248 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 09:14:17,677 autoreload 17656 2732 Watching for file changes with StatReloader
ERROR 2025-03-26 09:14:19,097 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:19,199 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:19,314 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:19,361 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:19,417 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:19,509 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:19,581 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:19,633 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:19,721 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:19,794 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:19,849 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:19,901 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:19,944 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:20,023 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:20,094 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:20,223 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:20,330 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:20,425 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:20,520 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:20,587 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
ERROR 2025-03-26 09:14:20,631 views 17656 18032 ValueError in site_admins_dashboard: Field 'id' expected a number but got ''.
INFO 2025-03-26 09:15:11,891 autoreload 17656 2732 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 09:15:12,830 autoreload 14768 20120 Watching for file changes with StatReloader
WARNING 2025-03-26 09:15:20,707 log 14768 13176 Not Found: /favicon.ico
WARNING 2025-03-26 09:16:44,131 log 14768 19428 Not Found: /favicon.ico
WARNING 2025-03-26 09:21:44,592 log 14768 10420 Not Found: /favicon.ico
ERROR 2025-03-26 09:26:02,247 log 14768 9756 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 09:26:02,265 log 14768 15280 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
WARNING 2025-03-26 09:26:02,460 log 14768 9756 Not Found: /favicon.ico
INFO 2025-03-26 09:26:51,342 autoreload 14768 20120 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 09:26:52,291 autoreload 20024 10296 Watching for file changes with StatReloader
INFO 2025-03-26 09:27:05,510 autoreload 20024 10296 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 09:27:06,492 autoreload 20280 2808 Watching for file changes with StatReloader
ERROR 2025-03-26 09:27:08,254 log 20280 9756 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 09:27:11,971 log 20280 10504 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 09:28:21,690 log 20280 18912 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
WARNING 2025-03-26 09:28:21,871 log 20280 18912 Not Found: /favicon.ico
ERROR 2025-03-26 09:28:22,846 log 20280 11928 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
WARNING 2025-03-26 09:28:23,016 log 20280 11928 Not Found: /favicon.ico
ERROR 2025-03-26 09:28:27,389 log 20280 6204 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 09:28:33,200 log 20280 20376 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
WARNING 2025-03-26 09:28:33,376 log 20280 20376 Not Found: /favicon.ico
ERROR 2025-03-26 09:28:42,042 log 20280 21120 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
WARNING 2025-03-26 09:28:42,261 log 20280 21120 Not Found: /favicon.ico
INFO 2025-03-26 09:29:48,303 autoreload 20280 2808 F:\DOCUMENTS\VRO2025\VRO_Project\urls.py changed, reloading.
INFO 2025-03-26 09:29:49,315 autoreload 9756 9964 Watching for file changes with StatReloader
INFO 2025-03-26 09:29:55,622 autoreload 9756 9964 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 09:29:56,679 autoreload 19528 3100 Watching for file changes with StatReloader
ERROR 2025-03-26 09:29:58,369 log 19528 10260 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
WARNING 2025-03-26 09:29:58,568 log 19528 10260 Not Found: /favicon.ico
INFO 2025-03-26 09:30:08,715 autoreload 19528 3100 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 09:30:09,672 autoreload 20764 19240 Watching for file changes with StatReloader
ERROR 2025-03-26 09:30:11,422 log 20764 8968 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
INFO 2025-03-26 09:30:28,522 autoreload 20764 19240 F:\DOCUMENTS\VRO2025\VRO_Project\urls.py changed, reloading.
INFO 2025-03-26 09:30:29,545 autoreload 21404 1052 Watching for file changes with StatReloader
ERROR 2025-03-26 09:30:31,053 log 21404 21200 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 09:30:31,237 log 21404 21120 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
INFO 2025-03-26 09:31:22,819 autoreload 21404 1052 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 09:31:23,842 autoreload 3716 20504 Watching for file changes with StatReloader
ERROR 2025-03-26 09:31:25,489 log 3716 4680 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
INFO 2025-03-26 09:31:34,590 autoreload 3716 20504 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 09:31:35,599 autoreload 20856 20312 Watching for file changes with StatReloader
ERROR 2025-03-26 09:31:37,289 log 20856 2904 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 09:31:47,921 log 20856 7196 Internal Server Error: /dashboard/site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
INFO 2025-03-26 09:31:59,117 autoreload 20856 20312 F:\DOCUMENTS\VRO2025\VRO_Project\urls.py changed, reloading.
INFO 2025-03-26 09:32:00,113 autoreload 21172 18964 Watching for file changes with StatReloader
WARNING 2025-03-26 09:32:01,781 log 21172 5180 Not Found: /dashboard/site-admins/
INFO 2025-03-26 09:34:09,605 autoreload 21172 18964 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 09:34:10,704 autoreload 20292 7088 Watching for file changes with StatReloader
WARNING 2025-03-26 09:34:12,533 log 20292 20492 Not Found: /dashboard/site-admins/
INFO 2025-03-26 09:34:19,439 autoreload 20292 7088 F:\DOCUMENTS\VRO2025\VRO_Project\settings.py changed, reloading.
INFO 2025-03-26 09:34:20,429 autoreload 6312 20672 Watching for file changes with StatReloader
WARNING 2025-03-26 09:34:22,081 log 6312 6180 Not Found: /dashboard/site-admins/
INFO 2025-03-26 09:34:40,533 autoreload 6312 20672 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 09:34:41,509 autoreload 20312 20640 Watching for file changes with StatReloader
WARNING 2025-03-26 09:34:43,125 log 20312 21284 Not Found: /dashboard/site-admins/
INFO 2025-03-26 10:02:10,970 autoreload 8916 12244 Watching for file changes with StatReloader
WARNING 2025-03-26 10:02:16,853 log 8916 12948 Not Found: /favicon.ico
WARNING 2025-03-26 10:02:17,285 log 8916 19184 Not Found: /favicon.ico
ERROR 2025-03-26 10:02:23,811 views 8916 18024 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:02:23,883 log 8916 18024 Internal Server Error: /site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 50, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 54, in site_admins_dashboard
    return redirect('VRO_App1:home')
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 48, in redirect
    return redirect_class(resolve_url(to, *args, **kwargs))
                          ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 145, in resolve_url
    return reverse(to, args=args, kwargs=kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'home' not found. 'home' is not a valid view function or pattern name.
INFO 2025-03-26 10:03:17,619 autoreload 8916 12244 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 10:03:18,606 autoreload 18668 7360 Watching for file changes with StatReloader
ERROR 2025-03-26 10:03:20,145 views 18668 19576 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:20,230 log 18668 19576 Internal Server Error: /site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 50, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 54, in site_admins_dashboard
    return redirect('VRO_App3:home')
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 48, in redirect
    return redirect_class(resolve_url(to, *args, **kwargs))
                          ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 145, in resolve_url
    return reverse(to, args=args, kwargs=kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'home' not found. 'home' is not a valid view function or pattern name.
INFO 2025-03-26 10:03:26,268 autoreload 18668 7360 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 10:03:27,248 autoreload 18936 3404 Watching for file changes with StatReloader
ERROR 2025-03-26 10:03:28,816 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:28,925 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:29,034 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:29,152 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:29,269 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:29,398 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:29,520 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:29,660 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:29,791 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:30,053 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:31,503 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:31,614 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:31,718 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:31,834 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:31,980 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:32,127 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:32,230 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:32,326 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:32,412 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:03:32,623 views 18936 3012 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
INFO 2025-03-26 10:04:09,688 autoreload 18408 3380 Watching for file changes with StatReloader
ERROR 2025-03-26 10:04:14,173 views 18408 11884 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:04:14,263 log 18408 11884 Internal Server Error: /site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 50, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 54, in site_admins_dashboard
    return redirect('VRO_App1:home')
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 48, in redirect
    return redirect_class(resolve_url(to, *args, **kwargs))
                          ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 145, in resolve_url
    return reverse(to, args=args, kwargs=kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'home' not found. 'home' is not a valid view function or pattern name.
INFO 2025-03-26 10:06:14,186 autoreload 18408 3380 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 10:06:15,154 autoreload 19640 11160 Watching for file changes with StatReloader
ERROR 2025-03-26 10:06:16,701 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:16,775 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:16,843 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:16,908 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:16,975 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,042 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,110 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,162 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,224 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,291 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,347 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,413 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,487 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,555 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,634 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,677 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,723 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:17,848 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:18,009 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:18,152 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:19,307 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:19,418 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:19,479 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:06:19,529 views 19640 19764 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
INFO 2025-03-26 10:07:30,996 autoreload 11328 5284 Watching for file changes with StatReloader
ERROR 2025-03-26 10:07:35,100 views 11328 10872 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:07:35,231 log 11328 10872 Internal Server Error: /site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 50, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 55, in site_admins_dashboard
    return render(request, 'VRO_App3/error.html', {'error_message': "Dashboard is currently unavailable"})
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
INFO 2025-03-26 10:09:26,034 autoreload 11328 5284 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 10:09:27,022 autoreload 10812 488 Watching for file changes with StatReloader
ERROR 2025-03-26 10:09:28,516 views 10812 14592 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:09:28,631 log 10812 14592 Internal Server Error: /site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 50, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', {
        **context,
        'template_type': 'site_admin'  # Add this to help with template selection
    })
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 57, in site_admins_dashboard
    return render(request, 'VRO_App3/error.html', {'error_message': "Dashboard is currently unavailable"})
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
INFO 2025-03-26 10:10:31,201 autoreload 10812 488 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 10:10:32,223 autoreload 5108 10992 Watching for file changes with StatReloader
INFO 2025-03-26 10:10:39,640 autoreload 5108 10992 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 10:10:40,582 autoreload 8420 19300 Watching for file changes with StatReloader
ERROR 2025-03-26 10:10:42,203 views 8420 14596 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:10:42,315 log 8420 14596 Internal Server Error: /site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 50, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', {
        **context,
        'template_type': 'site_admin'  # Add this to help with template selection
    })
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 57, in site_admins_dashboard
    return render(request, 'VRO_App3/error.html', {'error_message': "Dashboard is currently unavailable"})
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:11:19,440 views 8420 7560 Error in site_admins_dashboard: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:11:19,539 log 8420 7560 Internal Server Error: /site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 50, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', {
        **context,
        'template_type': 'site_admin'  # Add this to help with template selection
    })
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 57, in site_admins_dashboard
    return render(request, 'VRO_App3/error.html', {'error_message': "Dashboard is currently unavailable"})
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'public_messages' not found. 'public_messages' is not a valid view function or pattern name.
ERROR 2025-03-26 10:18:27,345 views 8420 15632 Error in site_admins_dashboard: Reverse for 'manage_group_allocations' not found. 'manage_group_allocations' is not a valid view function or pattern name.
ERROR 2025-03-26 10:18:27,447 log 8420 15632 Internal Server Error: /site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 50, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', {
        **context,
        'template_type': 'site_admin'  # Add this to help with template selection
    })
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'manage_group_allocations' not found. 'manage_group_allocations' is not a valid view function or pattern name.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 57, in site_admins_dashboard
    return render(request, 'VRO_App3/error.html', {'error_message': "Dashboard is currently unavailable"})
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'manage_group_allocations' not found. 'manage_group_allocations' is not a valid view function or pattern name.
ERROR 2025-03-26 10:18:34,209 views 8420 20708 Error in site_admins_dashboard: Reverse for 'manage_group_allocations' not found. 'manage_group_allocations' is not a valid view function or pattern name.
ERROR 2025-03-26 10:18:34,309 log 8420 20708 Internal Server Error: /site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 50, in site_admins_dashboard
    return render(request, 'VRO_App3/site-admins/dashboard.html', {
        **context,
        'template_type': 'site_admin'  # Add this to help with template selection
    })
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'manage_group_allocations' not found. 'manage_group_allocations' is not a valid view function or pattern name.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 57, in site_admins_dashboard
    return render(request, 'VRO_App3/error.html', {'error_message': "Dashboard is currently unavailable"})
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'manage_group_allocations' not found. 'manage_group_allocations' is not a valid view function or pattern name.
INFO 2025-03-26 10:19:11,876 autoreload 8420 19300 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 10:19:12,850 autoreload 8480 3428 Watching for file changes with StatReloader
INFO 2025-03-26 10:19:49,210 views 8480 17156 Group access removed <NAME_EMAIL> by <EMAIL>
ERROR 2025-03-26 10:33:21,786 log 8480 18024 Internal Server Error: /site-admins/blogs/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 75, in manage_blogs
    return render(request, 'VRO_App3/site-admins/manage_blogs.html')
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: VRO_App3/site-admins/manage_blogs.html
ERROR 2025-03-26 10:34:29,245 log 8480 3476 Internal Server Error: /site-admins/articles/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 69, in manage_articles
    return render(request, 'VRO_App3/site-admins/manage_articles.html')
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: VRO_App3/site-admins/manage_articles.html
WARNING 2025-03-26 10:37:57,289 log 8480 15804 Not Found: /favicon.ico
ERROR 2025-03-26 10:49:21,217 log 8480 12764 Internal Server Error: /site-admins/articles/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 69, in manage_articles
    return render(request, 'VRO_App3/site-admins/manage_articles.html')
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: VRO_App3/site-admins/manage_articles.html
INFO 2025-03-26 10:53:30,002 autoreload 8480 3428 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 10:53:31,071 autoreload 12184 7476 Watching for file changes with StatReloader
INFO 2025-03-26 10:54:16,870 autoreload 12184 7476 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 10:54:17,833 autoreload 15720 18048 Watching for file changes with StatReloader
INFO 2025-03-26 11:05:47,197 autoreload 15720 18048 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 11:05:48,072 autoreload 18748 19560 Watching for file changes with StatReloader
INFO 2025-03-26 11:06:10,118 autoreload 18748 19560 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 11:06:11,079 autoreload 8360 17904 Watching for file changes with StatReloader
INFO 2025-03-26 11:08:14,136 autoreload 8360 17904 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 11:08:15,107 autoreload 18704 18180 Watching for file changes with StatReloader
INFO 2025-03-26 11:08:28,072 autoreload 18704 18180 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 11:08:28,994 autoreload 16300 7776 Watching for file changes with StatReloader
INFO 2025-03-26 11:14:32,411 autoreload 16300 7776 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 11:14:33,287 autoreload 15272 7580 Watching for file changes with StatReloader
INFO 2025-03-26 11:14:43,884 autoreload 15272 7580 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 11:14:44,786 autoreload 15576 15572 Watching for file changes with StatReloader
INFO 2025-03-26 11:15:44,342 autoreload 15576 15572 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 11:15:45,247 autoreload 11540 19408 Watching for file changes with StatReloader
ERROR 2025-03-26 11:15:49,867 log 11540 2396 Internal Server Error: /site-admins/pages/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 71, in reverse
    extra, resolver = resolver.namespace_dict[ns]
                      ~~~~~~~~~~~~~~~~~~~~~~~^^^^
KeyError: 'admin'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 25, in _wrapper_view
    resolved_login_url = resolve_url(login_url or settings.LOGIN_URL)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 145, in resolve_url
    return reverse(to, args=args, kwargs=kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 82, in reverse
    raise NoReverseMatch("%s is not a registered namespace" % key)
django.urls.exceptions.NoReverseMatch: 'admin' is not a registered namespace
INFO 2025-03-26 11:16:56,347 autoreload 11540 19408 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 11:16:57,219 autoreload 6372 14500 Watching for file changes with StatReloader
ERROR 2025-03-26 11:18:45,558 log 6372 9360 Internal Server Error: /site-admins/pricing/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 76, in manage_pricing
    return render(request, 'VRO_App3/site-admins/manage_pricing.html')
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: VRO_App3/site-admins/manage_pricing.html
INFO 2025-03-26 11:21:55,801 autoreload 6372 14500 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 11:21:56,761 autoreload 10272 14528 Watching for file changes with StatReloader
ERROR 2025-03-26 11:38:57,471 log 10272 14324 Internal Server Error: /site-admins/articles/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 86, in manage_articles
    return render(request, 'VRO_App3/site-admins/manage_articles.html')
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: VRO_App3/site-admins/manage_articles.html
ERROR 2025-03-26 11:39:58,534 log 10272 22048 Internal Server Error: /site-admins/lookups/genders/3/edit/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 302, in edit_gender
    form = GenderForm(instance=gender)
           ^^^^^^^^^^
NameError: name 'GenderForm' is not defined
ERROR 2025-03-26 11:40:16,719 log 10272 18960 Internal Server Error: /site-admins/lookups/races/11/edit/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 355, in edit_race
    form = RaceForm(instance=race)
           ^^^^^^^^
NameError: name 'RaceForm' is not defined
INFO 2025-03-26 11:41:07,554 autoreload 10272 14528 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 11:41:08,502 autoreload 18152 3332 Watching for file changes with StatReloader
INFO 2025-03-26 12:02:05,030 autoreload 18152 3332 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 12:02:05,989 autoreload 6316 8508 Watching for file changes with StatReloader
INFO 2025-03-26 12:02:33,325 autoreload 6316 8508 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:02:34,180 autoreload 9852 17684 Watching for file changes with StatReloader
INFO 2025-03-26 12:02:57,171 autoreload 9852 17684 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:02:58,016 autoreload 7364 16556 Watching for file changes with StatReloader
INFO 2025-03-26 12:03:23,089 autoreload 7364 16556 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:03:23,947 autoreload 21884 10144 Watching for file changes with StatReloader
INFO 2025-03-26 12:03:44,662 autoreload 21884 10144 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:03:45,510 autoreload 12700 21648 Watching for file changes with StatReloader
ERROR 2025-03-26 12:03:49,151 log 12700 14792 Internal Server Error: /articles/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
psycopg2.errors.UndefinedColumn: column VRO_App1_document.author does not exist
LINE 1: ...nal_filename", "VRO_App1_document"."description", "VRO_App1_...
                                                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App1\views.py", line 443, in articles_index_view
    return render(request, 'VRO_App1/articles-index.html', {
        'articles': articles
    })
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
django.db.utils.ProgrammingError: column VRO_App1_document.author does not exist
LINE 1: ...nal_filename", "VRO_App1_document"."description", "VRO_App1_...
                                                             ^

ERROR 2025-03-26 12:05:10,530 log 12700 4772 Internal Server Error: /site-admins/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 39, in site_admins_dashboard
    User = get_user_model()
           ^^^^^^^^^^^^^^
NameError: name 'get_user_model' is not defined
INFO 2025-03-26 12:05:37,264 autoreload 12700 21648 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:05:38,169 autoreload 6596 21868 Watching for file changes with StatReloader
ERROR 2025-03-26 12:05:39,538 views 6596 14656 Error in site_admins_dashboard: name 'UserProfile' is not defined
INFO 2025-03-26 12:06:15,936 autoreload 6596 21868 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:06:16,892 autoreload 20668 20872 Watching for file changes with StatReloader
ERROR 2025-03-26 12:06:18,291 views 20668 16292 Error in site_admins_dashboard: Cannot resolve keyword 'status' into field. Choices are: created_at, created_by, created_by_id, email, group, group_id, id, is_used, used_at
ERROR 2025-03-26 12:06:20,097 views 20668 12144 Error in site_admins_dashboard: Cannot resolve keyword 'status' into field. Choices are: created_at, created_by, created_by_id, email, group, group_id, id, is_used, used_at
INFO 2025-03-26 12:07:01,584 autoreload 20668 20872 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:07:02,545 autoreload 17964 9180 Watching for file changes with StatReloader
ERROR 2025-03-26 12:10:07,133 log 17964 4648 Internal Server Error: /site-admins/lookups/genders/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 243, in manage_genders
    genders = Gender.objects.all()
              ^^^^^^
NameError: name 'Gender' is not defined. Did you mean: 'render'?
ERROR 2025-03-26 12:10:11,426 log 17964 4648 Internal Server Error: /site-admins/lookups/races/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 296, in manage_races
    races = Race.objects.all()
            ^^^^
NameError: name 'Race' is not defined
ERROR 2025-03-26 12:10:14,926 log 17964 4648 Internal Server Error: /site-admins/lookups/conditions/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 348, in manage_conditions
    conditions = Condition.objects.all()
                 ^^^^^^^^^
NameError: name 'Condition' is not defined
ERROR 2025-03-26 12:10:23,103 log 17964 4648 Internal Server Error: /site-admins/pricing/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in manage_pricing
    pricing_cards = PricingCard.objects.all().order_by('Card_ID')
                    ^^^^^^^^^^^
NameError: name 'PricingCard' is not defined
INFO 2025-03-26 12:11:04,397 autoreload 17964 9180 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 12:11:05,471 autoreload 20428 11536 Watching for file changes with StatReloader
WARNING 2025-03-26 12:11:33,732 log 20428 8372 Not Found: /favicon.ico
ERROR 2025-03-26 12:12:32,129 log 20428 21660 Internal Server Error: /site-admins/pricing/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 52, in manage_pricing
    pricing_cards = PricingCard.objects.all().order_by('Card_ID')
                    ^^^^^^^^^^^
NameError: name 'PricingCard' is not defined
INFO 2025-03-26 12:14:15,758 autoreload 20428 11536 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:14:16,700 autoreload 9360 1276 Watching for file changes with StatReloader
ERROR 2025-03-26 12:14:22,949 log 9360 20444 Internal Server Error: /site-admins/document/7/edit/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 564, in edit_document
    form = DocumentForm(instance=document)
           ^^^^^^^^^^^^
NameError: name 'DocumentForm' is not defined
ERROR 2025-03-26 12:14:40,142 log 9360 11144 Internal Server Error: /site-admins/document/11/edit/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 564, in edit_document
    form = DocumentForm(instance=document)
           ^^^^^^^^^^^^
NameError: name 'DocumentForm' is not defined
ERROR 2025-03-26 12:15:01,298 log 9360 15200 Internal Server Error: /site-admins/lookups/genders/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 253, in manage_genders
    genders = Gender.objects.all()
              ^^^^^^
NameError: name 'Gender' is not defined. Did you mean: 'render'?
ERROR 2025-03-26 12:15:13,607 log 9360 12912 Internal Server Error: /site-admins/lookups/races/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 306, in manage_races
    races = Race.objects.all()
            ^^^^
NameError: name 'Race' is not defined
ERROR 2025-03-26 12:15:29,994 log 9360 3580 Internal Server Error: /site-admins/lookups/conditions/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 358, in manage_conditions
    conditions = Condition.objects.all()
                 ^^^^^^^^^
NameError: name 'Condition' is not defined
INFO 2025-03-26 12:16:55,634 autoreload 9360 1276 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:16:56,499 autoreload 4424 1480 Watching for file changes with StatReloader
INFO 2025-03-26 12:20:29,678 autoreload 4424 1480 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 12:20:30,700 autoreload 1472 20216 Watching for file changes with StatReloader
INFO 2025-03-26 12:20:37,043 autoreload 1472 20216 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:20:37,896 autoreload 12232 19260 Watching for file changes with StatReloader
INFO 2025-03-26 12:25:21,245 autoreload 12232 19260 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:25:22,271 autoreload 8044 22452 Watching for file changes with StatReloader
ERROR 2025-03-26 12:28:08,357 views 8044 18096 Error in site_admins_dashboard: name 'UserProfile' is not defined
ERROR 2025-03-26 12:28:10,860 views 8044 21928 Error in site_admins_dashboard: name 'UserProfile' is not defined
ERROR 2025-03-26 12:28:31,128 log 8044 4648 Internal Server Error: /site-admins/group-allocations/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 134, in manage_group_allocations
    form = GroupAllocationForm()
           ^^^^^^^^^^^^^^^^^^^
NameError: name 'GroupAllocationForm' is not defined
ERROR 2025-03-26 12:28:44,872 views 8044 19784 Error in site_admins_dashboard: name 'UserProfile' is not defined
ERROR 2025-03-26 12:28:47,111 log 8044 5216 Internal Server Error: /site-admins/groups/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 201, in manage_groups
    groups = UserGroup.objects.all().order_by('name')
             ^^^^^^^^^
NameError: name 'UserGroup' is not defined
ERROR 2025-03-26 12:28:49,032 views 8044 22024 Error in site_admins_dashboard: name 'UserProfile' is not defined
ERROR 2025-03-26 12:28:52,067 views 8044 15276 Error in site_admins_dashboard: name 'UserProfile' is not defined
ERROR 2025-03-26 12:28:53,730 views 8044 21524 Error in site_admins_dashboard: name 'UserProfile' is not defined
ERROR 2025-03-26 12:28:55,193 views 8044 256 Error in site_admins_dashboard: name 'UserProfile' is not defined
ERROR 2025-03-26 12:28:56,053 views 8044 16312 Error in site_admins_dashboard: name 'UserProfile' is not defined
INFO 2025-03-26 12:31:57,666 autoreload 8044 22452 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:31:58,635 autoreload 18496 19868 Watching for file changes with StatReloader
INFO 2025-03-26 12:32:08,130 autoreload 18496 19868 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:32:08,986 autoreload 21484 11848 Watching for file changes with StatReloader
INFO 2025-03-26 12:32:40,781 autoreload 21484 11848 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:32:41,639 autoreload 13556 20376 Watching for file changes with StatReloader
INFO 2025-03-26 12:33:11,222 autoreload 13556 20376 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:33:12,064 autoreload 2276 12148 Watching for file changes with StatReloader
INFO 2025-03-26 12:33:38,285 autoreload 2276 12148 F:\DOCUMENTS\VRO2025\VRO_App3\forms.py changed, reloading.
INFO 2025-03-26 12:33:39,134 autoreload 20044 12592 Watching for file changes with StatReloader
INFO 2025-03-26 12:39:35,685 autoreload 20044 12592 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:39:36,583 autoreload 14404 19200 Watching for file changes with StatReloader
INFO 2025-03-26 12:39:48,411 autoreload 14404 19200 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 12:39:49,374 autoreload 15900 4536 Watching for file changes with StatReloader
INFO 2025-03-26 12:54:09,581 autoreload 15900 4536 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 12:54:10,615 autoreload 8204 9976 Watching for file changes with StatReloader
INFO 2025-03-26 12:55:57,494 autoreload 8204 9976 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 12:55:58,398 autoreload 11800 19952 Watching for file changes with StatReloader
INFO 2025-03-26 12:56:16,424 autoreload 20560 8932 Watching for file changes with StatReloader
INFO 2025-03-26 12:57:37,465 autoreload 20560 8932 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 12:57:38,472 autoreload 5292 10056 Watching for file changes with StatReloader
INFO 2025-03-26 12:58:55,725 autoreload 5292 10056 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 12:58:56,786 autoreload 10832 9132 Watching for file changes with StatReloader
INFO 2025-03-26 13:00:40,426 autoreload 19832 18652 Watching for file changes with StatReloader
WARNING 2025-03-26 13:00:48,802 log 19832 21564 Not Found: /favicon.ico
WARNING 2025-03-26 13:03:03,816 log 19832 19228 Not Found: /favicon.ico
INFO 2025-03-26 13:09:34,865 autoreload 18500 3380 Watching for file changes with StatReloader
WARNING 2025-03-26 13:09:38,879 log 18500 21956 Not Found: /favicon.ico
INFO 2025-03-26 13:15:39,952 autoreload 18792 7872 Watching for file changes with StatReloader
WARNING 2025-03-26 13:15:42,927 log 18792 19948 Not Found: /favicon.ico
WARNING 2025-03-26 13:15:56,835 log 18792 19948 Not Found: /favicon.ico
WARNING 2025-03-26 13:15:57,514 log 18792 19948 Not Found: /favicon.ico
WARNING 2025-03-26 13:15:57,733 log 18792 19948 Not Found: /favicon.ico
WARNING 2025-03-26 13:16:10,421 log 18792 10512 Not Found: /favicon.ico
INFO 2025-03-26 13:17:51,365 autoreload 20664 17384 Watching for file changes with StatReloader
WARNING 2025-03-26 13:17:55,789 log 20664 21388 Not Found: /favicon.ico
ERROR 2025-03-26 13:18:10,476 log 20664 9224 Internal Server Error: /site-admins/messages/delete/9/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 150, in delete_message
    return JsonResponse({'status': 'success'})
           ^^^^^^^^^^^^
NameError: name 'JsonResponse' is not defined
ERROR 2025-03-26 13:18:24,249 log 20664 5384 Internal Server Error: /site-admins/messages/delete/8/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 150, in delete_message
    return JsonResponse({'status': 'success'})
           ^^^^^^^^^^^^
NameError: name 'JsonResponse' is not defined
ERROR 2025-03-26 13:19:46,696 log 20664 22244 Internal Server Error: /site-admins/messages/delete/7/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 150, in delete_message
    return JsonResponse({'status': 'success'})
           ^^^^^^^^^^^^
NameError: name 'JsonResponse' is not defined
INFO 2025-03-26 13:20:20,118 autoreload 20664 17384 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 13:20:21,034 autoreload 17672 22256 Watching for file changes with StatReloader
INFO 2025-03-26 13:24:00,016 autoreload 17672 22256 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 13:24:01,020 autoreload 19464 7956 Watching for file changes with StatReloader
INFO 2025-03-26 13:32:05,197 autoreload 19464 7956 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 13:32:06,304 autoreload 9564 18856 Watching for file changes with StatReloader
INFO 2025-03-26 13:32:16,032 autoreload 9564 18856 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 13:32:16,998 autoreload 3384 14272 Watching for file changes with StatReloader
INFO 2025-03-26 13:35:45,054 autoreload 3384 14272 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 13:35:46,065 autoreload 21324 22968 Watching for file changes with StatReloader
INFO 2025-03-26 13:44:58,155 autoreload 21324 22968 F:\DOCUMENTS\VRO2025\VRO_App3\models.py changed, reloading.
INFO 2025-03-26 13:44:59,233 autoreload 3856 15900 Watching for file changes with StatReloader
INFO 2025-03-26 13:45:06,295 autoreload 3856 15900 F:\DOCUMENTS\VRO2025\VRO_App3\urls.py changed, reloading.
INFO 2025-03-26 13:45:07,181 autoreload 22680 12460 Watching for file changes with StatReloader
INFO 2025-03-26 13:45:58,159 autoreload 22680 12460 F:\DOCUMENTS\VRO2025\VRO_App3\models.py changed, reloading.
INFO 2025-03-26 13:45:59,121 autoreload 7200 22872 Watching for file changes with StatReloader
ERROR 2025-03-26 13:46:18,179 views 7200 19604 Error in site_admins_dashboard: name 'InternalMessage' is not defined
ERROR 2025-03-26 13:46:29,938 views 7200 8164 Error in site_admins_dashboard: name 'InternalMessage' is not defined
ERROR 2025-03-26 13:46:39,608 views 7200 20040 Error in site_admins_dashboard: name 'InternalMessage' is not defined
INFO 2025-03-26 13:50:19,465 autoreload 7200 22872 F:\DOCUMENTS\VRO2025\VRO_App1\views.py changed, reloading.
INFO 2025-03-26 13:50:20,534 autoreload 5512 5540 Watching for file changes with StatReloader
INFO 2025-03-26 13:50:26,976 autoreload 5512 5540 F:\DOCUMENTS\VRO2025\VRO_App1\views.py changed, reloading.
INFO 2025-03-26 13:50:28,036 autoreload 23400 23316 Watching for file changes with StatReloader
ERROR 2025-03-26 13:50:45,656 views 23400 340 Error in site_admins_dashboard: name 'InternalMessage' is not defined
INFO 2025-03-26 13:51:38,418 autoreload 23400 23316 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 13:51:39,390 autoreload 22400 21908 Watching for file changes with StatReloader
INFO 2025-03-26 13:54:46,637 autoreload 22400 21908 F:\DOCUMENTS\VRO2025\VRO_Project\settings.py changed, reloading.
INFO 2025-03-26 13:54:47,690 autoreload 6024 11040 Watching for file changes with StatReloader
INFO 2025-03-26 13:54:54,196 autoreload 6024 11040 F:\DOCUMENTS\VRO2025\VRO_App1\views.py changed, reloading.
INFO 2025-03-26 13:54:55,278 autoreload 9864 7896 Watching for file changes with StatReloader
INFO 2025-03-26 13:55:21,363 autoreload 9864 7896 F:\DOCUMENTS\VRO2025\VRO_App1\views.py changed, reloading.
INFO 2025-03-26 13:55:22,427 autoreload 21284 23236 Watching for file changes with StatReloader
ERROR 2025-03-26 13:55:39,589 log 21284 10336 Internal Server Error: /accounts/google/login/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\oauth2\views.py", line 81, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\base\mixins.py", line 19, in dispatch
    return self.login(request, *args, **kwargs)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\oauth2\views.py", line 109, in login
    app = provider.get_app(self.request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\base\provider.py", line 30, in get_app
    return adapter.get_app(request, self.id, config=config)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\adapter.py", line 203, in get_app
    app = SocialApp.objects.get_current(provider, request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\models.py", line 33, in get_current
    app = self.get(provider=provider)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
allauth.socialaccount.models.SocialApp.DoesNotExist: SocialApp matching query does not exist.
ERROR 2025-03-26 13:56:22,215 log 21284 5880 Internal Server Error: /accounts/google/login/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\oauth2\views.py", line 81, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\base\mixins.py", line 19, in dispatch
    return self.login(request, *args, **kwargs)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\oauth2\views.py", line 109, in login
    app = provider.get_app(self.request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\base\provider.py", line 30, in get_app
    return adapter.get_app(request, self.id, config=config)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\adapter.py", line 203, in get_app
    app = SocialApp.objects.get_current(provider, request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\models.py", line 33, in get_current
    app = self.get(provider=provider)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
allauth.socialaccount.models.SocialApp.DoesNotExist: SocialApp matching query does not exist.
INFO 2025-03-26 13:58:00,387 autoreload 21284 23236 F:\DOCUMENTS\VRO2025\VRO_Project\settings.py changed, reloading.
INFO 2025-03-26 13:58:01,398 autoreload 22452 22244 Watching for file changes with StatReloader
ERROR 2025-03-26 13:58:02,888 log 22452 1852 Internal Server Error: /accounts/google/login/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\oauth2\views.py", line 81, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\base\mixins.py", line 19, in dispatch
    return self.login(request, *args, **kwargs)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\oauth2\views.py", line 109, in login
    app = provider.get_app(self.request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\base\provider.py", line 30, in get_app
    return adapter.get_app(request, self.id, config=config)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\adapter.py", line 203, in get_app
    app = SocialApp.objects.get_current(provider, request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\models.py", line 31, in get_current
    app = self.get(sites__id=site.id, provider=provider)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
allauth.socialaccount.models.SocialApp.DoesNotExist: SocialApp matching query does not exist.
INFO 2025-03-26 13:58:36,670 autoreload 10776 14904 Watching for file changes with StatReloader
ERROR 2025-03-26 13:58:45,716 log 10776 22836 Internal Server Error: /accounts/google/login/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\oauth2\views.py", line 81, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\base\mixins.py", line 19, in dispatch
    return self.login(request, *args, **kwargs)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\oauth2\views.py", line 109, in login
    app = provider.get_app(self.request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\base\provider.py", line 30, in get_app
    return adapter.get_app(request, self.id, config=config)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\adapter.py", line 203, in get_app
    app = SocialApp.objects.get_current(provider, request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\models.py", line 31, in get_current
    app = self.get(sites__id=site.id, provider=provider)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
allauth.socialaccount.models.SocialApp.DoesNotExist: SocialApp matching query does not exist.
INFO 2025-03-26 14:01:41,599 autoreload 21380 12096 Watching for file changes with StatReloader
ERROR 2025-03-26 14:01:43,210 log 21380 19232 Internal Server Error: /accounts/google/login/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\oauth2\views.py", line 81, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\base\mixins.py", line 19, in dispatch
    return self.login(request, *args, **kwargs)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\oauth2\views.py", line 109, in login
    app = provider.get_app(self.request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\base\provider.py", line 30, in get_app
    return adapter.get_app(request, self.id, config=config)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\adapter.py", line 203, in get_app
    app = SocialApp.objects.get_current(provider, request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\models.py", line 31, in get_current
    app = self.get(sites__id=site.id, provider=provider)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\db\models\query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
allauth.socialaccount.models.SocialApp.DoesNotExist: SocialApp matching query does not exist.
INFO 2025-03-26 14:01:46,882 autoreload 21380 12096 F:\DOCUMENTS\VRO2025\VRO_Project\settings.py changed, reloading.
INFO 2025-03-26 14:01:47,883 autoreload 14680 15356 Watching for file changes with StatReloader
INFO 2025-03-26 14:04:40,572 autoreload 14680 15356 F:\DOCUMENTS\VRO2025\VRO_App1\middleware.py changed, reloading.
INFO 2025-03-26 14:04:41,701 autoreload 2948 11328 Watching for file changes with StatReloader
INFO 2025-03-26 14:05:44,163 autoreload 21000 1240 Watching for file changes with StatReloader
WARNING 2025-03-26 14:06:29,952 log 21000 22272 Not Found: /favicon.ico
WARNING 2025-03-26 14:06:39,041 log 21000 11680 Not Found: /favicon.ico
WARNING 2025-03-26 14:06:48,269 log 21000 12244 Not Found: /favicon.ico
WARNING 2025-03-26 14:06:50,098 log 21000 20712 Not Found: /favicon.ico
WARNING 2025-03-26 14:06:53,058 log 21000 11364 Not Found: /favicon.ico
INFO 2025-03-26 14:07:33,232 autoreload 21000 1240 F:\DOCUMENTS\VRO2025\VRO_App2\adapters.py changed, reloading.
INFO 2025-03-26 14:07:34,240 autoreload 18568 18876 Watching for file changes with StatReloader
WARNING 2025-03-26 14:07:36,115 log 18568 19608 Not Found: /favicon.ico
INFO 2025-03-26 14:07:41,833 autoreload 18568 18876 F:\DOCUMENTS\VRO2025\VRO_App1\views.py changed, reloading.
INFO 2025-03-26 14:07:42,817 autoreload 22452 7240 Watching for file changes with StatReloader
INFO 2025-03-26 14:07:50,319 autoreload 22452 7240 F:\DOCUMENTS\VRO2025\VRO_App1\urls.py changed, reloading.
INFO 2025-03-26 14:07:51,381 autoreload 19264 16176 Watching for file changes with StatReloader
WARNING 2025-03-26 14:07:55,448 log 19264 12756 Not Found: /favicon.ico
WARNING 2025-03-26 14:08:04,070 log 19264 17144 Not Found: /favicon.ico
WARNING 2025-03-26 14:08:06,909 log 19264 17144 Not Found: /favicon.ico
INFO 2025-03-26 14:08:15,321 autoreload 15452 12236 Watching for file changes with StatReloader
WARNING 2025-03-26 14:08:17,252 log 15452 21552 Not Found: /favicon.ico
ERROR 2025-03-26 14:08:21,670 log 15452 21284 Internal Server Error: /accounts/google/login/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\oauth2\views.py", line 81, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\base\mixins.py", line 19, in dispatch
    return self.login(request, *args, **kwargs)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\oauth2\views.py", line 109, in login
    app = provider.get_app(self.request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\providers\base\provider.py", line 29, in get_app
    adapter = get_adapter(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\socialaccount\adapter.py", line 208, in get_adapter
    return import_attribute(app_settings.ADAPTER)(request)
           ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\allauth\utils.py", line 156, in import_attribute
    ret = getattr(importlib.import_module(pkg), attr)
                  ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Program Files\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "F:\DOCUMENTS\VRO2025\VRO_App2\adapters.py", line 3, in <module>
    from VRO_App2.models import UserProfile, Group
ImportError: cannot import name 'Group' from 'VRO_App2.models' (F:\DOCUMENTS\VRO2025\VRO_App2\models.py)
WARNING 2025-03-26 14:09:07,066 log 15452 21284 Not Found: /favicon.ico
INFO 2025-03-26 14:10:26,698 autoreload 15452 12236 F:\DOCUMENTS\VRO2025\VRO_App2\adapters.py changed, reloading.
INFO 2025-03-26 14:10:27,723 autoreload 20500 1432 Watching for file changes with StatReloader
WARNING 2025-03-26 14:10:29,622 log 20500 15636 Not Found: /favicon.ico
WARNING 2025-03-26 14:10:49,554 log 20500 20864 Not Found: /favicon.ico
WARNING 2025-03-26 14:13:31,934 log 20500 23892 Not Found: /favicon.ico
INFO 2025-03-26 14:14:15,307 autoreload 20500 1432 F:\DOCUMENTS\VRO2025\VRO_App1\views.py changed, reloading.
INFO 2025-03-26 14:14:16,317 autoreload 23340 9380 Watching for file changes with StatReloader
WARNING 2025-03-26 14:14:19,878 log 23340 24228 Not Found: /favicon.ico
WARNING 2025-03-26 14:14:57,657 log 23340 19036 Not Found: /favicon.ico
WARNING 2025-03-26 14:15:06,033 log 23340 19036 Not Found: /favicon.ico
WARNING 2025-03-26 14:15:09,888 log 23340 21580 Not Found: /favicon.ico
WARNING 2025-03-26 14:16:00,564 log 23340 21580 Not Found: /favicon.ico
WARNING 2025-03-26 14:16:02,540 log 23340 6648 Not Found: /favicon.ico
WARNING 2025-03-26 14:18:00,731 log 23340 6648 Not Found: /favicon.ico
WARNING 2025-03-26 14:18:04,509 log 23340 21992 Not Found: /favicon.ico
WARNING 2025-03-26 14:18:05,387 log 23340 20888 Not Found: /favicon.ico
WARNING 2025-03-26 14:18:11,057 log 23340 20888 Not Found: /favicon.ico
WARNING 2025-03-26 14:19:44,715 log 23340 20888 Not Found: /favicon.ico
INFO 2025-03-26 14:19:53,791 autoreload 23340 9380 F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py changed, reloading.
INFO 2025-03-26 14:19:54,827 autoreload 16856 14404 Watching for file changes with StatReloader
INFO 2025-03-26 14:20:02,341 autoreload 16856 14404 F:\DOCUMENTS\VRO2025\VRO_App1\middleware.py changed, reloading.
INFO 2025-03-26 14:20:03,336 autoreload 22240 6652 Watching for file changes with StatReloader
INFO 2025-03-26 14:20:09,732 autoreload 22240 6652 F:\DOCUMENTS\VRO2025\VRO_Project\settings.py changed, reloading.
INFO 2025-03-26 14:20:10,734 autoreload 23972 22704 Watching for file changes with StatReloader
WARNING 2025-03-26 14:20:17,586 log 23972 13244 Not Found: /favicon.ico
WARNING 2025-03-26 14:20:18,476 log 23972 10788 Not Found: /favicon.ico
INFO 2025-03-26 14:22:41,482 autoreload 23972 22704 F:\DOCUMENTS\VRO2025\VRO_App1\views.py changed, reloading.
INFO 2025-03-26 14:22:42,525 autoreload 20432 16036 Watching for file changes with StatReloader
WARNING 2025-03-26 14:22:50,045 log 20432 14380 Not Found: /favicon.ico
WARNING 2025-03-26 14:23:29,012 log 20432 14380 Not Found: /favicon.ico
WARNING 2025-03-26 14:23:32,058 log 20432 14380 Not Found: /favicon.ico
INFO 2025-03-26 14:25:25,603 autoreload 20432 16036 F:\DOCUMENTS\VRO2025\VRO_App1\middleware.py changed, reloading.
INFO 2025-03-26 14:25:26,638 autoreload 23260 22036 Watching for file changes with StatReloader
WARNING 2025-03-26 14:25:33,149 log 23260 18276 Not Found: /favicon.ico
INFO 2025-03-26 14:28:09,252 autoreload 23260 22036 F:\DOCUMENTS\VRO2025\VRO_App1\views.py changed, reloading.
INFO 2025-03-26 14:28:10,340 autoreload 12152 9700 Watching for file changes with StatReloader
INFO 2025-03-26 14:28:16,012 autoreload 12152 9700 F:\DOCUMENTS\VRO2025\VRO_App1\views.py changed, reloading.
INFO 2025-03-26 14:28:17,031 autoreload 24172 23932 Watching for file changes with StatReloader
WARNING 2025-03-26 14:28:42,056 log 24172 15680 Not Found: /favicon.ico
WARNING 2025-03-26 14:28:44,134 log 24172 20956 Not Found: /favicon.ico
WARNING 2025-03-26 14:28:50,033 log 24172 16844 Not Found: /favicon.ico
WARNING 2025-03-26 14:37:22,372 log 24172 19056 Not Found: /favicon.ico
WARNING 2025-03-26 14:37:23,463 log 24172 24556 Not Found: /favicon.ico
WARNING 2025-03-26 14:37:30,463 log 24172 24556 Not Found: /favicon.ico
INFO 2025-03-26 14:37:43,103 autoreload 16040 21748 Watching for file changes with StatReloader
WARNING 2025-03-26 14:37:50,060 log 16040 8008 Not Found: /favicon.ico
WARNING 2025-03-26 14:37:51,882 log 16040 8008 Not Found: /favicon.ico
WARNING 2025-03-26 14:37:56,520 log 16040 5440 Not Found: /favicon.ico
INFO 2025-03-26 14:39:21,211 autoreload 16040 21748 F:\DOCUMENTS\VRO2025\VRO_App1\views.py changed, reloading.
INFO 2025-03-26 14:39:22,184 autoreload 22348 20828 Watching for file changes with StatReloader
WARNING 2025-03-26 14:39:23,953 log 22348 18336 Not Found: /favicon.ico
INFO 2025-03-26 14:39:27,391 autoreload 22348 20828 F:\DOCUMENTS\VRO2025\VRO_App1\urls.py changed, reloading.
INFO 2025-03-26 14:39:28,387 autoreload 25512 25540 Watching for file changes with StatReloader
WARNING 2025-03-26 14:39:37,645 log 25512 25384 Not Found: /favicon.ico
INFO 2025-03-26 14:40:10,581 autoreload 25512 25540 F:\DOCUMENTS\VRO2025\VRO_Project\settings.py changed, reloading.
INFO 2025-03-26 14:40:11,554 autoreload 6072 24804 Watching for file changes with StatReloader
INFO 2025-03-26 14:40:18,963 autoreload 6072 24804 F:\DOCUMENTS\VRO2025\VRO_App1\middleware.py changed, reloading.
INFO 2025-03-26 14:40:19,871 autoreload 12348 25048 Watching for file changes with StatReloader
WARNING 2025-03-26 14:40:43,928 middleware 12348 24312 Unauthenticated access attempt to admin area: /site-admins/group-allocations
WARNING 2025-03-26 14:41:06,857 log 12348 4028 Not Found: /favicon.ico
WARNING 2025-03-26 14:41:09,636 log 12348 25064 Not Found: /favicon.ico
WARNING 2025-03-26 14:41:15,994 log 12348 17208 Not Found: /favicon.ico
INFO 2025-03-26 14:42:59,512 autoreload 12348 25048 F:\DOCUMENTS\VRO2025\VRO_Project\settings.py changed, reloading.
INFO 2025-03-26 14:43:00,472 autoreload 24664 25368 Watching for file changes with StatReloader
INFO 2025-03-26 14:44:56,550 autoreload 24664 25368 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 14:44:57,512 autoreload 2440 4408 Watching for file changes with StatReloader
INFO 2025-03-26 14:45:10,707 autoreload 2440 4408 F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py changed, reloading.
INFO 2025-03-26 14:45:11,630 autoreload 4136 25116 Watching for file changes with StatReloader
INFO 2025-03-26 14:45:29,199 autoreload 308 3932 Watching for file changes with StatReloader
ERROR 2025-03-26 14:45:40,064 views 308 23440 Error in site_admins_dashboard: name 'InternalMessage' is not defined
INFO 2025-03-26 14:46:53,757 autoreload 308 3932 F:\DOCUMENTS\VRO2025\VRO_App1\views.py changed, reloading.
INFO 2025-03-26 14:46:54,695 autoreload 8204 13508 Watching for file changes with StatReloader
INFO 2025-03-26 14:46:59,792 autoreload 8204 13508 F:\DOCUMENTS\VRO2025\VRO_App1\urls.py changed, reloading.
INFO 2025-03-26 14:47:00,763 autoreload 24388 24628 Watching for file changes with StatReloader
ERROR 2025-03-26 14:47:19,100 views 24388 23416 Error in site_admins_dashboard: name 'InternalMessage' is not defined
INFO 2025-03-26 14:47:59,737 autoreload 24388 24628 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 14:48:00,617 autoreload 18192 2252 Watching for file changes with StatReloader
ERROR 2025-03-26 14:48:16,951 views 18192 24228 Error in site_admins_dashboard: name 'InternalMessage' is not defined
ERROR 2025-03-26 14:48:16,952 views 18192 24228 Full traceback:
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 61, in site_admins_dashboard
    'unread_internal_messages': InternalMessage.objects.filter(
                                ^^^^^^^^^^^^^^^
NameError: name 'InternalMessage' is not defined. Did you mean: 'internal_messages'?
INFO 2025-03-26 14:49:26,831 autoreload 18192 2252 F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py changed, reloading.
INFO 2025-03-26 14:49:27,753 autoreload 25248 21628 Watching for file changes with StatReloader
INFO 2025-03-26 14:49:35,064 autoreload 20812 23348 Watching for file changes with StatReloader
INFO 2025-03-26 14:49:47,972 decorators 20812 24016 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:49:47,974 decorators 20812 24016 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:49:47,977 decorators 20812 24016 User profile exists, group: site-admins
INFO 2025-03-26 14:49:47,977 decorators 20812 24016 Access <NAME_EMAIL> for group site-admins
ERROR 2025-03-26 14:49:47,984 views 20812 24016 Error in site_admins_dashboard: name 'InternalMessage' is not defined
ERROR 2025-03-26 14:49:47,984 views 20812 24016 Full traceback:
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 61, in site_admins_dashboard
    'unread_internal_messages': InternalMessage.objects.filter(
                                ^^^^^^^^^^^^^^^
NameError: name 'InternalMessage' is not defined. Did you mean: 'internal_messages'?
INFO 2025-03-26 14:50:47,640 autoreload 20812 23348 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 14:50:48,576 autoreload 13064 18328 Watching for file changes with StatReloader
INFO 2025-03-26 14:51:16,432 decorators 13064 23416 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:51:16,434 decorators 13064 23416 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:51:16,436 decorators 13064 23416 User profile exists, group: site-admins
INFO 2025-03-26 14:51:16,437 decorators 13064 23416 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 14:51:28,968 decorators 13064 23416 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:51:28,983 decorators 13064 23416 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:51:29,013 decorators 13064 23416 User profile exists, group: site-admins
INFO 2025-03-26 14:51:29,013 decorators 13064 23416 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 14:51:31,883 decorators 13064 23416 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:51:31,885 decorators 13064 23416 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:51:31,888 decorators 13064 23416 User profile exists, group: site-admins
INFO 2025-03-26 14:51:31,889 decorators 13064 23416 Access <NAME_EMAIL> for group site-admins
ERROR 2025-03-26 14:51:31,933 log 13064 23416 Internal Server Error: /site-admins/messages/internal/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py", line 35, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 157, in internal_messages
    messages = InternalMessage.objects.filter(recipient=request.user).order_by('-sent_at')
               ^^^^^^^^^^^^^^^
NameError: name 'InternalMessage' is not defined. Did you mean: 'internal_messages'?
INFO 2025-03-26 14:51:48,904 autoreload 13064 18328 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 14:51:49,802 autoreload 5824 25084 Watching for file changes with StatReloader
INFO 2025-03-26 14:52:04,113 decorators 5824 20240 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:52:04,115 decorators 5824 20240 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:52:04,118 decorators 5824 20240 User profile exists, group: site-admins
INFO 2025-03-26 14:52:04,118 decorators 5824 20240 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 14:52:05,821 decorators 5824 20240 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:52:05,822 decorators 5824 20240 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:52:05,825 decorators 5824 20240 User profile exists, group: site-admins
INFO 2025-03-26 14:52:05,825 decorators 5824 20240 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 14:52:08,305 decorators 5824 20240 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:52:08,307 decorators 5824 20240 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:52:08,310 decorators 5824 20240 User profile exists, group: site-admins
INFO 2025-03-26 14:52:08,310 decorators 5824 20240 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 14:52:09,933 decorators 5824 20240 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:52:09,942 decorators 5824 20240 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:52:09,949 decorators 5824 20240 User profile exists, group: site-admins
INFO 2025-03-26 14:52:09,950 decorators 5824 20240 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 14:52:11,471 decorators 5824 20240 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:52:11,472 decorators 5824 20240 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:52:11,475 decorators 5824 20240 User profile exists, group: site-admins
INFO 2025-03-26 14:52:11,475 decorators 5824 20240 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 14:52:15,100 decorators 5824 20240 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:52:15,101 decorators 5824 20240 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:52:15,104 decorators 5824 20240 User profile exists, group: site-admins
INFO 2025-03-26 14:52:15,104 decorators 5824 20240 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 14:52:16,432 decorators 5824 20240 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:52:16,433 decorators 5824 20240 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:52:16,437 decorators 5824 20240 User profile exists, group: site-admins
INFO 2025-03-26 14:52:16,437 decorators 5824 20240 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 14:52:37,462 decorators 5824 20240 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:52:37,468 decorators 5824 20240 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:52:37,473 decorators 5824 20240 User profile exists, group: site-admins
INFO 2025-03-26 14:52:37,473 decorators 5824 20240 Access <NAME_EMAIL> for group site-admins
ERROR 2025-03-26 14:52:42,248 log 5824 20240 Internal Server Error: /site-admins/messages/internal/send/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 186, in send_internal_message
    users = User.objects.filter(is_active=True).exclude(id=request.user.id)
            ^^^^
NameError: name 'User' is not defined
INFO 2025-03-26 14:54:25,283 autoreload 5824 25084 F:\DOCUMENTS\VRO2025\VRO_App3\views.py changed, reloading.
INFO 2025-03-26 14:54:26,267 autoreload 20828 25540 Watching for file changes with StatReloader
INFO 2025-03-26 14:54:29,681 decorators 20828 21856 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:54:29,683 decorators 20828 21856 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:54:29,695 decorators 20828 21856 User profile exists, group: site-admins
INFO 2025-03-26 14:54:29,695 decorators 20828 21856 Access <NAME_EMAIL> for group site-admins
ERROR 2025-03-26 14:54:31,228 log 20828 21856 Internal Server Error: /site-admins/messages/internal/send/
Traceback (most recent call last):
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "F:\DOCUMENTS\VRO2025\VRO_App3\views.py", line 194, in send_internal_message
    return render(request, 'VRO_App3/messages/send_internal_message.html', {
        'users': users,
        'template_type': 'site_admin'
    })
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "F:\DOCUMENTS\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: VRO_App3/messages/send_internal_message.html
WARNING 2025-03-26 14:54:31,379 log 20828 21856 Not Found: /favicon.ico
INFO 2025-03-26 14:56:44,578 decorators 20828 19036 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:56:44,579 decorators 20828 19036 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:56:44,582 decorators 20828 19036 User profile exists, group: site-admins
INFO 2025-03-26 14:56:44,582 decorators 20828 19036 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 14:56:53,684 decorators 20828 19036 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:56:53,686 decorators 20828 19036 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:56:53,690 decorators 20828 19036 User profile exists, group: site-admins
INFO 2025-03-26 14:56:53,690 decorators 20828 19036 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 14:58:44,237 decorators 20828 19036 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:58:44,239 decorators 20828 19036 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:58:44,242 decorators 20828 19036 User profile exists, group: site-admins
INFO 2025-03-26 14:58:44,243 decorators 20828 19036 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 14:58:49,954 decorators 20828 19036 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 14:58:49,956 decorators 20828 19036 User authenticated, email: <EMAIL>
INFO 2025-03-26 14:58:49,959 decorators 20828 19036 User profile exists, group: site-admins
INFO 2025-03-26 14:58:49,959 decorators 20828 19036 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:02:27,002 autoreload 20828 25540 F:\DOCUMENTS\VRO2025\VRO_App1\views.py changed, reloading.
INFO 2025-03-26 15:02:28,085 autoreload 20512 22940 Watching for file changes with StatReloader
INFO 2025-03-26 15:02:29,671 decorators 20512 7900 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:02:29,673 decorators 20512 7900 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:02:29,675 decorators 20512 7900 User profile exists, group: site-admins
INFO 2025-03-26 15:02:29,675 decorators 20512 7900 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:02:35,876 decorators 20512 7900 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:02:35,878 decorators 20512 7900 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:02:35,880 decorators 20512 7900 User profile exists, group: site-admins
INFO 2025-03-26 15:02:35,881 decorators 20512 7900 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:02:44,937 autoreload 20512 22940 F:\DOCUMENTS\VRO2025\VRO_App1\middleware.py changed, reloading.
INFO 2025-03-26 15:02:45,912 autoreload 24328 23888 Watching for file changes with StatReloader
INFO 2025-03-26 15:02:47,546 decorators 24328 24804 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:02:47,554 decorators 24328 24804 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:02:47,560 decorators 24328 24804 User profile exists, group: site-admins
INFO 2025-03-26 15:02:47,561 decorators 24328 24804 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:02:54,775 decorators 24328 24804 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:02:54,777 decorators 24328 24804 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:02:54,786 decorators 24328 24804 User profile exists, group: site-admins
INFO 2025-03-26 15:02:54,786 decorators 24328 24804 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:03:03,757 autoreload 24328 23888 F:\DOCUMENTS\VRO2025\VRO_App3\decorators.py changed, reloading.
INFO 2025-03-26 15:03:04,711 autoreload 26324 26240 Watching for file changes with StatReloader
INFO 2025-03-26 15:03:06,321 decorators 26324 26404 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:03:06,323 decorators 26324 26404 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:03:06,328 decorators 26324 26404 User profile exists, group: site-admins
INFO 2025-03-26 15:03:06,328 decorators 26324 26404 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:03:24,798 decorators 26324 26404 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:03:24,803 decorators 26324 26404 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:03:24,807 decorators 26324 26404 User profile exists, group: site-admins
INFO 2025-03-26 15:03:24,808 decorators 26324 26404 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:03:33,810 decorators 26324 26404 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:03:33,811 decorators 26324 26404 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:03:33,815 decorators 26324 26404 User profile exists, group: site-admins
INFO 2025-03-26 15:03:33,815 decorators 26324 26404 Access <NAME_EMAIL> for group site-admins
WARNING 2025-03-26 15:03:45,446 log 26324 26404 Not Found: /favicon.ico
WARNING 2025-03-26 15:08:10,803 log 26324 26440 Not Found: /favicon.ico
INFO 2025-03-26 15:08:22,001 autoreload 26324 26240 F:\DOCUMENTS\VRO2025\VRO_Project\settings.py changed, reloading.
INFO 2025-03-26 15:08:22,921 autoreload 24180 17840 Watching for file changes with StatReloader
INFO 2025-03-26 15:08:31,334 decorators 24180 1316 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:08:31,338 decorators 24180 1316 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:08:31,353 decorators 24180 1316 User profile exists, group: site-admins
INFO 2025-03-26 15:08:31,354 decorators 24180 1316 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:08:35,331 decorators 24180 1316 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:08:35,333 decorators 24180 1316 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:08:35,337 decorators 24180 1316 User profile exists, group: site-admins
INFO 2025-03-26 15:08:35,338 decorators 24180 1316 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:08:42,678 decorators 24180 1316 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:08:42,679 decorators 24180 1316 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:08:42,683 decorators 24180 1316 User profile exists, group: site-admins
INFO 2025-03-26 15:08:42,684 decorators 24180 1316 Access <NAME_EMAIL> for group site-admins
WARNING 2025-03-26 15:08:42,927 log 24180 1316 Not Found: /favicon.ico
INFO 2025-03-26 15:08:51,466 decorators 24180 18068 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:08:51,468 decorators 24180 18068 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:08:51,472 decorators 24180 18068 User profile exists, group: site-admins
INFO 2025-03-26 15:08:51,473 decorators 24180 18068 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:08:53,371 decorators 24180 18068 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:08:53,373 decorators 24180 18068 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:08:53,378 decorators 24180 18068 User profile exists, group: site-admins
INFO 2025-03-26 15:08:53,378 decorators 24180 18068 Access <NAME_EMAIL> for group site-admins
WARNING 2025-03-26 15:08:53,586 log 24180 18068 Not Found: /favicon.ico
INFO 2025-03-26 15:08:59,144 decorators 24180 26084 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:08:59,146 decorators 24180 26084 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:08:59,150 decorators 24180 26084 User profile exists, group: site-admins
INFO 2025-03-26 15:08:59,150 decorators 24180 26084 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:09:01,230 decorators 24180 26084 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:01,232 decorators 24180 26084 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:01,235 decorators 24180 26084 User profile exists, group: site-admins
INFO 2025-03-26 15:09:01,236 decorators 24180 26084 Access <NAME_EMAIL> for group site-admins
WARNING 2025-03-26 15:09:01,443 log 24180 26084 Not Found: /favicon.ico
INFO 2025-03-26 15:09:06,123 decorators 24180 25820 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:06,125 decorators 24180 25820 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:06,129 decorators 24180 25820 User profile exists, group: site-admins
INFO 2025-03-26 15:09:06,129 decorators 24180 25820 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:09:07,992 decorators 24180 25820 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:07,994 decorators 24180 25820 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:08,005 decorators 24180 25820 User profile exists, group: site-admins
INFO 2025-03-26 15:09:08,006 decorators 24180 25820 Access <NAME_EMAIL> for group site-admins
WARNING 2025-03-26 15:09:08,298 log 24180 25820 Not Found: /favicon.ico
INFO 2025-03-26 15:09:11,946 decorators 24180 12816 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:11,948 decorators 24180 12816 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:11,952 decorators 24180 12816 User profile exists, group: site-admins
INFO 2025-03-26 15:09:11,953 decorators 24180 12816 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:09:14,118 decorators 24180 12816 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:14,120 decorators 24180 12816 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:14,124 decorators 24180 12816 User profile exists, group: site-admins
INFO 2025-03-26 15:09:14,125 decorators 24180 12816 Access <NAME_EMAIL> for group site-admins
WARNING 2025-03-26 15:09:14,343 log 24180 12816 Not Found: /favicon.ico
INFO 2025-03-26 15:09:17,384 decorators 24180 25344 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:17,386 decorators 24180 25344 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:17,392 decorators 24180 25344 User profile exists, group: site-admins
INFO 2025-03-26 15:09:17,392 decorators 24180 25344 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:09:18,718 decorators 24180 25344 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:18,719 decorators 24180 25344 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:18,723 decorators 24180 25344 User profile exists, group: site-admins
INFO 2025-03-26 15:09:18,724 decorators 24180 25344 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:09:22,175 decorators 24180 25344 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:22,177 decorators 24180 25344 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:22,183 decorators 24180 25344 User profile exists, group: site-admins
INFO 2025-03-26 15:09:22,183 decorators 24180 25344 Access <NAME_EMAIL> for group site-admins
WARNING 2025-03-26 15:09:23,772 log 24180 25344 Not Found: /favicon.ico
INFO 2025-03-26 15:09:28,263 decorators 24180 21512 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:28,264 decorators 24180 21512 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:28,270 decorators 24180 21512 User profile exists, group: site-admins
INFO 2025-03-26 15:09:28,270 decorators 24180 21512 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:09:29,530 decorators 24180 21512 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:29,531 decorators 24180 21512 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:29,535 decorators 24180 21512 User profile exists, group: site-admins
INFO 2025-03-26 15:09:29,536 decorators 24180 21512 Access <NAME_EMAIL> for group site-admins
WARNING 2025-03-26 15:09:29,711 log 24180 21512 Not Found: /favicon.ico
INFO 2025-03-26 15:09:33,174 decorators 24180 1852 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:33,175 decorators 24180 1852 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:33,180 decorators 24180 1852 User profile exists, group: site-admins
INFO 2025-03-26 15:09:33,180 decorators 24180 1852 Access <NAME_EMAIL> for group site-admins
INFO 2025-03-26 15:09:36,244 decorators 24180 1852 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:36,246 decorators 24180 1852 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:36,250 decorators 24180 1852 User profile exists, group: site-admins
INFO 2025-03-26 15:09:36,250 decorators 24180 1852 Access <NAME_EMAIL> for group site-admins
WARNING 2025-03-26 15:09:36,501 log 24180 1852 Not Found: /favicon.ico
INFO 2025-03-26 15:09:39,837 decorators 24180 23660 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 15:09:39,838 decorators 24180 23660 User authenticated, email: <EMAIL>
INFO 2025-03-26 15:09:39,842 decorators 24180 23660 User profile exists, group: site-admins
INFO 2025-03-26 15:09:39,842 decorators 24180 23660 Access <NAME_EMAIL> for group site-admins
WARNING 2025-03-26 15:09:42,752 log 24180 23660 Not Found: /favicon.ico
WARNING 2025-03-26 15:09:46,480 log 24180 10548 Not Found: /favicon.ico
WARNING 2025-03-26 15:09:58,671 log 24180 22048 Not Found: /favicon.ico
INFO 2025-03-26 15:19:40,118 autoreload 24180 17840 F:\DOCUMENTS\VRO2025\VRO_Project\settings.py changed, reloading.
INFO 2025-03-26 15:19:41,098 autoreload 24444 24784 Watching for file changes with StatReloader
WARNING 2025-03-26 15:19:42,901 log 24444 18000 Not Found: /favicon.ico
INFO 2025-03-26 15:22:58,800 autoreload 24444 24784 F:\DOCUMENTS\VRO2025\VRO_Project\settings.py changed, reloading.
INFO 2025-03-26 15:22:59,770 autoreload 25340 8248 Watching for file changes with StatReloader
WARNING 2025-03-26 15:23:01,591 log 25340 24972 Not Found: /favicon.ico
INFO 2025-03-26 16:39:18,906 autoreload 2688 19892 Watching for file changes with StatReloader
WARNING 2025-03-26 16:39:22,737 log 2688 25156 Not Found: /favicon.ico
INFO 2025-03-26 16:39:26,806 decorators 2688 25156 Checking group access <NAME_EMAIL> requiring group site-admins
INFO 2025-03-26 16:39:26,808 decorators 2688 25156 User authenticated, email: <EMAIL>
INFO 2025-03-26 16:39:26,812 decorators 2688 25156 User profile exists, group: site-admins
INFO 2025-03-26 16:39:26,813 decorators 2688 25156 Access <NAME_EMAIL> for group site-admins
