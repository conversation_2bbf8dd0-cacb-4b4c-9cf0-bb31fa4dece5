Internal Server Error: /support/
Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\VRO_App1\views.py", line 31, in support
    return render(request, 'VRO_App1/support.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 295, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 228, in do_block
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'block' tag with name 'content' appears more than once
"GET /support/ HTTP/1.1" 500 11716
Internal Server Error: /support/
Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\VRO_App1\views.py", line 256, in support_view
    return render(request, 'support.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: support.html
"GET /support/ HTTP/1.1" 500 11716
Internal Server Error: /support/
Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\VRO_App1\views.py", line 256, in support_view
    return render(request, 'support.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: support.html
"GET /support/ HTTP/1.1" 500 11716
Internal Server Error: /support/
Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\VRO_App1\views.py", line 256, in support_view
    return render(request, 'support.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: support.html
"GET /support/ HTTP/1.1" 500 11716
Internal Server Error: /support/
Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\VRO_App1\views.py", line 256, in support_view
    return render(request, 'support.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: support.html
"GET /support/ HTTP/1.1" 500 11716
Internal Server Error: /support/
Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\VRO_App1\views.py", line 256, in support_view
    return render(request, 'support.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: support.html
"GET /support/ HTTP/1.1" 500 11716
"GET /get-started/ HTTP/1.1" 500 59
Internal Server Error: /
Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 134, in render
    compiled_parent = self.get_parent(context)
                      ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 131, in get_parent
    return self.find_template(parent, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 109, in find_template
    template, origin = context.template.engine.find_template(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\engine.py", line 163, in find_template
    raise TemplateDoesNotExist(name, tried=tried)
django.template.exceptions.TemplateDoesNotExist: base.html

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\VRO_App1\views.py", line 22, in index
    return render(request, 'VRO_App1/index.html')  # Fixed template path
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 109, in render
    reraise(exc, self.backend)
  File "F:\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 130, in reraise
    raise new from exc
django.template.exceptions.TemplateDoesNotExist: base.html
"GET / HTTP/1.1" 500 145
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /50
"GET /50 HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /fuel-expenses/
"GET /fuel-expenses/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /__reload__/events/
"GET /__reload__/events/ HTTP/1.1" 500 12169
Internal Server Error: /supp
"GET /supp HTTP/1.1" 500 12169
Internal Server Error: /supp
"GET /supp HTTP/1.1" 500 12169
Internal Server Error: /signup/
Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'provider_login_url'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\VRO_App1\views.py", line 332, in signup_view
    return render(request, 'VRO_App1/signup.html', {'form': form})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 295, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 563, in invalid_block_tag
    raise self.error(
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 71: 'provider_login_url', expected 'endblock'. Did you forget to register or load this tag?
"GET /signup/ HTTP/1.1" 500 13593
Internal Server Error: /login/
Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\VRO_App1\views.py", line 367, in login_view
    return render(request, 'VRO_App1/login.html', {
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'account_login_by_code' not found. 'account_login_by_code' is not a valid view function or pattern name.
"GET /login/ HTTP/1.1" 500 13593
Internal Server Error: /signup/
Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'provider_login_url'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\VRO_App1\views.py", line 332, in signup_view
    return render(request, 'VRO_App1/signup.html', {'form': form})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 295, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 563, in invalid_block_tag
    raise self.error(
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 71: 'provider_login_url', expected 'endblock'. Did you forget to register or load this tag?
"GET /signup/ HTTP/1.1" 500 13593
Internal Server Error: /login/
Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\VRO_App1\views.py", line 367, in login_view
    return render(request, 'VRO_App1/login.html', {
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'account_login_by_code' not found. 'account_login_by_code' is not a valid view function or pattern name.
"GET /login/ HTTP/1.1" 500 13593
Internal Server Error: /login/
Traceback (most recent call last):
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\VRO_App1\views.py", line 367, in login_view
    return render(request, 'VRO_App1/login.html', {
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\VRO2025\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'account_login_by_code' not found. 'account_login_by_code' is not a valid view function or pattern name.
"GET /login/ HTTP/1.1" 500 13593
