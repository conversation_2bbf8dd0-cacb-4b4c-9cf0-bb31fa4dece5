import logging
from django import forms
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.db.models import ProtectedError, Q
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.http import JsonResponse

# Set up logger
logger = logging.getLogger(__name__)

# Get User model
User = get_user_model()

# Import models
from VRO_App0.models import PricingCard
from VRO_App1.models import Document, DocumentCategory, Contact
from VRO_App2.models import (
    Gender, 
    Race, 
    Condition, 
    UserProfile, 
    UserGroup,
    PendingGroupAllocation
)
from .models import InternalMessage
from .forms import GroupAllocationForm, UserGroupForm
from .decorators import group_required

# Import forms
from .forms import (
    GenderForm, RaceForm, ConditionForm,
    DocumentForm  # Make sure this exists in forms.py
)

def check_site_admin(user):
    return user.userprofile.group and user.userprofile.group.code == 'site-admins'

@login_required
@group_required('site-admins')
def site_admins_dashboard(request):
    try:
        User = get_user_model()
        
        context = {
            'debug_info': {
                'total_users': User.objects.count(),
                'unread_messages': Contact.objects.filter(have_replied__isnull=True).count(),
                'unread_internal_messages': InternalMessage.objects.filter(
                    recipient=request.user,
                    read_at__isnull=True
                ).count()
            }
        }
        
        return render(request, 'VRO_App3/site-admins/dashboard.html', {
            **context,
            'template_type': 'site_admin'
        })
    except Exception as e:
        logger.error(f"Error in site_admins_dashboard: {str(e)}")
        logger.exception("Full traceback:")
        return render(request, 'VRO_App3/site-admins/dashboard.html', {
            'debug_info': {
                'total_users': 0,
                'unread_messages': 0,
                'unread_internal_messages': 0
            },
            'template_type': 'site_admin',
            'error_message': 'There was an error loading some dashboard data.'
        })

@login_required
@group_required('site-admins')
def manage_pricing(request):
    pricing_cards = PricingCard.objects.all().order_by('Card_ID')
    return render(request, 'VRO_App3/site-admins/pricing/manage_pricing.html', {
        'pricing_cards': pricing_cards,
        'template_type': 'site_admin'
    })

@login_required
@group_required('site-admins')
def manage_articles(request):
    articles = Document.objects.filter(category__name='Articles').order_by('-posted_date', '-created_at')
    return render(request, 'VRO_App3/site-admins/articles/manage_articles.html', {
        'articles': articles,
        'template_type': 'site_admin'
    })

@login_required
@group_required('site-admins')
def manage_blogs(request):
    blogs = Document.objects.filter(category__name='Blogs').order_by('-posted_date', '-created_at')
    return render(request, 'VRO_App3/site-admins/blogs/manage_blogs.html', {
        'blogs': blogs,
        'template_type': 'site_admin'
    })

@login_required
@group_required('site-admins')
def contact_messages(request):
    return render(request, 'VRO_App3/messages/contact_messages.html')

from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.http import JsonResponse

@login_required
@group_required('site-admins')
def public_messages(request):
    messages = Contact.objects.all().order_by('-submission_time')
    return render(request, 'VRO_App3/messages/public_messages.html', {
        'messages': messages
    })

@login_required
@group_required('site-admins')
def view_message(request, message_id):
    message = get_object_or_404(Contact, id=message_id)
    
    if not message.mail_read:
        message.mail_read = timezone.now()
        message.save()
    
    if request.method == 'POST':
        reply_text = request.POST.get('reply')
        if reply_text:
            # Send email
            send_mail(
                f'Re: {message.subject}',
                reply_text,
                settings.DEFAULT_FROM_EMAIL,
                [message.email],
                fail_silently=False,
            )
            
            # Update message status
            message.have_replied = timezone.now()
            message.save()
            
            messages.success(request, 'Reply sent successfully')
            return redirect('VRO_App3:public_messages')
    
    return render(request, 'VRO_App3/messages/view_message.html', {
        'message': message
    })

@login_required
@group_required('site-admins')
def delete_message(request, message_id):
    if request.method == 'POST':
        message = get_object_or_404(Contact, id=message_id)
        message.delete()
        return JsonResponse({'status': 'success'})
    return redirect('VRO_App3:public_messages')  # Redirect on GET request instead of showing confirmation

@login_required
@group_required('site-admins')
def internal_messages(request):
    messages = InternalMessage.objects.filter(recipient=request.user).order_by('-sent_at')
    return render(request, 'VRO_App3/messages/internal_messages.html', {
        'messages': messages,
        'template_type': 'site_admin'
    })

@login_required
def send_internal_message(request):
    if request.method == 'POST':
        recipient_id = request.POST.get('recipient')
        subject = request.POST.get('subject')
        message = request.POST.get('message')
        
        try:
            recipient = User.objects.get(id=recipient_id)
            InternalMessage.objects.create(
                sender=request.user,
                recipient=recipient,
                subject=subject,
                message=message
            )
            messages.success(request, 'Message sent successfully')
            return redirect('VRO_App3:internal_messages')
        except User.DoesNotExist:
            messages.error(request, 'Recipient not found')
    
    users = User.objects.filter(is_active=True).exclude(id=request.user.id)
    return render(request, 'VRO_App3/messages/send_internal_message.html', {
        'users': users,
        'template_type': 'site_admin'
    })

@login_required
def view_internal_message(request, message_id):
    message = get_object_or_404(InternalMessage, id=message_id, recipient=request.user)
    
    if not message.read_at:
        message.read_at = timezone.now()
        message.save()
    
    return render(request, 'VRO_App3/messages/view_internal_message.html', {
        'message': message,
        'template_type': 'site_admin'
    })

@login_required
@group_required('site-admins')
def manage_group_allocations(request):
    if request.method == 'POST':
        form = GroupAllocationForm(request.POST)
        if form.is_valid():
            try:
                email = form.cleaned_data['email']
                group = form.cleaned_data['group']
                
                # Check for existing allocation
                existing_allocation = PendingGroupAllocation.objects.filter(
                    email=email,
                    group=group
                ).exists()
                
                if existing_allocation:
                    messages.error(request, f'An allocation for {email} to this group already exists')
                    return redirect('VRO_App3:manage_group_allocations')
                
                allocation = form.save(commit=False)
                allocation.created_by = request.user
                allocation.save()
                logger.info(f"Group allocation created for {allocation.email} by {request.user.email}")
                messages.success(request, f'Group allocation created for {allocation.email}')
                return redirect('VRO_App3:manage_group_allocations')
            except Exception as e:
                logger.error(f"Error creating group allocation: {str(e)}")
                messages.error(request, 'Error creating group allocation')
    else:
        form = GroupAllocationForm()
    
    allocations = PendingGroupAllocation.objects.all()
    return render(request, 'VRO_App3/site-admins/manage_group_allocations.html', {
        'form': form,
        'allocations': allocations,
        'current_user_email': request.user.email  # Pass current user's email to template
    })

@login_required
@group_required('site-admins')
def delete_group_allocation(request, allocation_id):
    if not check_site_admin(request.user):
        logger.warning(f"Non-admin user {request.user.email} attempted to delete allocation")
        raise PermissionDenied
    
    try:
        allocation = PendingGroupAllocation.objects.get(id=allocation_id)
        
        # Prevent deletion of own allocation
        if allocation.email == request.user.email:
            logger.warning(f"User {request.user.email} attempted to delete their own allocation")
            messages.error(request, 'You cannot delete your own group allocation')
            return redirect('VRO_App3:manage_group_allocations')
        
        if request.method == 'POST':
            email = allocation.email
            was_used = allocation.is_used
            
            # If allocation was used, remove group from user's profile
            if was_used:
                try:
                    User = get_user_model()
                    user = User.objects.get(email=email)
                    if hasattr(user, 'userprofile'):
                        user.userprofile.group = None
                        user.userprofile.save()
                        logger.info(f"Group access removed for user {email} by {request.user.email}")
                except User.DoesNotExist:
                    logger.error(f"User not found for email: {email}")
                    messages.error(request, f'User not found for email: {email}')
                    return redirect('VRO_App3:manage_group_allocations')
                except Exception as e:
                    logger.error(f"Error removing group access: {str(e)}")
                    messages.error(request, 'Error removing group access')
                    return redirect('VRO_App3:manage_group_allocations')
            
            # Delete the allocation record
            allocation.delete()
            
            if was_used:
                messages.success(request, f'Group access removed and allocation record deleted for {email}')
            else:
                messages.success(request, f'Pending allocation for {email} deleted successfully')
                
    except PendingGroupAllocation.DoesNotExist:
        logger.error(f"Attempt to delete non-existent allocation ID: {allocation_id}")
        messages.error(request, 'Allocation not found')
    except Exception as e:
        logger.error(f"Error deleting allocation: {str(e)}")
        messages.error(request, 'Error deleting allocation')
    
    return redirect('VRO_App3:manage_group_allocations')

@login_required
@group_required('site-admins')
def manage_groups(request):
    groups = UserGroup.objects.all().order_by('name')
    return render(request, 'VRO_App3/site-admins/manage_groups.html', {
        'groups': groups
    })

@login_required
@group_required('site-admins')
def create_group(request):
    if request.method == 'POST':
        form = UserGroupForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Group created successfully')
            return redirect('VRO_App3:manage_groups')
    else:
        form = UserGroupForm()
    
    return render(request, 'VRO_App3/site-admins/group_form.html', {
        'form': form,
        'action': 'Create'
    })

@login_required
@group_required('site-admins')
def edit_group(request, group_id):
    group = get_object_or_404(UserGroup, id=group_id)
    
    if request.method == 'POST':
        form = UserGroupForm(request.POST, instance=group)
        if form.is_valid():
            form.save()
            messages.success(request, 'Group updated successfully')
            return redirect('VRO_App3:manage_groups')
    else:
        form = UserGroupForm(instance=group)
    
    return render(request, 'VRO_App3/site-admins/group_form.html', {
        'form': form,
        'group': group,
        'action': 'Edit'
    })

@login_required
@group_required('site-admins')
def delete_group(request, group_id):
    group = get_object_or_404(UserGroup, id=group_id)
    
    if request.method == 'POST':
        try:
            group.delete()
            messages.success(request, 'Group deleted successfully')
        except ProtectedError:
            messages.error(request, 'This group cannot be deleted because it is being used by one or more users')
        return redirect('VRO_App3:manage_groups')
    
    return render(request, 'VRO_App3/site-admins/confirm_delete_group.html', {
        'group': group
    })

@login_required
def manage_genders(request):
    genders = Gender.objects.all()
    return render(request, 'VRO_App3/site-admins/lookups/gender_list.html', {
        'genders': genders
    })

@login_required
def create_gender(request):
    if request.method == 'POST':
        form = GenderForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Gender created successfully.')
            return redirect('VRO_App3:manage_genders')
    else:
        form = GenderForm()
    
    return render(request, 'VRO_App3/site-admins/lookups/lookup_form.html', {
        'form': form,
        'action': 'Create',
        'item_type': 'Gender'
    })

@login_required
def edit_gender(request, gender_id):
    gender = get_object_or_404(Gender, id=gender_id)
    if request.method == 'POST':
        form = GenderForm(request.POST, instance=gender)
        if form.is_valid():
            form.save()
            messages.success(request, 'Gender updated successfully.')
            return redirect('VRO_App3:manage_genders')
    else:
        form = GenderForm(instance=gender)
    
    return render(request, 'VRO_App3/site-admins/lookups/lookup_form.html', {
        'form': form,
        'action': 'Edit',
        'item_type': 'Gender'
    })

@login_required
def delete_gender(request, gender_id):
    gender = get_object_or_404(Gender, id=gender_id)
    try:
        gender.delete()
        messages.success(request, 'Gender deleted successfully.')
    except ProtectedError:
        messages.error(request, 'This gender cannot be deleted as it is being used by one or more profiles.')
    return redirect('VRO_App3:manage_genders')

# Similar views for Race
@login_required
def manage_races(request):
    races = Race.objects.all()
    return render(request, 'VRO_App3/site-admins/lookups/race_list.html', {
        'races': races
    })

@login_required
def create_race(request):
    if request.method == 'POST':
        form = RaceForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Race created successfully.')
            return redirect('VRO_App3:manage_races')
    else:
        form = RaceForm()
    
    return render(request, 'VRO_App3/site-admins/lookups/lookup_form.html', {
        'form': form,
        'action': 'Create',
        'item_type': 'Race'
    })

@login_required
def edit_race(request, race_id):
    race = get_object_or_404(Race, id=race_id)
    if request.method == 'POST':
        form = RaceForm(request.POST, instance=race)
        if form.is_valid():
            form.save()
            messages.success(request, 'Race updated successfully.')
            return redirect('VRO_App3:manage_races')
    else:
        form = RaceForm(instance=race)
    
    return render(request, 'VRO_App3/site-admins/lookups/lookup_form.html', {
        'form': form,
        'action': 'Edit',
        'item_type': 'Race'
    })

@login_required
def delete_race(request, race_id):
    race = get_object_or_404(Race, id=race_id)
    try:
        race.delete()
        messages.success(request, 'Race deleted successfully.')
    except ProtectedError:
        messages.error(request, 'This race cannot be deleted as it is being used by one or more profiles.')
    return redirect('VRO_App3:manage_races')

@login_required
def manage_conditions(request):
    conditions = Condition.objects.all()
    return render(request, 'VRO_App3/site-admins/lookups/condition_list.html', {
        'conditions': conditions
    })

@login_required
def create_condition(request):
    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description')
        is_active = request.POST.get('is_active') == 'on'
        
        try:
            Condition.objects.create(
                name=name,
                description=description,
                is_active=is_active
            )
            messages.success(request, 'Reading condition created successfully.')
            return redirect('VRO_App3:manage_conditions')
        except Exception as e:
            messages.error(request, f'Error creating reading condition: {str(e)}')
    
    return render(request, 'VRO_App3/site-admins/lookups/condition_form.html')

@login_required
def edit_condition(request, condition_id):
    condition = get_object_or_404(Condition, id=condition_id)
    
    if request.method == 'POST':
        try:
            condition.name = request.POST.get('name')
            condition.description = request.POST.get('description')
            condition.is_active = request.POST.get('is_active') == 'on'
            condition.save()
            
            messages.success(request, 'Reading condition updated successfully.')
            return redirect('VRO_App3:manage_conditions')
        except Exception as e:
            messages.error(request, f'Error updating reading condition: {str(e)}')
    
    return render(request, 'VRO_App3/site-admins/lookups/condition_form.html', {
        'condition': condition
    })

@login_required
def delete_condition(request, condition_id):
    condition = get_object_or_404(Condition, id=condition_id)
    try:
        condition.delete()
        messages.success(request, 'Reading condition deleted successfully.')
    except ProtectedError:
        messages.error(request, 'This reading condition cannot be deleted as it is being used by one or more profiles.')
    return redirect('VRO_App3:manage_conditions')

@login_required
@group_required('site-admins')
def manage_pages(request):
    # Get the Pages category
    pages_category = get_object_or_404(DocumentCategory, name='Pages')
    
    # Get all page documents
    page_documents = Document.objects.filter(
        category=pages_category
    ).order_by('title')
    
    return render(request, 'VRO_App3/site-admins/pages/page_list.html', {
        'pages': page_documents
    })

@login_required
@group_required('site-admins')
def download_page_document(request, page_name):
    # Get the document
    document = get_object_or_404(Document, 
        url_name=page_name,
        category__name='Pages'
    )
    
    if not document.file:
        raise Http404("Document file not found")
    
    try:
        response = FileResponse(document.file.open('rb'))
        response['Content-Disposition'] = f'attachment; filename="{document.file.name.split("/")[-1]}"'
        return response
    except Exception as e:
        messages.error(request, f"Error downloading file: {str(e)}")
        return redirect('VRO_App3:manage_pages')

@login_required
@group_required('site-admins')
def upload_page_document(request, page_name):
    document = get_object_or_404(Document, 
        url_name=page_name,
        category__name='Pages'
    )
    
    if request.method == 'POST' and request.FILES.get('document'):
        try:
            # Save the new file
            document.file.save(
                document.file.name.split('/')[-1],
                request.FILES['document'],
                save=True
            )
            messages.success(request, f"Successfully updated {document.title}")
        except Exception as e:
            messages.error(request, f"Error uploading file: {str(e)}")
    
    return redirect('VRO_App3:manage_pages')

@login_required
@group_required('site-admins')
def edit_pricing_card(request, card_id):
    card = get_object_or_404(PricingCard, Card_ID=card_id)
    
    if request.method == 'POST':
        try:
            # Handle special case for TBA prices
            price_str = request.POST.get('price', '').strip()
            if price_str.upper() == 'TBA':
                price = Decimal('0.00')
            else:
                # Remove £ symbol if present and convert to Decimal
                price_str = price_str.replace('£', '').strip()
                price = Decimal(price_str)

            card.Card_Title = request.POST.get('title', '').strip()
            card.Card_Sub_Title = request.POST.get('subtitle', '').strip()
            card.Price = price
            card.Point1 = request.POST.get('point1', '').strip()
            card.Point2 = request.POST.get('point2', '').strip()
            card.Point3 = request.POST.get('point3', '').strip()
            card.Card_Button_Text = request.POST.get('button_text', '').strip()
            card.Card_Button_Link = request.POST.get('button_link', '').strip()
            card.save()
            
            messages.success(request, f'Successfully updated "{card.Card_Title}"')
            return redirect('VRO_App3:manage_pricing')
            
        except InvalidOperation:
            messages.error(request, 'Invalid price format. Please enter a valid number or "TBA".')
        except Exception as e:
            messages.error(request, f'Error updating pricing card: {str(e)}')
    
    return render(request, 'VRO_App3/site-admins/pricing/edit_pricing_card.html', {
        'card': card
    })

@login_required
def upload_document(request, doc_type):
    if request.method == 'POST':
        title = request.POST.get('title')
        author = request.POST.get('author')
        posted_date = request.POST.get('posted_date')
        file = request.FILES.get('file')
        
        if not all([title, file]):
            messages.error(request, 'Please fill in all required fields')
            return redirect('VRO_App3:manage_articles' if doc_type == 'article' else 'VRO_App3:manage_blogs')
        
        category = DocumentCategory.objects.get(name='Articles' if doc_type == 'article' else 'Blogs')
        
        # Create document without saving first
        document = Document(
            title=title,
            author=author,
            posted_date=posted_date,
            category=category,
            file=file,
            is_published=True,
            created_by=request.user
        )
        
        # Save to generate the url_name
        document.save()
        
        messages.success(request, f'Successfully uploaded {doc_type}: {title}')
        messages.info(request, f'Permanent URL: {request.build_absolute_uri(document.get_absolute_url())}')
        
        return redirect('VRO_App3:manage_articles' if doc_type == 'article' else 'VRO_App3:manage_blogs')
    
    return render(request, 'VRO_App3/upload_document.html', {'doc_type': doc_type})

@login_required
def delete_document(request, doc_id):
    document = get_object_or_404(Document, id=doc_id)
    doc_type = 'article' if document.category.name == 'Articles' else 'blog'
    
    if request.method == 'POST':
        document.delete()
        messages.success(request, f'{doc_type.title()} deleted successfully')
        return redirect('VRO_App3:manage_articles' if doc_type == 'article' else 'VRO_App3:manage_blogs')
    
    return render(request, 'VRO_App3/confirm_delete.html', {'document': document})

@login_required
@group_required('site-admins')
def edit_document(request, doc_id):
    document = get_object_or_404(Document, id=doc_id)
    
    if request.method == 'POST':
        form = DocumentForm(request.POST, request.FILES, instance=document)
        if form.is_valid():
            doc = form.save(commit=False)
            doc.updated_by = request.user
            doc.save()
            messages.success(request, f"{doc.title} has been updated successfully.")
            return redirect('VRO_App3:manage_articles' if doc.category.slug == 'articles' else 'VRO_App3:manage_blogs')
    else:
        form = DocumentForm(instance=document)
    
    return render(request, 'VRO_App3/edit_document.html', {
        'form': form,
        'document': document,
        'template_type': 'site_admin'
    })

@login_required
@group_required('site-admins')
def download_document(request, doc_id):
    document = get_object_or_404(Document, id=doc_id)
    
    if not document.file:
        raise Http404("Document file not found")
    
    try:
        response = FileResponse(document.file.open('rb'))
        response['Content-Disposition'] = f'attachment; filename="{document.file.name.split("/")[-1]}"'
        return response
    except Exception as e:
        messages.error(request, f"Error downloading file: {str(e)}")
        return redirect('VRO_App3:manage_articles' if document.category.name == 'Articles' else 'VRO_App3:manage_blogs')

@login_required
@group_required('site-admins')
def upload_document_version(request, doc_id):
    document = get_object_or_404(Document, id=doc_id)
    
    if request.method == 'POST' and request.FILES.get('document'):
        try:
            # Save the new file - this will automatically overwrite if same filename
            document.file.save(
                document.file.name.split('/')[-1],  # Keep original filename
                request.FILES['document'],
                save=True
            )
            messages.success(request, f"Successfully updated {document.title}")
        except Exception as e:
            messages.error(request, f"Error uploading file: {str(e)}")
    
    return redirect('VRO_App3:manage_articles' if document.category.name == 'Articles' else 'VRO_App3:manage_blogs')

@login_required
@group_required('site-admins')
def manage_users(request):
    User = get_user_model()
    users = User.objects.select_related('userprofile').all().order_by('email')
    return render(request, 'VRO_App3/site-admins/users/manage_users.html', {
        'users': users,
        'template_type': 'site_admin'
    })

@login_required
@group_required('site-admins')
def edit_user(request, user_id):
    User = get_user_model()
    user = get_object_or_404(User.objects.select_related('userprofile'), id=user_id)
    
    if request.method == 'POST':
        try:
            # Update basic user information
            user.email = request.POST.get('email')
            user.is_active = request.POST.get('is_active') == '1'
            user.userprofile.first_name = request.POST.get('first_name')
            user.userprofile.last_name = request.POST.get('last_name')
            
            # Handle group assignment
            new_group_id = request.POST.get('group')
            if new_group_id:
                new_group = UserGroup.objects.get(id=new_group_id)
                
                # Create a group allocation record if changing groups
                if user.userprofile.group != new_group:
                    # If there's an existing unused allocation, use it
                    existing_allocation = PendingGroupAllocation.objects.filter(
                        email=user.email,
                        group=new_group,
                        is_used=False
                    ).first()
                    
                    if not existing_allocation:
                        # Create new allocation
                        allocation = PendingGroupAllocation.objects.create(
                            email=user.email,
                            group=new_group,
                            created_by=request.user,
                            is_used=True,
                            used_at=timezone.now()
                        )
                    else:
                        # Mark existing allocation as used
                        existing_allocation.is_used = True
                        existing_allocation.used_at = timezone.now()
                        existing_allocation.save()
                
                user.userprofile.group = new_group
            else:
                user.userprofile.group = None
            
            user.save()
            user.userprofile.save()
            
            messages.success(request, 'User updated successfully')
            return redirect('VRO_App3:manage_users')
            
        except Exception as e:
            logger.error(f"Error updating user: {str(e)}")
            messages.error(request, 'Error updating user')
    
    # Get available groups for the form
    available_groups = UserGroup.objects.filter(is_active=True).order_by('name')
    
    return render(request, 'VRO_App3/site-admins/users/edit_user.html', {
        'edit_user': user,
        'available_groups': available_groups,
        'template_type': 'site_admin'
    })
