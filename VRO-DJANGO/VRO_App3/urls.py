from django.urls import path
from . import views

app_name = 'VRO_App3'

urlpatterns = [
    # Dashboard
    path('site-admins/', views.site_admins_dashboard, name='site-admins'),
    
    # Messages URLs
    path('site-admins/messages/public/', views.public_messages, name='public_messages'),
    path('site-admins/messages/internal/', views.internal_messages, name='internal_messages'),
    path('site-admins/messages/internal/send/', views.send_internal_message, name='send_internal_message'),
    path('site-admins/messages/internal/view/<int:message_id>/', views.view_internal_message, name='view_internal_message'),
    path('site-admins/messages/contact/', views.contact_messages, name='contact_messages'),
    path('site-admins/messages/view/<int:message_id>/', views.view_message, name='view_message'),
    path('site-admins/messages/delete/<int:message_id>/', views.delete_message, name='delete_message'),
    
    # Group Management
    path('site-admins/group-allocations/', views.manage_group_allocations, name='manage_group_allocations'),
    path('site-admins/group-allocations/<int:allocation_id>/delete/', views.delete_group_allocation, name='delete_group_allocation'),
    path('site-admins/groups/', views.manage_groups, name='manage_groups'),
    path('site-admins/groups/create/', views.create_group, name='create_group'),
    path('site-admins/groups/<int:group_id>/edit/', views.edit_group, name='edit_group'),
    path('site-admins/groups/<int:group_id>/delete/', views.delete_group, name='delete_group'),
    
    # Site Content Management
    path('site-admins/pricing/', views.manage_pricing, name='manage_pricing'),
    path('site-admins/pricing/<int:card_id>/edit/', views.edit_pricing_card, name='edit_pricing_card'),
    path('site-admins/articles/', views.manage_articles, name='manage_articles'),
    path('site-admins/blogs/', views.manage_blogs, name='manage_blogs'),
    path('site-admins/pages/', views.manage_pages, name='manage_pages'),
    path('site-admins/pages/upload/<str:page_name>/', views.upload_page_document, name='upload_page_document'),
    path('site-admins/pages/download/<str:page_name>/', views.download_page_document, name='download_page_document'),
    path('site-admins/upload/<str:doc_type>/', views.upload_document, name='upload_document'),
    path('site-admins/document/<int:doc_id>/delete/', views.delete_document, name='delete_document'),
    path('site-admins/document/<int:doc_id>/edit/', views.edit_document, name='edit_document'),
    
    # Lookup Management URLs
    path('site-admins/lookups/genders/', views.manage_genders, name='manage_genders'),
    path('site-admins/lookups/genders/create/', views.create_gender, name='create_gender'),
    path('site-admins/lookups/genders/<int:gender_id>/edit/', views.edit_gender, name='edit_gender'),
    path('site-admins/lookups/genders/<int:gender_id>/delete/', views.delete_gender, name='delete_gender'),
    
    path('site-admins/lookups/races/', views.manage_races, name='manage_races'),
    path('site-admins/lookups/races/create/', views.create_race, name='create_race'),
    path('site-admins/lookups/races/<int:race_id>/edit/', views.edit_race, name='edit_race'),
    path('site-admins/lookups/races/<int:race_id>/delete/', views.delete_race, name='delete_race'),
    
    # New Reading Conditions Management URLs
    path('site-admins/lookups/conditions/', views.manage_conditions, name='manage_conditions'),
    path('site-admins/lookups/conditions/create/', views.create_condition, name='create_condition'),
    path('site-admins/lookups/conditions/<int:condition_id>/edit/', views.edit_condition, name='edit_condition'),
    path('site-admins/lookups/conditions/<int:condition_id>/delete/', 
         views.delete_condition, 
         name='delete_condition'),
    
    # Document Management URLs
    path('site-admins/document/<int:doc_id>/download/', views.download_document, name='download_document'),
    path('site-admins/document/<int:doc_id>/upload/', views.upload_document_version, name='upload_document_version'),
    
    # User Management
    path('site-admins/users/', views.manage_users, name='manage_users'),
    path('site-admins/users/<int:user_id>/edit/', views.edit_user, name='edit_user'),
]






















