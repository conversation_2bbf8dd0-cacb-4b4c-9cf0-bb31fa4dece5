from django import forms
from VRO_App1.models import Document
from VRO_App2.models import Gender, Race, Condition, PendingGroupAllocation, UserGroup

class GroupAllocationForm(forms.ModelForm):
    class Meta:
        model = PendingGroupAllocation
        fields = ['email', 'group']
        widgets = {
            'email': forms.EmailInput(attrs={'class': 'form-input rounded-md w-full'}),
            'group': forms.Select(attrs={'class': 'form-select rounded-md w-full'}),
        }

class UserGroupForm(forms.ModelForm):
    class Meta:
        model = UserGroup
        fields = ['name', 'code', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-input rounded-md w-full'}),
            'code': forms.TextInput(attrs={'class': 'form-input rounded-md w-full'}),
            'description': forms.Textarea(attrs={'class': 'form-textarea rounded-md w-full', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
        }

class DocumentForm(forms.ModelForm):
    class Meta:
        model = Document
        fields = [
            'title', 'description', 'author', 
            'posted_date', 'is_published', 'file'
        ]
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-input rounded-md w-full'}),
            'description': forms.Textarea(attrs={'class': 'form-textarea rounded-md w-full', 'rows': 4}),
            'author': forms.TextInput(attrs={'class': 'form-input rounded-md w-full'}),
            'posted_date': forms.DateInput(attrs={'class': 'form-input rounded-md', 'type': 'date'}),
            'is_published': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
        }

class LookupForm(forms.ModelForm):
    class Meta:
        fields = ['name', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-input rounded-md w-full'}),
            'description': forms.Textarea(attrs={'class': 'form-textarea rounded-md w-full', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
        }

class GenderForm(LookupForm):
    class Meta(LookupForm.Meta):
        model = Gender

class RaceForm(LookupForm):
    class Meta(LookupForm.Meta):
        model = Race

class ConditionForm(LookupForm):
    class Meta(LookupForm.Meta):
        model = Condition


