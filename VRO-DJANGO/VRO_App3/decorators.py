import logging
from functools import wraps
from django.shortcuts import redirect

logger = logging.getLogger(__name__)

def group_required(group_code):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            logger.info(f"Checking group access for user {request.user} requiring group {group_code}")
            
            # Force refresh user from database
            if request.user.is_authenticated:
                request.user = request.user.__class__.objects.get(id=request.user.id)
                logger.info(f"User authenticated, email: {request.user.email}")
                
                if hasattr(request.user, 'userprofile'):
                    logger.info(f"User profile exists, group: {request.user.userprofile.group.code if request.user.userprofile.group else 'None'}")
                else:
                    logger.warning(f"No userprofile for {request.user.email}")
            else:
                logger.warning("User not authenticated")
                return redirect('VRO_App1:please_login')
            
            if not hasattr(request.user, 'userprofile') or not request.user.userprofile.group:
                logger.warning(f"User {request.user.email} attempted to access {group_code} without a group")
                return redirect('VRO_App1:please_login')
                
            if request.user.userprofile.group.code != group_code:
                logger.warning(f"User {request.user.email} with group {request.user.userprofile.group.code} attempted to access {group_code}")
                return redirect('VRO_App1:please_login')
            
            logger.info(f"Access granted to {request.user.email} for group {group_code}")    
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator

def secure_view(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            logger.warning(f"Unauthenticated access attempt to secure view: {request.path}")
            request.session.flush()
            return redirect('VRO_App1:please_login')
            
        # Force refresh user from database to ensure current status
        request.user.refresh_from_db()
        
        if not request.user.is_authenticated:
            logger.warning(f"User session invalid after refresh: {request.path}")
            request.session.flush()
            return redirect('VRO_App1:please_login')
            
        return view_func(request, *args, **kwargs)
    return _wrapped_view


