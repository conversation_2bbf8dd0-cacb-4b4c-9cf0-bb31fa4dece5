[{"model": "auth.permission", "pk": 1, "fields": {"name": "Can add log entry", "content_type": 1, "codename": "add_logentry"}}, {"model": "auth.permission", "pk": 2, "fields": {"name": "Can change log entry", "content_type": 1, "codename": "change_logentry"}}, {"model": "auth.permission", "pk": 3, "fields": {"name": "Can delete log entry", "content_type": 1, "codename": "delete_logentry"}}, {"model": "auth.permission", "pk": 4, "fields": {"name": "Can view log entry", "content_type": 1, "codename": "view_logentry"}}, {"model": "auth.permission", "pk": 5, "fields": {"name": "Can add permission", "content_type": 2, "codename": "add_permission"}}, {"model": "auth.permission", "pk": 6, "fields": {"name": "Can change permission", "content_type": 2, "codename": "change_permission"}}, {"model": "auth.permission", "pk": 7, "fields": {"name": "Can delete permission", "content_type": 2, "codename": "delete_permission"}}, {"model": "auth.permission", "pk": 8, "fields": {"name": "Can view permission", "content_type": 2, "codename": "view_permission"}}, {"model": "auth.permission", "pk": 9, "fields": {"name": "Can add group", "content_type": 3, "codename": "add_group"}}, {"model": "auth.permission", "pk": 10, "fields": {"name": "Can change group", "content_type": 3, "codename": "change_group"}}, {"model": "auth.permission", "pk": 11, "fields": {"name": "Can delete group", "content_type": 3, "codename": "delete_group"}}, {"model": "auth.permission", "pk": 12, "fields": {"name": "Can view group", "content_type": 3, "codename": "view_group"}}, {"model": "auth.permission", "pk": 13, "fields": {"name": "Can add content type", "content_type": 4, "codename": "add_contenttype"}}, {"model": "auth.permission", "pk": 14, "fields": {"name": "Can change content type", "content_type": 4, "codename": "change_contenttype"}}, {"model": "auth.permission", "pk": 15, "fields": {"name": "Can delete content type", "content_type": 4, "codename": "delete_contenttype"}}, {"model": "auth.permission", "pk": 16, "fields": {"name": "Can view content type", "content_type": 4, "codename": "view_contenttype"}}, {"model": "auth.permission", "pk": 17, "fields": {"name": "Can add session", "content_type": 5, "codename": "add_session"}}, {"model": "auth.permission", "pk": 18, "fields": {"name": "Can change session", "content_type": 5, "codename": "change_session"}}, {"model": "auth.permission", "pk": 19, "fields": {"name": "Can delete session", "content_type": 5, "codename": "delete_session"}}, {"model": "auth.permission", "pk": 20, "fields": {"name": "Can view session", "content_type": 5, "codename": "view_session"}}, {"model": "auth.permission", "pk": 21, "fields": {"name": "Can add site", "content_type": 6, "codename": "add_site"}}, {"model": "auth.permission", "pk": 22, "fields": {"name": "Can change site", "content_type": 6, "codename": "change_site"}}, {"model": "auth.permission", "pk": 23, "fields": {"name": "Can delete site", "content_type": 6, "codename": "delete_site"}}, {"model": "auth.permission", "pk": 24, "fields": {"name": "Can view site", "content_type": 6, "codename": "view_site"}}, {"model": "auth.permission", "pk": 25, "fields": {"name": "Can add contact", "content_type": 7, "codename": "add_contact"}}, {"model": "auth.permission", "pk": 26, "fields": {"name": "Can change contact", "content_type": 7, "codename": "change_contact"}}, {"model": "auth.permission", "pk": 27, "fields": {"name": "Can delete contact", "content_type": 7, "codename": "delete_contact"}}, {"model": "auth.permission", "pk": 28, "fields": {"name": "Can view contact", "content_type": 7, "codename": "view_contact"}}, {"model": "auth.permission", "pk": 29, "fields": {"name": "Can add gender", "content_type": 8, "codename": "add_gender"}}, {"model": "auth.permission", "pk": 30, "fields": {"name": "Can change gender", "content_type": 8, "codename": "change_gender"}}, {"model": "auth.permission", "pk": 31, "fields": {"name": "Can delete gender", "content_type": 8, "codename": "delete_gender"}}, {"model": "auth.permission", "pk": 32, "fields": {"name": "Can view gender", "content_type": 8, "codename": "view_gender"}}, {"model": "auth.permission", "pk": 33, "fields": {"name": "Can add reading condition", "content_type": 9, "codename": "add_readingcondition"}}, {"model": "auth.permission", "pk": 34, "fields": {"name": "Can change reading condition", "content_type": 9, "codename": "change_readingcondition"}}, {"model": "auth.permission", "pk": 35, "fields": {"name": "Can delete reading condition", "content_type": 9, "codename": "delete_readingcondition"}}, {"model": "auth.permission", "pk": 36, "fields": {"name": "Can view reading condition", "content_type": 9, "codename": "view_readingcondition"}}, {"model": "auth.permission", "pk": 37, "fields": {"name": "Can add user", "content_type": 10, "codename": "add_user"}}, {"model": "auth.permission", "pk": 38, "fields": {"name": "Can change user", "content_type": 10, "codename": "change_user"}}, {"model": "auth.permission", "pk": 39, "fields": {"name": "Can delete user", "content_type": 10, "codename": "delete_user"}}, {"model": "auth.permission", "pk": 40, "fields": {"name": "Can view user", "content_type": 10, "codename": "view_user"}}, {"model": "auth.permission", "pk": 41, "fields": {"name": "Can add access log", "content_type": 11, "codename": "add_accesslog"}}, {"model": "auth.permission", "pk": 42, "fields": {"name": "Can change access log", "content_type": 11, "codename": "change_accesslog"}}, {"model": "auth.permission", "pk": 43, "fields": {"name": "Can delete access log", "content_type": 11, "codename": "delete_accesslog"}}, {"model": "auth.permission", "pk": 44, "fields": {"name": "Can view access log", "content_type": 11, "codename": "view_accesslog"}}, {"model": "auth.permission", "pk": 45, "fields": {"name": "Can add group change log", "content_type": 12, "codename": "add_groupchangelog"}}, {"model": "auth.permission", "pk": 46, "fields": {"name": "Can change group change log", "content_type": 12, "codename": "change_groupchangelog"}}, {"model": "auth.permission", "pk": 47, "fields": {"name": "Can delete group change log", "content_type": 12, "codename": "delete_groupchangelog"}}, {"model": "auth.permission", "pk": 48, "fields": {"name": "Can view group change log", "content_type": 12, "codename": "view_groupchangelog"}}, {"model": "auth.permission", "pk": 49, "fields": {"name": "Can add user group email lookup", "content_type": 13, "codename": "add_usergroupemaillookup"}}, {"model": "auth.permission", "pk": 50, "fields": {"name": "Can change user group email lookup", "content_type": 13, "codename": "change_usergroupemaillookup"}}, {"model": "auth.permission", "pk": 51, "fields": {"name": "Can delete user group email lookup", "content_type": 13, "codename": "delete_usergroupemaillookup"}}, {"model": "auth.permission", "pk": 52, "fields": {"name": "Can view user group email lookup", "content_type": 13, "codename": "view_usergroupemaillookup"}}, {"model": "auth.permission", "pk": 53, "fields": {"name": "Can add user reading condition", "content_type": 14, "codename": "add_userreadingcondition"}}, {"model": "auth.permission", "pk": 54, "fields": {"name": "Can change user reading condition", "content_type": 14, "codename": "change_userreadingcondition"}}, {"model": "auth.permission", "pk": 55, "fields": {"name": "Can delete user reading condition", "content_type": 14, "codename": "delete_userreadingcondition"}}, {"model": "auth.permission", "pk": 56, "fields": {"name": "Can view user reading condition", "content_type": 14, "codename": "view_userreadingcondition"}}, {"model": "auth.permission", "pk": 57, "fields": {"name": "Can add ethnicity", "content_type": 15, "codename": "add_ethnicity"}}, {"model": "auth.permission", "pk": 58, "fields": {"name": "Can change ethnicity", "content_type": 15, "codename": "change_ethnicity"}}, {"model": "auth.permission", "pk": 59, "fields": {"name": "Can delete ethnicity", "content_type": 15, "codename": "delete_ethnicity"}}, {"model": "auth.permission", "pk": 60, "fields": {"name": "Can view ethnicity", "content_type": 15, "codename": "view_ethnicity"}}, {"model": "auth.permission", "pk": 61, "fields": {"name": "Can add reading condition category", "content_type": 16, "codename": "add_readingconditioncategory"}}, {"model": "auth.permission", "pk": 62, "fields": {"name": "Can change reading condition category", "content_type": 16, "codename": "change_readingconditioncategory"}}, {"model": "auth.permission", "pk": 63, "fields": {"name": "Can delete reading condition category", "content_type": 16, "codename": "delete_readingconditioncategory"}}, {"model": "auth.permission", "pk": 64, "fields": {"name": "Can view reading condition category", "content_type": 16, "codename": "view_readingconditioncategory"}}, {"model": "auth.permission", "pk": 65, "fields": {"name": "Can add admin action", "content_type": 17, "codename": "add_adminaction"}}, {"model": "auth.permission", "pk": 66, "fields": {"name": "Can change admin action", "content_type": 17, "codename": "change_adminaction"}}, {"model": "auth.permission", "pk": 67, "fields": {"name": "Can delete admin action", "content_type": 17, "codename": "delete_adminaction"}}, {"model": "auth.permission", "pk": 68, "fields": {"name": "Can view admin action", "content_type": 17, "codename": "view_adminaction"}}, {"model": "auth.permission", "pk": 69, "fields": {"name": "Can add lookup table edit", "content_type": 18, "codename": "add_lookuptableedit"}}, {"model": "auth.permission", "pk": 70, "fields": {"name": "Can change lookup table edit", "content_type": 18, "codename": "change_lookuptableedit"}}, {"model": "auth.permission", "pk": 71, "fields": {"name": "Can delete lookup table edit", "content_type": 18, "codename": "delete_lookuptableedit"}}, {"model": "auth.permission", "pk": 72, "fields": {"name": "Can view lookup table edit", "content_type": 18, "codename": "view_lookuptableedit"}}, {"model": "auth.permission", "pk": 73, "fields": {"name": "Can add Case Study", "content_type": 19, "codename": "add_casestudy"}}, {"model": "auth.permission", "pk": 74, "fields": {"name": "Can change Case Study", "content_type": 19, "codename": "change_casestudy"}}, {"model": "auth.permission", "pk": 75, "fields": {"name": "Can delete Case Study", "content_type": 19, "codename": "delete_casestudy"}}, {"model": "auth.permission", "pk": 76, "fields": {"name": "Can view Case Study", "content_type": 19, "codename": "view_casestudy"}}, {"model": "auth.permission", "pk": 77, "fields": {"name": "Can add Program Statistic", "content_type": 20, "codename": "add_programstatistic"}}, {"model": "auth.permission", "pk": 78, "fields": {"name": "Can change Program Statistic", "content_type": 20, "codename": "change_programstatistic"}}, {"model": "auth.permission", "pk": 79, "fields": {"name": "Can delete Program Statistic", "content_type": 20, "codename": "delete_programstatistic"}}, {"model": "auth.permission", "pk": 80, "fields": {"name": "Can view Program Statistic", "content_type": 20, "codename": "view_programstatistic"}}, {"model": "auth.permission", "pk": 81, "fields": {"name": "Can add Student Testimonial", "content_type": 21, "codename": "add_studenttestimonial"}}, {"model": "auth.permission", "pk": 82, "fields": {"name": "Can change Student Testimonial", "content_type": 21, "codename": "change_studenttestimonial"}}, {"model": "auth.permission", "pk": 83, "fields": {"name": "Can delete Student Testimonial", "content_type": 21, "codename": "delete_studenttestimonial"}}, {"model": "auth.permission", "pk": 84, "fields": {"name": "Can view Student Testimonial", "content_type": 21, "codename": "view_studenttestimonial"}}, {"model": "auth.permission", "pk": 85, "fields": {"name": "Can add Success Story", "content_type": 22, "codename": "add_successstory"}}, {"model": "auth.permission", "pk": 86, "fields": {"name": "Can change Success Story", "content_type": 22, "codename": "change_successstory"}}, {"model": "auth.permission", "pk": 87, "fields": {"name": "Can delete Success Story", "content_type": 22, "codename": "delete_successstory"}}, {"model": "auth.permission", "pk": 88, "fields": {"name": "Can view Success Story", "content_type": 22, "codename": "view_successstory"}}, {"model": "auth.permission", "pk": 89, "fields": {"name": "Can add user profile", "content_type": 23, "codename": "add_userprofile"}}, {"model": "auth.permission", "pk": 90, "fields": {"name": "Can change user profile", "content_type": 23, "codename": "change_userprofile"}}, {"model": "auth.permission", "pk": 91, "fields": {"name": "Can delete user profile", "content_type": 23, "codename": "delete_userprofile"}}, {"model": "auth.permission", "pk": 92, "fields": {"name": "Can view user profile", "content_type": 23, "codename": "view_userprofile"}}, {"model": "auth.permission", "pk": 93, "fields": {"name": "Can add internal message", "content_type": 24, "codename": "add_internalmessage"}}, {"model": "auth.permission", "pk": 94, "fields": {"name": "Can change internal message", "content_type": 24, "codename": "change_internalmessage"}}, {"model": "auth.permission", "pk": 95, "fields": {"name": "Can delete internal message", "content_type": 24, "codename": "delete_internalmessage"}}, {"model": "auth.permission", "pk": 96, "fields": {"name": "Can view internal message", "content_type": 24, "codename": "view_internalmessage"}}, {"model": "auth.permission", "pk": 97, "fields": {"name": "Can add Blog Post", "content_type": 25, "codename": "add_blogpost"}}, {"model": "auth.permission", "pk": 98, "fields": {"name": "Can change Blog Post", "content_type": 25, "codename": "change_blogpost"}}, {"model": "auth.permission", "pk": 99, "fields": {"name": "Can delete Blog Post", "content_type": 25, "codename": "delete_blogpost"}}, {"model": "auth.permission", "pk": 100, "fields": {"name": "Can view Blog Post", "content_type": 25, "codename": "view_blogpost"}}, {"model": "auth.permission", "pk": 101, "fields": {"name": "Can add email address", "content_type": 26, "codename": "add_emailaddress"}}, {"model": "auth.permission", "pk": 102, "fields": {"name": "Can change email address", "content_type": 26, "codename": "change_emailaddress"}}, {"model": "auth.permission", "pk": 103, "fields": {"name": "Can delete email address", "content_type": 26, "codename": "delete_emailaddress"}}, {"model": "auth.permission", "pk": 104, "fields": {"name": "Can view email address", "content_type": 26, "codename": "view_emailaddress"}}, {"model": "auth.permission", "pk": 105, "fields": {"name": "Can add email confirmation", "content_type": 27, "codename": "add_emailconfirmation"}}, {"model": "auth.permission", "pk": 106, "fields": {"name": "Can change email confirmation", "content_type": 27, "codename": "change_emailconfirmation"}}, {"model": "auth.permission", "pk": 107, "fields": {"name": "Can delete email confirmation", "content_type": 27, "codename": "delete_emailconfirmation"}}, {"model": "auth.permission", "pk": 108, "fields": {"name": "Can view email confirmation", "content_type": 27, "codename": "view_emailconfirmation"}}, {"model": "auth.permission", "pk": 109, "fields": {"name": "Can add social account", "content_type": 28, "codename": "add_socialaccount"}}, {"model": "auth.permission", "pk": 110, "fields": {"name": "Can change social account", "content_type": 28, "codename": "change_socialaccount"}}, {"model": "auth.permission", "pk": 111, "fields": {"name": "Can delete social account", "content_type": 28, "codename": "delete_socialaccount"}}, {"model": "auth.permission", "pk": 112, "fields": {"name": "Can view social account", "content_type": 28, "codename": "view_socialaccount"}}, {"model": "auth.permission", "pk": 113, "fields": {"name": "Can add social application", "content_type": 29, "codename": "add_socialapp"}}, {"model": "auth.permission", "pk": 114, "fields": {"name": "Can change social application", "content_type": 29, "codename": "change_socialapp"}}, {"model": "auth.permission", "pk": 115, "fields": {"name": "Can delete social application", "content_type": 29, "codename": "delete_socialapp"}}, {"model": "auth.permission", "pk": 116, "fields": {"name": "Can view social application", "content_type": 29, "codename": "view_socialapp"}}, {"model": "auth.permission", "pk": 117, "fields": {"name": "Can add social application token", "content_type": 30, "codename": "add_socialtoken"}}, {"model": "auth.permission", "pk": 118, "fields": {"name": "Can change social application token", "content_type": 30, "codename": "change_socialtoken"}}, {"model": "auth.permission", "pk": 119, "fields": {"name": "Can delete social application token", "content_type": 30, "codename": "delete_socialtoken"}}, {"model": "auth.permission", "pk": 120, "fields": {"name": "Can view social application token", "content_type": 30, "codename": "view_socialtoken"}}