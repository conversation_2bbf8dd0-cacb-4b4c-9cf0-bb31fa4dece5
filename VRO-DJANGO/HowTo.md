# VRO_Project Setup Guide

## Initial Setup Using Script

The easiest way to set up your new project is to use the provided setup script:

```bash
# Run the setup script
python setup_new_project.py
```

The script will:
1. Ask for your new project and app names
2. Replace all instances of VRO_Project and VRO_App1
3. Clean up temporary files and directories
4. Set up a new virtual environment
5. Install dependencies
6. Initialize the database
7. Update Docker container names

If you prefer to set up manually, follow the steps below.

## Manual Setup

Find and replace all instances of VRO_Project to your new project name
Find and replace all instances of VRO_App1 to your new app name

## Development Setup

### Local Development (without Docker)

#### Create venv
```bash
python3 -m venv venv
```

#### Activate venv
```bash
source venv/bin/activate
```

#### Update pip and install requirements
```bash
pip install --upgrade pip && pip install -r requirements.txt
```

#### Configure Database
1. Ensure PostgreSQL is installed and running locally
2. Create a development database and user:
```sql
CREATE DATABASE devdb;
CREATE USER devuser WITH PASSWORD 'devpass';
GRANT ALL PRIVILEGES ON DATABASE devdb TO devuser;
```

#### Start Tailwindcss Watcher
```bash
source venv/bin/activate && python manage.py tailwind start
```

#### Start Dev Server
```bash
source venv/bin/activate && python manage.py runserver
```

### Docker Development Setup

1. Ensure your local PostgreSQL server is running and accessible
2. Start development containers:
```bash
# Build and start containers
docker-compose -f docker-compose.dev.yml up --build

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop containers
docker-compose -f docker-compose.dev.yml down
```

## Production Setup

### 1. Pre-deployment Configuration

1. Update settings.py with your production database credentials:
```python
# In settings.py, update the production database configuration:
if not DEBUG:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": "proddb",
            "USER": "produser",
            "PASSWORD": "your-secure-password",
            "HOST": "your-production-db-host",
            "PORT": "5432",
        }
    }
```

2. Configure SSL/TLS certificates:
   - Place your SSL certificates in the appropriate location
   - Update Nginx configuration accordingly

### 2. Docker Production Deployment

```bash
# Build and start containers in detached mode
docker-compose -f docker-compose.prod.yml up --build -d

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Stop containers
docker-compose -f docker-compose.prod.yml down
```

### 3. Database Management

```bash
# Create database backup
pg_dump -h localhost -U devuser devdb > backup.sql

# Restore database
psql -h localhost -U devuser devdb < backup.sql

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser
```

### 4. Maintenance Commands

```bash
# Run migrations
docker-compose -f docker-compose.prod.yml exec web python manage.py migrate

# Create superuser
docker-compose -f docker-compose.prod.yml exec web python manage.py createsuperuser

# Collect static files
docker-compose -f docker-compose.prod.yml exec web python manage.py collectstatic --no-input
```

### 5. Production Checklist

- [ ] Create secure `.env.prod` file with production settings
- [ ] Update `SECRET_KEY` with a secure value
- [ ] Configure proper `ALLOWED_HOSTS`
- [ ] Set up SSL/TLS certificates
- [ ] Configure secure database credentials
- [ ] Set up email settings
- [ ] Configure backup strategy
- [ ] Set up monitoring and alerting
- [ ] Configure logging
- [ ] Set up firewall rules
- [ ] Configure automated backups
- [ ] Set up health checks
- [ ] Configure rate limiting
- [ ] Set up error tracking (e.g., Sentry)
- [ ] Configure CDN (if needed)
- [ ] Set up automated deployment pipeline

### 6. Scaling and Maintenance

- Monitor resource usage and scale containers as needed
- Regularly update dependencies and security patches
- Implement automated backup strategy
- Set up monitoring and alerting
- Configure log rotation
- Implement CI/CD pipeline

### 7. Security Best Practices

- Keep all environment files secure and never commit them to version control
- Regularly rotate database credentials and secret keys
- Use strong passwords for all services
- Keep all packages and dependencies up to date
- Regularly review security logs
- Implement rate limiting and DDoS protection
- Use secure headers in Nginx configuration
- Enable HTTPS only (redirect HTTP to HTTPS)
