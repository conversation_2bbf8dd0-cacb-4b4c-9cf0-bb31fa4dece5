from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.core.validators import RegexValidator, MinValueValidator, MaxValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.conf import settings

class CustomUserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        return self.create_user(email, password, **extra_fields)

    def get_or_create_for_social(self, email, **extra_fields):
        """
        Get or create a user for social authentication
        """
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model.objects.filter(email=email).first()
        if user:
            return user, False
        
        extra_fields.setdefault('is_active', True)  # Social users are auto-activated
        user = self.model(email=email, **extra_fields)
        user.set_unusable_password()
        user.save(using=self._db)
        return user, True

class User(AbstractUser):
    username = None
    email = models.EmailField(_('email address'), unique=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []
    
    objects = CustomUserManager()
    
    # Add related_name to avoid clashes
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name=_('groups'),
        blank=True,
        help_text=_(
            'The groups this user belongs to. A user will get all permissions '
            'granted to each of their groups.'
        ),
        related_name='vro_app2_users',
        related_query_name='vro_app2_user',
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name=_('user permissions'),
        blank=True,
        help_text=_('Specific permissions for this user.'),
        related_name='vro_app2_users',
        related_query_name='vro_app2_user',
    )

    @property
    def is_currently_logged_in(self):
        return self.login_activities.filter(
            is_active_session=True,
            logout_datetime__isnull=True
        ).exists()

    @property
    def active_sessions_count(self):
        return self.login_activities.filter(
            is_active_session=True,
            logout_datetime__isnull=True
        ).count()

    def end_all_sessions(self):
        now = timezone.now()
        self.login_activities.filter(
            is_active_session=True,
            logout_datetime__isnull=True
        ).update(
            logout_datetime=now,
            is_active_session=False
        )

    def __str__(self):
        return self.email

class BaseNameModel(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True
        ordering = ['name']

    def __str__(self):
        return self.name

class Gender(BaseNameModel):
    pass

class Race(BaseNameModel):
    pass

class Condition(BaseNameModel):
    pass

class AgeGroup(BaseNameModel):
    min_age = models.PositiveSmallIntegerField()
    max_age = models.PositiveSmallIntegerField()

    def __str__(self):
        return f"{self.name} ({self.min_age}-{self.max_age})"

class PostcodeArea(BaseNameModel):
    code = models.CharField(max_length=4, unique=True)  # For storing area codes like "SW1"
    
    def __str__(self):
        return f"{self.code} - {self.name}"

class UserGroup(BaseNameModel):
    code = models.CharField(max_length=20, unique=True)
    
    def __str__(self):
        return f"{self.name} ({self.code})"

class UserProfile(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    age = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(99)]
    )
    age_group = models.ForeignKey(AgeGroup, on_delete=models.PROTECT, null=True)
    gender = models.ForeignKey(Gender, on_delete=models.PROTECT)
    race = models.ForeignKey(Race, on_delete=models.PROTECT)
    mobile = models.CharField(
        max_length=15, 
        blank=True,
        validators=[RegexValidator(regex=r'^\+?1?\d{9,15}$')]
    )
    landline = models.CharField(max_length=15, blank=True)
    address_line1 = models.CharField(max_length=100)
    address_line2 = models.CharField(max_length=100, blank=True)
    postcode = models.CharField(max_length=10)
    postcode_area = models.ForeignKey(PostcodeArea, on_delete=models.PROTECT, null=True)
    conditions = models.ManyToManyField(Condition, blank=True)
    has_no_conditions = models.BooleanField(default=True)
    is_profile_completed = models.BooleanField(default=False)
    group = models.ForeignKey(
        UserGroup,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    profile_completed_date = models.DateTimeField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)
    notes = models.TextField(blank=True)

    def clean(self):
        if self.group == '':
            self.group = None

    def save(self, *args, **kwargs):
        if self.postcode:
            self.postcode = self.postcode.upper()
            # Try to set postcode_area based on first part of postcode
            postcode_prefix = ''.join(filter(str.isalpha, self.postcode[:2]))
            try:
                self.postcode_area = PostcodeArea.objects.get(code__iexact=postcode_prefix)
            except PostcodeArea.DoesNotExist:
                pass

        if self.age:
            # Try to set age_group based on age
            try:
                self.age_group = AgeGroup.objects.get(
                    min_age__lte=self.age,
                    max_age__gte=self.age
                )
            except AgeGroup.DoesNotExist:
                pass

        if self.is_profile_completed and not self.profile_completed_date:
            self.profile_completed_date = timezone.now()

        super().save(*args, **kwargs)

        if self.has_no_conditions:
            self.conditions.clear()

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

    def __str__(self):
        return self.get_full_name()

    class Meta:
        ordering = ['last_name', 'first_name']

class PendingGroupAllocation(models.Model):
    email = models.EmailField()
    group = models.ForeignKey('UserGroup', on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    is_used = models.BooleanField(default=False)
    used_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.email} -> {self.group.name}"
