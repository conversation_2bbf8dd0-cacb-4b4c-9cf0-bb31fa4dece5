from django.contrib.auth.tokens import default_token_generator
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.utils.timezone import now

def send_magic_link(request, user):
    token = default_token_generator.make_token(user)
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    magic_link = request.build_absolute_uri(
        reverse('VRO_App1:magic_link_login', kwargs={'uidb64': uid, 'token': token})
    )
    
    context = {
        'user_display': user.email,
        'magic_link': magic_link,
        'site_domain': request.get_host(),
        'ip_address': request.META.get('REMOTE_ADDR', ''),
        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        'timestamp': now().strftime('%Y-%m-%d %H:%M:%S %Z')
    }

    # Render both HTML and text versions
    html_content = render_to_string('account/email/magic_link_message.html', context)
    text_content = render_to_string('account/email/magic_link_message.txt', context)

    # Create the email
    msg = EmailMultiAlternatives(
        'Your login link for Visual Reading Online',
        text_content,
        '<EMAIL>',
        [user.email]
    )
    msg.attach_alternative(html_content, "text/html")
    msg.send()

def send_welcome_email(request, user):
    context = {
        'user_display': user.email,  # This is what's currently used
        'email': user.email,         # Add this for test compatibility
        'site_domain': request.get_host(),
    }

    # Render both HTML and text versions
    html_content = render_to_string('account/email/welcome_message.html', context)
    text_content = render_to_string('account/email/welcome_message.txt', context)

    # Create the email
    msg = EmailMultiAlternatives(
        'Welcome to Visual Reading Online!',
        text_content,
        '<EMAIL>',
        [user.email]
    )
    msg.attach_alternative(html_content, "text/html")
    msg.send()
