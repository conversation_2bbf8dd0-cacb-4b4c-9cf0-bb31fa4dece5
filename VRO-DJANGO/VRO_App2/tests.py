from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages
from .models import UserProfile, Gender, Race, Condition  # Add missing imports

User = get_user_model()

class BaseTestCase(TestCase):
    """Base test case with common setup"""
    def setUp(self):
        # Create test data for foreign key fields
        self.test_gender = Gender.objects.create(name='TestGender')
        self.test_race = Race.objects.create(name='TestRace')
        self.test_condition = Condition.objects.create(name='TestCondition')
        
        self.client = Client()
        
        # Create user with only email
        self.test_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestPass123!'
        )
        
        # Create UserProfile and save it to get an ID
        self.user_profile = UserProfile.objects.create(
            user=self.test_user,
            first_name='Test',
            last_name='User',
            age=30,
            gender=self.test_gender,
            race=self.test_race,
            address_line1='123 Test St',
            postcode='SW1A 1AA'
        )
        self.user_profile.save()  # Save to get ID before adding many-to-many
        
        # Now safe to add many-to-many relationships
        self.user_profile.conditions.add(self.test_condition)
        
        # Log in the user
        self.client.login(email='<EMAIL>', password='TestPass123!')
        
        # Valid form data
        self.valid_profile_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'age': 30,
            'gender': self.test_gender.id,
            'race': self.test_race.id,
            'mobile': '+1234567890',
            'landline': '02012345678',
            'address_line1': '123 Test Street',
            'address_line2': 'Apt 4B',
            'postcode': 'SW1A 1AA',
            'has_no_conditions': True
        }

class HomeAndProfileTests(BaseTestCase):
    def setUp(self):
        super().setUp()  # Call parent setUp to get all the common setup
        self.user_profile.is_profile_completed = True
        self.user_profile.save()

    def test_home_page_loads_for_completed_profile(self):
        response = self.client.get(reverse('VRO_App2:home'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'VRO_App2/home.html')

    def test_home_redirect_when_profile_incomplete(self):
        self.user_profile.is_profile_completed = False
        self.user_profile.save()
        
        response = self.client.get(reverse('VRO_App2:home'))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('VRO_App2:complete_profile'))

    def test_profile_completion_status(self):
        response = self.client.get(reverse('VRO_App2:home'))
        self.assertRedirects(response, reverse('VRO_App2:complete_profile'))
        
        self.user_profile.is_profile_completed = True
        self.user_profile.save()
        
        response = self.client.get(reverse('VRO_App2:home'))
        self.assertEqual(response.status_code, 200)

    def test_profile_page_loads(self):
        response = self.client.get(reverse('VRO_App2:profile'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'VRO_App2/profile.html')

    def test_unauthorized_access(self):
        self.client.logout()
        response = self.client.get(reverse('VRO_App2:home'))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(
            response, 
            f"{reverse('VRO_App1:login')}?next={reverse('VRO_App2:home')}"
        )

class ProfileFormTests(BaseTestCase):
    def test_profile_form_required_fields(self):
        required_fields = [
            'first_name', 'last_name', 'age', 'gender', 
            'race', 'address_line1', 'postcode'
        ]
        
        for field in required_fields:
            invalid_data = self.valid_profile_data.copy()
            invalid_data.pop(field)
            response = self.client.post(reverse('VRO_App2:profile'), invalid_data)
            self.assertEqual(response.status_code, 200)
            self.assertTrue(response.context['form'].errors)

    def test_profile_form_optional_fields(self):
        optional_fields = ['mobile', 'landline', 'address_line2']
        
        for field in optional_fields:
            valid_data = self.valid_profile_data.copy()
            valid_data.pop(field)
            response = self.client.post(reverse('VRO_App2:profile'), valid_data)
            self.assertEqual(response.status_code, 302)
            self.assertRedirects(response, reverse('VRO_App2:home'))

    def test_age_validation(self):
        invalid_ages = [-1, 0, 100, 'abc']
        for age in invalid_ages:
            invalid_data = self.valid_profile_data.copy()
            invalid_data['age'] = age
            response = self.client.post(reverse('VRO_App2:profile'), invalid_data)
            self.assertEqual(response.status_code, 200)
            self.assertTrue(response.context['form'].errors)

    def test_postcode_formatting(self):
        test_data = self.valid_profile_data.copy()
        test_data['postcode'] = 'sw1a 1aa'
        
        response = self.client.post(reverse('VRO_App2:profile'), test_data)
        self.assertEqual(response.status_code, 302)
        
        self.user_profile.refresh_from_db()
        self.assertEqual(self.user_profile.postcode, 'SW1A 1AA')

    def test_conditions_handling(self):
        condition2 = Condition.objects.create(name='Condition 2')
        test_data = self.valid_profile_data.copy()
        test_data['conditions'] = [self.test_condition.id, condition2.id]
        test_data['has_no_conditions'] = False
        
        response = self.client.post(reverse('VRO_App2:profile'), test_data)
        self.assertEqual(response.status_code, 302)
        
        self.user_profile.refresh_from_db()
        self.assertFalse(self.user_profile.has_no_conditions)
        self.assertEqual(self.user_profile.conditions.count(), 2)

    def test_phone_number_validation(self):
        invalid_numbers = ['123', 'abc', '++44123']
        for number in invalid_numbers:
            invalid_data = self.valid_profile_data.copy()
            invalid_data['mobile'] = number
            response = self.client.post(reverse('VRO_App2:profile'), invalid_data)
            self.assertEqual(response.status_code, 200)
            self.assertTrue(response.context['form'].errors)
