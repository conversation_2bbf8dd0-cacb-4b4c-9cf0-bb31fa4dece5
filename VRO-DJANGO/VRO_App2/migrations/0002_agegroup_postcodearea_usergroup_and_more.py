# Generated by Django 4.2.9 on 2025-03-22 23:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("VRO_App2", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="AgeGroup",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("min_age", models.PositiveSmallIntegerField()),
                ("max_age", models.PositiveSmallIntegerField()),
            ],
            options={
                "ordering": ["name"],
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PostcodeArea",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("code", models.CharField(max_length=4, unique=True)),
            ],
            options={
                "ordering": ["name"],
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="UserGroup",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("code", models.CharField(max_length=20, unique=True)),
            ],
            options={
                "ordering": ["name"],
                "abstract": False,
            },
        ),
        migrations.AlterModelOptions(
            name="condition",
            options={"ordering": ["name"]},
        ),
        migrations.AlterModelOptions(
            name="gender",
            options={"ordering": ["name"]},
        ),
        migrations.AlterModelOptions(
            name="race",
            options={"ordering": ["name"]},
        ),
        migrations.AlterModelOptions(
            name="userprofile",
            options={"ordering": ["last_name", "first_name"]},
        ),
        migrations.RemoveIndex(
            model_name="userprofile",
            name="VRO_App2_us_is_prof_60dbf9_idx",
        ),
        migrations.RemoveIndex(
            model_name="userprofile",
            name="VRO_App2_us_group_346c1b_idx",
        ),
        migrations.AddField(
            model_name="condition",
            name="is_active",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="gender",
            name="description",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="gender",
            name="is_active",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="race",
            name="description",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="race",
            name="is_active",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="notes",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="age_group",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.PROTECT, to="VRO_App2.agegroup"
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="postcode_area",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.PROTECT, to="VRO_App2.postcodearea"
            ),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="group",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="VRO_App2.usergroup",
            ),
        ),
    ]
