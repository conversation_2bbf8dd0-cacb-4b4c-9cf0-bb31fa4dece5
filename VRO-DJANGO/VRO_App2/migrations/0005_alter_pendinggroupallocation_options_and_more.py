# Generated by Django 4.2.9 on 2025-03-25 17:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('VRO_App2', '0004_pendinggroupallocation'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='pendinggroupallocation',
            options={'ordering': ['-created_at']},
        ),
        migrations.RemoveIndex(
            model_name='pendinggroupallocation',
            name='VRO_App2_pe_email_73468d_idx',
        ),
        migrations.AddField(
            model_name='pendinggroupallocation',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='pendinggroupallocation',
            name='email',
            field=models.EmailField(max_length=254),
        ),
    ]
