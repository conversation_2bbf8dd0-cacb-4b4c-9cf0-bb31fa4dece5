# Generated by Django 4.2.9 on 2025-03-25 16:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('VRO_App2', '0003_add_default_lookups'),
    ]

    operations = [
        migrations.CreateModel(
            name='PendingGroupAllocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, verbose_name='email address')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_used', models.BooleanField(default=False)),
                ('used_at', models.DateTimeField(blank=True, null=True)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='VRO_App2.usergroup')),
            ],
            options={
                'indexes': [models.Index(fields=['email', 'is_used'], name='VRO_App2_pe_email_73468d_idx')],
            },
        ),
    ]
