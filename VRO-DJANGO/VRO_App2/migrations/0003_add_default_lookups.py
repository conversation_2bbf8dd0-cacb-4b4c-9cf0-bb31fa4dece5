from django.db import migrations

def add_default_lookups(apps, schema_editor):
    Gender = apps.get_model('VRO_App2', 'Gender')
    Race = apps.get_model('VRO_App2', 'Race')
    Condition = apps.get_model('VRO_App2', 'Condition')

    # Default Genders
    genders = [
        {
            'name': 'Male',
            'description': 'Identifies as male'
        },
        {
            'name': 'Female',
            'description': 'Identifies as female'
        },
        {
            'name': 'Non-binary',
            'description': 'Identifies as non-binary'
        },
        {
            'name': 'Other',
            'description': 'Other gender identity'
        },
        {
            'name': 'Prefer not to say',
            'description': 'Prefers not to disclose gender'
        }
    ]

    for gender in genders:
        Gender.objects.get_or_create(
            name=gender['name'],
            defaults={'description': gender['description']}
        )

    # Default Races/Ethnicities (based on UK census categories)
    races = [
        {
            'name': 'White - British',
            'description': 'White British, English, Welsh, Scottish, or Northern Irish'
        },
        {
            'name': 'White - Irish',
            'description': 'White Irish background'
        },
        {
            'name': 'White - Other',
            'description': 'Other White background'
        },
        {
            'name': 'Mixed - White and Black Caribbean',
            'description': 'Mixed White and Black Caribbean background'
        },
        {
            'name': 'Mixed - White and Black African',
            'description': 'Mixed White and Black African background'
        },
        {
            'name': 'Mixed - White and Asian',
            'description': 'Mixed White and Asian background'
        },
        {
            'name': 'Mixed - Other',
            'description': 'Other Mixed background'
        },
        {
            'name': 'Asian - Indian',
            'description': 'Indian background'
        },
        {
            'name': 'Asian - Pakistani',
            'description': 'Pakistani background'
        },
        {
            'name': 'Asian - Bangladeshi',
            'description': 'Bangladeshi background'
        },
        {
            'name': 'Asian - Chinese',
            'description': 'Chinese background'
        },
        {
            'name': 'Asian - Other',
            'description': 'Other Asian background'
        },
        {
            'name': 'Black - African',
            'description': 'Black African background'
        },
        {
            'name': 'Black - Caribbean',
            'description': 'Black Caribbean background'
        },
        {
            'name': 'Black - Other',
            'description': 'Other Black background'
        },
        {
            'name': 'Other Ethnic Group',
            'description': 'Any other ethnic background'
        },
        {
            'name': 'Prefer not to say',
            'description': 'Prefers not to disclose ethnicity'
        }
    ]

    for race in races:
        Race.objects.get_or_create(
            name=race['name'],
            defaults={'description': race['description']}
        )

    # Default Reading Conditions
    conditions = [
        {
            'name': 'Dyslexia',
            'description': 'A learning disorder characterized by difficulty reading due to problems identifying speech sounds and learning how they relate to letters and words'
        },
        {
            'name': 'Visual Stress',
            'description': 'Also known as Meares-Irlen syndrome, characterized by visual distortions and discomfort when reading'
        },
        {
            'name': 'Visual Processing Disorder',
            'description': 'Difficulty making sense of visual information despite normal vision'
        },
        {
            'name': 'ADHD',
            'description': 'Attention Deficit Hyperactivity Disorder affecting reading focus and comprehension'
        },
        {
            'name': 'Autism',
            'description': 'Autism Spectrum Disorder which may affect reading and comprehension'
        },
        {
            'name': 'Dyscalculia',
            'description': 'Difficulty understanding numbers and learning math facts'
        },
        {
            'name': 'Dysgraphia',
            'description': 'Affects handwriting ability and fine motor skills'
        },
        {
            'name': 'Auditory Processing Disorder',
            'description': 'Difficulty processing and distinguishing between sounds'
        },
        {
            'name': 'Language Processing Disorder',
            'description': 'Difficulty processing and making meaning of language'
        },
        {
            'name': 'Non-Verbal Learning Disability',
            'description': 'Affects ability to understand non-verbal cues and spatial relationships'
        },
        {
            'name': 'Other',
            'description': 'Other reading-related condition not listed'
        }
    ]

    for condition in conditions:
        Condition.objects.get_or_create(
            name=condition['name'],
            defaults={'description': condition['description']}
        )

def remove_default_lookups(apps, schema_editor):
    Gender = apps.get_model('VRO_App2', 'Gender')
    Race = apps.get_model('VRO_App2', 'Race')
    Condition = apps.get_model('VRO_App2', 'Condition')
    
    Gender.objects.all().delete()
    Race.objects.all().delete()
    Condition.objects.all().delete()

class Migration(migrations.Migration):
    dependencies = [
        ('VRO_App2', '0002_agegroup_postcodearea_usergroup_and_more'),
    ]

    operations = [
        migrations.RunPython(add_default_lookups, remove_default_lookups),
    ]