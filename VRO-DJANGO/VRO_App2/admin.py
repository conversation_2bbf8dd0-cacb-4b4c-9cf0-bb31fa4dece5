from django.contrib import admin
from .models import (
    Gender, Race, Condition, AgeGroup, 
    PostcodeArea, UserGroup, UserProfile
)

class BaseNameAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    ordering = ('name',)

@admin.register(Gender)
class GenderAdmin(BaseNameAdmin):
    pass

@admin.register(Race)
class RaceAdmin(BaseNameAdmin):
    pass

@admin.register(Condition)
class ConditionAdmin(BaseNameAdmin):
    pass

@admin.register(AgeGroup)
class AgeGroupAdmin(BaseNameAdmin):
    list_display = ('name', 'min_age', 'max_age', 'is_active')
    ordering = ('min_age',)

@admin.register(PostcodeArea)
class PostcodeAreaAdmin(BaseNameAdmin):
    list_display = ('code', 'name', 'is_active')
    search_fields = ('code', 'name')
    ordering = ('code',)

@admin.register(UserGroup)
class UserGroupAdmin(BaseNameAdmin):
    list_display = ('name', 'code', 'is_active')
    search_fields = ('name', 'code')

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('get_full_name', 'age', 'gender', 'postcode', 'is_profile_completed')
    list_filter = ('is_profile_completed', 'gender', 'race', 'age_group', 'postcode_area', 'group')
    search_fields = ('first_name', 'last_name', 'postcode', 'address_line1')
    raw_id_fields = ('user',)
    filter_horizontal = ('conditions',)
    readonly_fields = ('profile_completed_date', 'last_updated')
    fieldsets = (
        ('Personal Information', {
            'fields': ('user', 'first_name', 'last_name', 'age', 'age_group', 'gender', 'race')
        }),
        ('Contact Details', {
            'fields': ('mobile', 'landline', 'address_line1', 'address_line2', 'postcode', 'postcode_area')
        }),
        ('Medical Information', {
            'fields': ('has_no_conditions', 'conditions')
        }),
        ('Profile Status', {
            'fields': ('is_profile_completed', 'profile_completed_date', 'group')
        }),
        ('Additional Information', {
            'fields': ('notes', 'last_updated')
        }),
    )