
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from django.contrib.auth import get_user_model
from VRO_App2.models import UserProfile, UserGroup
import logging
logger = logging.getLogger(__name__)

class CustomSocialAccountAdapter(DefaultSocialAccountAdapter):
    def pre_social_login(self, request, sociallogin):
        logger.info(f"pre_social_login for user: {sociallogin.user.email}")
        request.session.flush()
        
        user = sociallogin.user
        if user.id:
            logger.info(f"User already exists with id: {user.id}")
            return
        
        try:
            # Use Django's get_user_model instead of self.get_user_model
            User = get_user_model()
            existing_user = User.objects.get(email=user.email)
            logger.info(f"Found existing user: {existing_user.email}")
            sociallogin.connect(request, existing_user)
        except User.DoesNotExist:
            logger.info(f"No existing user found for: {user.email}")
            pass

    def save_user(self, request, sociallogin, form=None):
        user = super().save_user(request, sociallogin, form)
        logger.info(f"save_user called for: {user.email}")
        
        try:
            profile = user.userprofile
            logger.info(f"Existing profile found for user: {user.email}")
        except UserProfile.DoesNotExist:
            profile = UserProfile(user=user)
            logger.info(f"Created new profile for user: {user.email}")
            
        if user.email.endswith('@visualreadingonline.com'):
            try:
                admin_group = UserGroup.objects.get(code='site-admins')
                profile.group = admin_group
                profile.save()
                logger.info(f"Assigned site-admin group to: {user.email}")
            except UserGroup.DoesNotExist:
                logger.error("site-admins group not found")
                
        profile.save()
        return user






