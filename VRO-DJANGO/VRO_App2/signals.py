from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from django.contrib.sites.shortcuts import get_current_site
from .auth import send_welcome_email

User = get_user_model()

@receiver(post_save, sender=User)
def send_welcome_email_handler(sender, instance, created, **kwargs):
    if created:  # Only send email when user is first created
        # Create a fake request object since we're in a signal handler
        request = HttpRequest()
        request.META = {}
        
        # Get the first site's domain
        site = get_current_site(request)
        request.META['SERVER_NAME'] = site.domain
        request.META['SERVER_PORT'] = '443'  # Assuming HTTPS
        request.is_secure = lambda: True  # Force HTTPS
        
        # Send the welcome email
        send_welcome_email(request, instance)