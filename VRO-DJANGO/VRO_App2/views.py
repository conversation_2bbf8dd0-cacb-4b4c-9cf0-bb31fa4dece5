from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import Gender, Race, Condition, UserProfile, PendingGroupAllocation
from django.http import JsonResponse
from django.utils import timezone

@login_required
def app_home(request):
    try:
        profile = request.user.userprofile
    except UserProfile.DoesNotExist:
        # Create a default profile if one doesn't exist
        default_gender, _ = Gender.objects.get_or_create(name='Prefer not to say')
        default_race, _ = Race.objects.get_or_create(name='Prefer not to say')
        
        profile = UserProfile.objects.create(
            user=request.user,
            first_name=request.user.first_name or '',
            last_name=request.user.last_name or '',
            age=18,  # Default age
            gender=default_gender,
            race=default_race,
            is_profile_completed=False,
            has_no_conditions=True  # Set a default value
        )
    
    if not profile.is_profile_completed:
        return redirect('VRO_App2:complete_profile')
        
    if profile.group:
        # Use the group's code instead of the group object
        return redirect(f'VRO_App3:{profile.group.code}')
    
    # If no group is assigned, show the default home page
    return render(request, 'VRO_App2/home.html')

@login_required
def profile_view(request):
    try:
        profile = request.user.userprofile
    except UserProfile.DoesNotExist:
        # Create a default profile if one doesn't exist
        default_gender, _ = Gender.objects.get_or_create(name='Prefer not to say')
        default_race, _ = Race.objects.get_or_create(name='Prefer not to say')
        
        profile = UserProfile.objects.create(
            user=request.user,
            first_name=request.user.first_name or '',
            last_name=request.user.last_name or '',
            gender=default_gender,
            race=default_race,
            is_profile_completed=False,
            has_no_conditions=True
        )

    if request.method == 'POST':
        # Handle form submission
        data = request.POST
        
        # Validate age
        try:
            age = int(data.get('age', '0'))
            if age < 1 or age > 99:
                messages.error(request, 'Please enter a valid age between 1 and 99.')
                return redirect('VRO_App2:profile')
        except (ValueError, TypeError):
            messages.error(request, 'Please enter a valid age.')
            return redirect('VRO_App2:profile')

        profile_data = {
            'first_name': data.get('first_name'),
            'last_name': data.get('last_name'),
            'age': age,
            'gender_id': data.get('gender'),
            'race_id': data.get('race'),
            'mobile': data.get('mobile'),
            'landline': data.get('landline'),
            'address_line1': data.get('address_line1'),
            'address_line2': data.get('address_line2'),
            'postcode': data.get('postcode', '').upper(),
            'has_no_conditions': data.get('has_no_conditions') == 'on',
            'notes': data.get('notes'),
            'is_profile_completed': True
        }

        # Validate required fields
        required_fields = ['first_name', 'last_name', 'age', 'gender_id', 
                         'race_id', 'address_line1', 'postcode']
        
        if not all(profile_data.get(field) for field in required_fields):
            messages.error(request, 'Please fill in all required fields.')
            return redirect('VRO_App2:profile')

        # Validate conditions
        conditions = request.POST.getlist('conditions')
        has_no_conditions = data.get('has_no_conditions') == 'on'
        
        if not has_no_conditions and not conditions:
            messages.error(request, 'Please either select some conditions or check "None".')
            return redirect('VRO_App2:profile')
        
        if has_no_conditions and conditions:
            messages.error(request, 'You cannot select both "None" and specific conditions.')
            return redirect('VRO_App2:profile')

        try:
            for key, value in profile_data.items():
                setattr(profile, key, value)
            
            profile.save()

            # Update conditions
            profile.conditions.clear()
            if not profile_data['has_no_conditions']:
                profile.conditions.set(conditions)

            messages.success(request, 'Profile updated successfully!')
            return redirect('VRO_App2:home')
            
        except Exception as e:
            messages.error(request, 'There was an error saving your profile. Please try again.')
            return redirect('VRO_App2:profile')

    context = {
        'profile': profile,
        'genders': Gender.objects.all(),
        'races': Race.objects.all(),
        'conditions': Condition.objects.all(),
    }
    return render(request, 'VRO_App2/profile.html', context)

@login_required
def learn_more(request):
    return render(request, 'VRO_App2/learn_more.html')

@login_required
def purchase_options(request):
    modals = [
        ('individualCourseModal', 'Choose Your Individual Course'),
        ('distanceLearningModal', 'Choose Your Distance Learning Course'),
        ('consultationModal', 'Choose Your Consultation Session'),
        ('coachModal', 'Become a Visual Reading Coach'),
    ]
    return render(request, 'VRO_App2/purchase_options.html', {'modals': modals})

@login_required
def members_faq(request):
    return render(request, 'VRO_App2/members_faq.html')

def check_pending_group(request):
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Not authenticated'}, status=401)
    
    pending = PendingGroupAllocation.objects.filter(
        email=request.user.email,
        is_used=False
    ).first()
    
    if pending:
        return JsonResponse({
            'found': True,
            'group_name': pending.group.name,
            'group_id': pending.group.id
        })
    return JsonResponse({'found': False})

def link_account_to_group(request):
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Not authenticated'}, status=401)
    
    pending = PendingGroupAllocation.objects.filter(
        email=request.user.email,
        is_used=False
    ).first()
    
    if pending:
        # Update user's profile
        profile = request.user.userprofile
        profile.group = pending.group
        profile.save()
        
        # Mark allocation as used
        pending.is_used = True
        pending.used_at = timezone.now()
        pending.save()
        
        return JsonResponse({
            'success': True,
            'group_name': pending.group.name,
            'message': 'Your account has been successfully linked. Please log out and log back in to access your new features.'
        })
    return JsonResponse({'success': False, 'error': 'No pending allocation found'})
