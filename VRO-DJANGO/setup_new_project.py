#!/usr/bin/env python3
import os
import sys
import shutil
import subprocess
import fileinput
from pathlib import Path
import platform

def clean_directory():
    """Remove temporary files and directories."""
    items_to_remove = [
        'venv',
        'db.sqlite3',
        '.vscode',
        '.idea',
        '__pycache__',
        '.pytest_cache',
        '.coverage',
        '.DS_Store',
        '*.pyc',
        '.env',
        '.env.dev',
        '.env.prod'
    ]
    
    print("\nCleaning directory...")
    for item in items_to_remove:
        paths = Path('.').glob('**/' + item)
        for path in paths:
            if path.is_file():
                path.unlink()
                print(f"Removed file: {path}")
            elif path.is_dir():
                shutil.rmtree(path)
                print(f"Removed directory: {path}")

def rename_in_files(directory, old_project, new_project, old_app, new_app):
    """Recursively rename strings in all files."""
    valid_extensions = {
        '.py', '.html', '.js', '.json', '.yml', '.yaml', '.md', 
        '.sh', '.txt', '.css', '.dockerfile', ''
    }
    
    skip_dirs = {'.git', 'venv', 'node_modules', '__pycache__'}
    
    print("\nUpdating file contents...")
    for root, dirs, files in os.walk(directory):
        dirs[:] = [d for d in dirs if d not in skip_dirs]
        
        for filename in files:
            filepath = Path(root) / filename
            
            if filepath.suffix.lower() not in valid_extensions:
                continue
                
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                new_content = content.replace(old_project, new_project)
                new_content = new_content.replace(old_app, new_app)
                
                # Special handling for header.html
                if filename == 'header.html' and 'TEMPLATE PROJECT' in new_content:
                    new_content = new_content.replace(
                        'TEMPLATE PROJECT', 
                        f'Home page for the {new_app} and {new_project}'
                    )
                
                # Special handling for index.html VRO Project title
                if filename == 'index.html' and 'VRO Project' in new_content:
                    new_content = new_content.replace(
                        'VRO Project', 
                        f'{new_project}'
                    )
                
                # Update docker container names in docker-compose files
                if filename in ['docker-compose.dev.yml', 'docker-compose.prod.yml']:
                    new_content = new_content.replace(
                        'container_name: web', 
                        f'container_name: {new_project.lower()}-{"dev" if "dev" in filename else "prod"}'
                    )
                
                if new_content != content:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    print(f"Updated: {filepath}")
            except UnicodeDecodeError:
                continue

def rename_directories(directory, old_project, new_project, old_app, new_app):
    """Rename directories containing the old names."""
    print("\nRenaming directories...")
    for root, dirs, files in os.walk(directory, topdown=False):
        for dir_name in dirs:
            full_path = Path(root) / dir_name
            
            new_name = dir_name.replace(old_project, new_project)
            new_name = new_name.replace(old_app, new_app)
            
            if new_name != dir_name:
                new_path = full_path.parent / new_name
                try:
                    full_path.rename(new_path)
                    print(f"Renamed directory: {full_path} → {new_path}")
                except Exception as e:
                    print(f"Error renaming directory {full_path}: {e}")

def setup_virtual_environment():
    """Create and set up a new virtual environment."""
    print("\nSetting up virtual environment...")
    try:
        # Create virtual environment using system Python
        subprocess.run([sys.executable, '-m', 'venv', 'venv'], check=True)
        print("Virtual environment created successfully")
        
        # Get the appropriate pip and python commands based on OS
        if platform.system() == "Windows":
            pip_cmd = str(Path('venv/Scripts/pip').absolute())
            python_cmd = str(Path('venv/Scripts/python').absolute())
        else:
            pip_cmd = str(Path('venv/bin/pip').absolute())
            python_cmd = str(Path('venv/bin/python').absolute())
            
        # Install requirements
        print("Installing requirements...")
        subprocess.run([pip_cmd, 'install', '--upgrade', 'pip'], check=True)
        subprocess.run([pip_cmd, 'install', '-r', 'requirements.txt'], check=True)
        
        # Create new database
        print("Initializing database...")
        subprocess.run([python_cmd, 'manage.py', 'migrate'], check=True)
        print("Database initialized successfully")
        
    except subprocess.CalledProcessError as e:
        print(f"Error during virtual environment setup: {e}")
        print("\nPlease manually run these commands:")
        print("1. python -m venv venv")
        print("2. source venv/bin/activate  # On Unix/Linux")
        print("   .\\venv\\Scripts\\activate  # On Windows")
        print("3. pip install -r requirements.txt")
        print("4. python manage.py migrate")

def update_howto(new_project):
    """Update the HowTo.md file with project-specific information."""
    howto_path = Path('HowTo.md')
    if howto_path.exists():
        with open(howto_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Update the content with project-specific information
        updated_content = content.replace('# PROJECT SET UP', f'# {new_project} Setup Guide')
        
        with open(howto_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        print("\nUpdated HowTo.md with project-specific information")

def main():
    print("Welcome to the Django Project Setup Script!")
    print("------------------------------------------")
    
    # Get project and app names from user
    new_project = input("\nEnter your new project name (e.g., BlogProject): ").strip()
    new_app = input("Enter your new app name (e.g., BlogApp): ").strip()
    
    if not new_project or not new_app:
        print("Error: Project and app names cannot be empty")
        sys.exit(1)
    
    # Confirm with user
    print(f"\nYou are about to:")
    print(f"1. Rename VRO_Project to {new_project}")
    print(f"2. Rename VRO_App1 to {new_app}")
    print("3. Remove existing venv, database, and temporary files")
    print("4. Create a new virtual environment")
    print("5. Initialize a new database")
    confirm = input("\nProceed? (y/n): ").lower()
    
    if confirm != 'y':
        print("Setup cancelled")
        sys.exit(0)
    
    # Original template names
    old_project = "VRO_Project"
    old_app = "VRO_App1"
    
    # Get the directory containing this script
    base_dir = Path(__file__).parent
    
    # Clean directory
    clean_directory()
    
    # Rename files and directories
    rename_in_files(base_dir, old_project, new_project, old_app, new_app)
    rename_directories(base_dir, old_project, new_project, old_app, new_app)
    
    # Set up virtual environment
    setup_virtual_environment()
    
    # Update HowTo.md
    update_howto(new_project)
    
    print("\nSetup complete!")
    print("\nNext steps:")
    if platform.system() == "Windows":
        print("1. Activate virtual environment: .\\venv\\Scripts\\activate")
    else:
        print("1. Activate virtual environment: source venv/bin/activate")
    print("2. Create a superuser: python manage.py createsuperuser")
    print("3. Start the development server: python manage.py runserver")
    print("\nFor Docker development:")
    print(f"- Run: docker-compose -f docker-compose.dev.yml up --build")
    print(f"- Container will be named: {new_project.lower()}-dev")
    print("\nFor Docker production:")
    print(f"- Run: docker-compose -f docker-compose.prod.yml up --build")
    print(f"- Container will be named: {new_project.lower()}-prod")
    print("\nPlease run: source venv/bin/activate")

if __name__ == "__main__":
    main()
