# Generated by Django 4.2.9 on 2025-03-22 20:01

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="PricingCard",
            fields=[
                (
                    "Card_ID",
                    models.AutoField(db_column="Card-ID", primary_key=True, serialize=False),
                ),
                ("Card_Title", models.<PERSON><PERSON><PERSON><PERSON>(db_column="Card-Title", max_length=200)),
                ("Card_Sub_Title", models.Char<PERSON>ield(db_column="Card-Sub-Title", max_length=200)),
                ("Price", models.DecimalField(db_column="Price", decimal_places=2, max_digits=10)),
                ("Point1", models.<PERSON><PERSON><PERSON><PERSON>(blank=True, db_column="Point1", max_length=200)),
                ("Point2", models.Char<PERSON>ield(blank=True, db_column="Point2", max_length=200)),
                ("Point3", models.<PERSON><PERSON><PERSON><PERSON>(blank=True, db_column="Point3", max_length=200)),
                (
                    "Card_Button_Text",
                    models.<PERSON><PERSON><PERSON><PERSON>(db_column="Card-Button-Text", max_length=100),
                ),
                ("Card_Button_Link", models.URLField(db_column="Card-Button-Link")),
            ],
            options={
                "ordering": ["Card_ID"],
            },
        ),
    ]
