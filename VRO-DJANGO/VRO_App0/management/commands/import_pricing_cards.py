from django.core.management.base import BaseCommand
import csv
from VRO_App0.models import PricingCard
from decimal import Decimal, InvalidOperation

class Command(BaseCommand):
    help = 'Import pricing cards from CSV file'

    def handle(self, *args, **options):
        csv_file = 'VRO_App0_pricing_cards.csv'
        
        with open(csv_file, 'r') as file:
            csv_reader = csv.DictReader(file)
            for row in csv_reader:
                try:
                    # Handle special case for TBA prices
                    if row['Price'].strip() == '£TBA':
                        price = Decimal('0.00')
                    else:
                        # Remove £ symbol and any whitespace, then convert to Decimal
                        price_str = row['Price'].replace('£', '').strip()
                        price = Decimal(price_str)

                    PricingCard.objects.update_or_create(
                        Card_ID=row['Card-ID'],
                        defaults={
                            'Card_Title': row['Card-Title'].strip(),
                            'Card_Sub_Title': row['Card-Sub-Title'].strip(),
                            'Price': price,
                            'Point1': row['Point1'].strip(),
                            'Point2': row['Point2'].strip(),
                            'Point3': row['Point3'].strip(),
                            'Card_Button_Text': row['Card-Button-Text'].strip(),
                            'Card_Button_Link': row['Card-Button-Link'].strip()
                        }
                    )
                    self.stdout.write(self.style.SUCCESS(f'Successfully imported card: {row["Card-Title"]}'))
                
                except InvalidOperation as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error processing price for card {row["Card-ID"]}: {row["Price"]}')
                    )
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error importing card {row["Card-ID"]}: {str(e)}')
                    )

        self.stdout.write(self.style.SUCCESS('Finished importing pricing cards'))
