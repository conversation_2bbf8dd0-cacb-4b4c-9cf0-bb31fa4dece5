from django.db import models

class PricingCard(models.Model):
    Card_ID = models.AutoField(primary_key=True, db_column='Card-ID')
    Card_Title = models.CharField(max_length=200, db_column='Card-Title')
    Card_Sub_Title = models.CharField(max_length=200, db_column='Card-Sub-Title')
    Price = models.DecimalField(max_digits=10, decimal_places=2, db_column='Price')
    Point1 = models.CharField(max_length=200, blank=True, db_column='Point1')
    Point2 = models.Char<PERSON>ield(max_length=200, blank=True, db_column='Point2')
    Point3 = models.CharField(max_length=200, blank=True, db_column='Point3')
    Card_Button_Text = models.CharField(max_length=100, db_column='Card-Button-Text')
    Card_Button_Link = models.URLField(db_column='Card-Button-Link')

    class Meta:
        ordering = ['Card_ID']

    def __str__(self):
        return self.Card_Title

