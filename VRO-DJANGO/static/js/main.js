var acc = document.getElementsByClassName("accordion");
var i;

for (i = 0; i < acc.length; i++) {
  acc[i].addEventListener("click", function () {
    this.classList.toggle("active");
    var panel = this.nextElementSibling;
    if (panel.style.maxHeight) {
      panel.style.maxHeight = null;
    } else {
      panel.style.maxHeight = panel.scrollHeight + "px";
    }
  });
}

document.addEventListener('DOMContentLoaded', function() {
  const contactForm = document.getElementById("contact-form");
  
  if (contactForm) {
    contactForm.addEventListener("submit", function (event) {
      var email = document.getElementById("id_email").value;
      var agreeChecked = document.getElementById("agree").checked;
      var message = document.getElementById("id_message").value;
      let organisationInput = document.getElementById("organisation");
      let isValid = true;

      // Validate email format
      if (!validateEmail(email)) {
        alert(
          "Please enter a valid email address so that we can reply to your message if needed.",
        );
        event.preventDefault();
        isValid = false;
      }

      // Ensure the agree checkbox is checked
      if (!agreeChecked) {
        alert("You must agree to our Privacy Policy to send us a message.");
        event.preventDefault();
        isValid = false;
      }

      // Validation for message length
      if (message.length < 30) {
        alert(
          "We have implemented a rule on messages to avoid submissions from bots and spammers, please enter a slightly longer message.",
        );
        event.preventDefault();
        isValid = false;
      }

      // Add 'N/A' to organisation if blank
      if (organisationInput && organisationInput.value.trim() === "") {
        organisationInput.value = "N/A";
      }

      return isValid;
    });
  }
});

// Function to validate email format
function validateEmail(email) {
  var re = /\S+@\S+\.\S+/;
  return re.test(email);
}
