(function() {
    console.log('Profile JS initializing...');
    
    const noConditionsCheckbox = document.getElementById('has_no_conditions');
    const conditionCheckboxes = document.querySelectorAll('.condition-checkbox');
    
    if (!noConditionsCheckbox || !conditionCheckboxes.length) {
        console.error('Required elements not found');
        return;
    }

    function handleConditionChange(checkbox) {
        if (checkbox.checked) {
            noConditionsCheckbox.checked = false;
        } else {
            // If no conditions are checked, check "None"
            const anyChecked = Array.from(conditionCheckboxes).some(cb => cb.checked);
            if (!anyChecked) {
                noConditionsCheckbox.checked = true;
            }
        }
    }

    function handleNoneChange() {
        if (noConditionsCheckbox.checked) {
            conditionCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }
    }

    // Add event listeners
    conditionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => handleConditionChange(checkbox));
    });

    noConditionsCheckbox.addEventListener('change', handleNoneChange);

    // Initial state check
    const anyChecked = Array.from(conditionCheckboxes).some(cb => cb.checked);
    if (!anyChecked && !noConditionsCheckbox.checked) {
        noConditionsCheckbox.checked = true;
    }
})();
