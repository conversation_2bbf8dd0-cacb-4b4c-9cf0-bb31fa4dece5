version: "3.8"

services:
  web:
    container_name: ${VRO_Project}-prod
    build:
      context: .
      target: production
      args:
        - DOCKER_BUILDKIT=1
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    environment:
      - DEBUG=0
    networks:
      - app_network

volumes:
  static_volume:
  media_volume:

networks:
  app_network:
    driver: bridge
