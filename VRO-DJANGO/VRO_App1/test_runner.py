from django.test.runner import <PERSON>ver<PERSON>unner
from django.utils.termcolors import colorize
import sys

class CustomTestRunner(DiscoverRunner):
    def run_tests(self, test_labels, extra_tests=None, **kwargs):
        self.setup_test_environment()
        suite = self.build_suite(test_labels)
        
        print("\n" + "="*80)
        print(colorize(f"Running {suite.countTestCases()} tests...", fg="yellow"))
        print("="*80 + "\n")
        
        result = self.run_suite(suite)
        
        # Store failed test details
        failed_tests = []
        
        # Collect failure details
        for failure in result.failures + result.errors:
            test_case = failure[0]
            error_lines = failure[1].split('\n')
            # Get the actual error message by finding the first line after "Error: "
            error_message = "Unknown error"
            for line in error_lines:
                if ": " in line and not line.startswith('  File "'):
                    error_message = line.split(": ", 1)[1]
                    break
            
            failed_tests.append({
                'test': test_case.id(),
                'error': error_message
            })
        
        # Print summary
        print("\n" + "="*80)
        print(colorize("Test Summary:", fg="yellow"))
        print("-"*80)
        print(f"Total tests run: {result.testsRun}")
        print(colorize(f"Tests passed: {result.testsRun - len(result.failures) - len(result.errors)}", fg="green"))
        
        # Print failed tests in a concise format
        if failed_tests:
            print("\nFailed Tests:")
            print("-"*80)
            for i, test in enumerate(failed_tests, 1):
                test_name = test['test'].split('.')[-1]  # Get just the test method name
                test_class = test['test'].split('.')[-2]  # Get the test class name
                print(colorize(f"{i}. {test_class}.{test_name}", fg="red"))
                print(f"   Error: {test['error']}\n")
        
        if result.failures or result.errors:
            print(colorize(f"Failed Tests: {len(result.failures) + len(result.errors)}", fg="red"))
        
        print("="*80 + "\n")
        
        self.teardown_test_environment()
        return self.suite_result(suite, result)