from django.shortcuts import render, get_object_or_404, redirect
from django.http import Http404, JsonResponse, HttpResponse
from django.contrib import messages
from django.contrib.auth import login, logout, get_user_model
from django.contrib.auth.decorators import login_required
from pathlib import Path
from .utils import load_word_document, transform_html_urls
from .models import Blog, Document, DocumentCategory
import mammoth
from django.conf import settings
import os
from .forms import ContactForm, MagicLinkForm  # Make sure you have these forms defined
from .auth import send_magic_link
from allauth.socialaccount.models import SocialLogin
from django.contrib.auth import login
import logging
logger = logging.getLogger(__name__)
from django.contrib.sessions.backends.db import SessionStore

def index(request):
    return render(request, 'VRO_App1/index.html')

def about_view(request):
    return render(request, 'VRO_App1/about.html')

def dr_cooper_view(request):
    try:
        doc = Document.objects.get(
            category__name='Pages',
            url_name='dr-cooper',
            is_published=True
        )
        
        with doc.file.open('rb') as docx_file:
            custom_styles = get_custom_style_map()
            result = mammoth.convert_to_html(
                docx_file,
                style_map=custom_styles
            )
            html_content = result.value
    except Document.DoesNotExist as e:
        html_content = "<p>Error: Document not found in database</p>"
        print(f"Debug - Document not found: {str(e)}")
        # Debug - Print all documents in the Pages category
        pages = Document.objects.filter(category__name='Pages')
        print(f"Available Pages documents: {[doc.url_name for doc in pages]}")
    except Exception as e:
        html_content = f"<p>Error loading content: {str(e)}</p>"
        print(f"Debug - Error: {str(e)}")
    
    return render(request, 'VRO_App1/dr-cooper.html', {
        'mammoth_content': html_content
    })

def learn_more_view(request):
    return render(request, 'VRO_App1/learn-more.html')

def get_started_view(request):
    return render(request, 'VRO_App1/get-started.html')

def articles_view(request):
    articles = Document.objects.select_related('category').filter(
        category__name__iexact='Articles',
        is_published=True
    ).order_by('-publication_date')
    
    return render(request, 'VRO_App1/resources-articles.html', {
        'articles': articles
    })

def blog_posts_index_view(request):
    blogs = Document.objects.select_related('category').filter(
        category__name__iexact='Blogs',
        is_published=True
    ).order_by('-publication_date')
    
    return render(request, 'VRO_App1/blog-posts.html', {
        'blogs': blogs,
        'debug_all_docs': Document.objects.select_related('category').all(),
        'debug_categories': DocumentCategory.objects.all(),
    })

def our_results_view(request):
    try:
        # Debug print statements
        print("Attempting to fetch our results document")
        print("All Pages documents:", Document.objects.filter(category__name='Pages').values_list('url_name', 'is_published'))
        
        doc = Document.objects.get(
            category__name='Pages',
            url_name='our-results',
            is_published=True
        )
        
        print(f"Found document: {doc.title}, File path: {doc.file.path}")
        
        with doc.file.open('rb') as docx_file:
            custom_styles = get_custom_style_map()
            result = mammoth.convert_to_html(
                docx_file,
                style_map=custom_styles
            )
            html_content = result.value
            print("Successfully converted document to HTML")
    except Document.DoesNotExist as e:
        html_content = "<p>Results content not available at this time.</p>"
        print(f"Debug - Document not found: {str(e)}")
        # Debug - Print all documents in the Pages category
        pages = Document.objects.filter(category__name='Pages')
        print(f"Available Pages documents: {[doc.url_name for doc in pages]}")
    except Exception as e:
        html_content = f"<p>Error loading content: {str(e)}</p>"
        print(f"Debug - Error: {str(e)}")
        print(f"Error type: {type(e)}")
    
    return render(request, 'VRO_App1/our-results.html', {
        'mammoth_content': html_content
    })

def our_products_and_services_view(request):
    return render(request, 'VRO_App1/resources-our-products-and-services.html')

def products_view(request):
    return render(request, 'VRO_App1/products.html')

def social_media_view(request):
    return render(request, 'VRO_App1/social-media.html')

def student_feedback_view(request):
    try:
        # Debug print statements
        print("Attempting to fetch student feedback document")
        print("All Pages documents:", Document.objects.filter(category__name='Pages').values_list('url_name', 'is_published'))
        
        doc = Document.objects.get(
            category__name='Pages',
            url_name='student-feedback',
            is_published=True
        )
        
        print(f"Found document: {doc.title}, File path: {doc.file.path}")
        
        with doc.file.open('rb') as docx_file:
            custom_styles = get_custom_style_map()
            result = mammoth.convert_to_html(
                docx_file,
                style_map=custom_styles
            )
            html_content = result.value
            print("Successfully converted document to HTML")
    except Document.DoesNotExist as e:
        html_content = "<p>Student feedback content not available at this time.</p>"
        print(f"Debug - Document not found: {str(e)}")
        # Debug - Print all documents in the Pages category
        pages = Document.objects.filter(category__name='Pages')
        print(f"Available Pages documents: {[doc.url_name for doc in pages]}")
    except Exception as e:
        html_content = f"<p>Error loading content: {str(e)}</p>"
        print(f"Debug - Error: {str(e)}")
        print(f"Error type: {type(e)}")
    
    return render(request, 'VRO_App1/student-feedback.html', {
        'mammoth_content': html_content
    })

def useful_links_view(request):
    try:
        doc = Document.objects.get(
            category__name='Pages',
            url_name='useful-links',
            is_published=True
        )
        
        with doc.file.open('rb') as docx_file:
            custom_styles = get_custom_style_map()
            result = mammoth.convert_to_html(
                docx_file,
                style_map=custom_styles
            )
            html_content = result.value
    except (Document.DoesNotExist, Exception) as e:
        html_content = "<p>Useful links content not available at this time.</p>"
    
    return render(request, 'VRO_App1/useful-links.html', {
        'mammoth_content': html_content
    })

def youtube_view(request):
    return render(request, 'VRO_App1/youtube.html')

def pay_per_view(request):
    return render(request, 'VRO_App1/pay-per-view.html')

def presentations(request):
    return render(request, 'VRO_App1/presentations.html')

# Article views
def article_how_science_can_become_view(request):
    return render(request, 'VRO_App1/articles/science.html')

def article_how_many_labels_view(request):
    return render(request, 'VRO_App1/articles/labels.html')

def article_the_development_and_impact_view(request):
    return render(request, 'VRO_App1/articles/development-impact.html')

def article_the_social_mechanics_view(request):
    return render(request, 'VRO_App1/articles/social-mechanics.html')

def article_why_dyslexia_is_not_view(request):
    return render(request, 'VRO_App1/articles/dyslexia.html')

def article_the_first_110_visual_readers_view(request):
    return render(request, 'VRO_App1/articles/first-110.html')

def article_weekly_progress_of_view(request):
    return render(request, 'VRO_App1/articles/weekly-progress.html')

# Get Started section views
def get_started_private_students(request):
    return render(request, 'VRO_App1/get-started-private-students.html')

def get_started_private_consultation_view(request):
    return render(request, 'VRO_App1/get-started-private-consultation.html')

def get_started_gift_a_visual_reading_course(request):
    return render(request, 'VRO_App1/get-started-gift-a-visual-reading-course.html')

def get_started_organisations(request):
    return render(request, 'VRO_App1/get-started-organisations.html')

def get_started_coaches_view(request):
    return render(request, 'VRO_App1/get-started-coaches.html')

def get_started_dsa_students_view(request):
    return render(request, 'VRO_App1/get-started-dsa-students.html')

def video_dsa_students_view(request):
    return render(request, 'VRO_App1/video-dsa-students.html')

# Blog views
def blog_the_magnocellular_pathway_view(request):
    return render(request, 'VRO_App1/blogs/magnocellular-pathway.html')

def blog_visual_reading_works_with_children_view(request):
    return render(request, 'VRO_App1/blogs/visual-reading-children.html')

def blog_changes_to_the_dsa_view(request):
    return render(request, 'VRO_App1/blogs/dsa-changes.html')

def blog_a_review_of_visual_reading_2023_view(request):
    return render(request, 'VRO_App1/blogs/2023-review.html')

# Legal and Support views
def legal_statement(request):
    try:
        doc = Document.objects.get(
            category__name='Pages',
            url_name='legal-statement',
            is_published=True
        )
        
        with doc.file.open('rb') as docx_file:
            custom_styles = get_custom_style_map()
            result = mammoth.convert_to_html(
                docx_file,
                style_map=custom_styles
            )
            html_content = result.value
    except Document.DoesNotExist as e:
        html_content = "<p>Legal statement content not available at this time.</p>"
        print(f"Debug - Document not found: {str(e)}")
        pages = Document.objects.filter(category__name='Pages')
        print(f"Available Pages documents: {[doc.url_name for doc in pages]}")
    except Exception as e:
        html_content = f"<p>Error loading content: {str(e)}</p>"
        print(f"Debug - Error: {str(e)}")
    
    return render(request, 'VRO_App1/legal-statement.html', {
        'mammoth_content': html_content
    })

def terms_of_service(request):
    try:
        doc = Document.objects.get(
            category__name='Pages',
            url_name='terms-of-service',
            is_published=True
        )
        
        with doc.file.open('rb') as docx_file:
            custom_styles = get_custom_style_map()
            result = mammoth.convert_to_html(
                docx_file,
                style_map=custom_styles
            )
            html_content = result.value
    except Document.DoesNotExist as e:
        html_content = "<p>Terms of service content not available at this time.</p>"
        print(f"Debug - Document not found: {str(e)}")
        pages = Document.objects.filter(category__name='Pages')
        print(f"Available Pages documents: {[doc.url_name for doc in pages]}")
    except Exception as e:
        html_content = f"<p>Error loading content: {str(e)}</p>"
        print(f"Debug - Error: {str(e)}")
    
    return render(request, 'VRO_App1/terms-of-service.html', {
        'mammoth_content': html_content
    })

def privacy_policy(request):
    try:
        doc = Document.objects.get(
            category__name='Pages',
            url_name='privacy-policy',
            is_published=True
        )
        
        with doc.file.open('rb') as docx_file:
            custom_styles = get_custom_style_map()
            result = mammoth.convert_to_html(
                docx_file,
                style_map=custom_styles
            )
            html_content = result.value
    except Document.DoesNotExist as e:
        html_content = "<p>Privacy policy content not available at this time.</p>"
        print(f"Debug - Document not found: {str(e)}")
        pages = Document.objects.filter(category__name='Pages')
        print(f"Available Pages documents: {[doc.url_name for doc in pages]}")
    except Exception as e:
        html_content = f"<p>Error loading content: {str(e)}</p>"
        print(f"Debug - Error: {str(e)}")
    
    return render(request, 'VRO_App1/privacy-policy.html', {
        'mammoth_content': html_content
    })

def support_view(request):
    return render(request, 'VRO_App1/support.html')

def contact(request):
    # Clear any existing messages that aren't related to the contact form
    existing_messages = messages.get_messages(request)
    for message in existing_messages:
        if not message.extra_tags or 'contact_form' not in message.extra_tags:
            existing_messages.used = True

    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Your message has been sent successfully!', 
                           extra_tags='contact_form')
            return redirect('VRO_App1:contact_success')
    else:
        form = ContactForm()
    
    return render(request, 'VRO_App1/contact-us.html', {'form': form})

def contact_success(request):
    return render(request, 'VRO_App1/contact-success.html')

def cookie_policy(request):
    try:
        doc = Document.objects.get(
            category__name='Pages',
            url_name='cookie-policy',
            is_published=True
        )
        
        with doc.file.open('rb') as docx_file:
            custom_styles = get_custom_style_map()
            result = mammoth.convert_to_html(
                docx_file,
                style_map=custom_styles
            )
            html_content = result.value
    except Document.DoesNotExist as e:
        html_content = "<p>Error: Document not found in database</p>"
        print(f"Debug - Document not found: {str(e)}")
        # Debug - Print all documents in the Pages category
        pages = Document.objects.filter(category__name='Pages')
        print(f"Available Pages documents: {[doc.url_name for doc in pages]}")
    except Exception as e:
        html_content = f"<p>Error loading content: {str(e)}</p>"
        print(f"Debug - Error: {str(e)}")
    
    return render(request, 'VRO_App1/cookie-policy.html', {
        'mammoth_content': html_content
    })

# Authentication views
def please_login_view(request):
    next_url = request.GET.get('next', '')
    # Prevent redirect loops
    if next_url == '/please-login/':
        next_url = '/'
    
    return render(request, 'VRO_App1/please-login.html', {
        'next': next_url
    })

def logged_out_view(request):
    return render(request, 'VRO_App1/logged-out.html')

def logout_view(request):
    # Clear all messages before logout
    storage = messages.get_messages(request)
    for message in storage:
        pass  # Iterate through messages to mark them as used
    storage.used = True
    
    # Perform logout
    logout(request)
    
    # Clear any remaining session data
    request.session.flush()
    
    # Redirect to logged out page
    return redirect('VRO_App1:logged_out')

def signup_view(request):
    return render(request, 'VRO_App1/signup.html')

def signup_confirmation(request):
    return render(request, 'VRO_App1/signup-confirmation.html')

def resend_verification(request):
    return render(request, 'VRO_App1/resend-verification.html')

def verify_email(request, uidb64, token):
    # Add email verification logic here
    return render(request, 'VRO_App1/verify-email.html')

def login_view(request):
    return render(request, 'VRO_App1/login.html')

def word_test_view(request):
    return render(request, 'VRO_App1/word-test.html')

def blog_demo_view(request):
    return render(request, 'VRO_App1/blog-demo.html')

def articles_and_blogs_view(request):
    return render(request, 'VRO_App1/articles-and-blogs.html')

def articles_index_view(request):
    articles = Document.objects.select_related('category').filter(
        category__name__iexact='Articles',
        is_published=True
    ).order_by('-publication_date')
    
    return render(request, 'VRO_App1/articles-index.html', {
        'articles': articles
    })

def faqs_view(request):
    try:
        doc = Document.objects.get(
            category__name='Pages',
            url_name='faqs',
            is_published=True
        )
        
        with doc.file.open('rb') as docx_file:
            custom_styles = get_custom_style_map()
            result = mammoth.convert_to_html(
                docx_file,
                style_map=custom_styles
            )
            html_content = result.value
    except (Document.DoesNotExist, Exception) as e:
        html_content = "<p>FAQs content not available at this time.</p>"
    
    return render(request, 'VRO_App1/faqs.html', {
        'mammoth_content': html_content
    })

def request_magic_link(request):
    # First clear all existing messages
    storage = messages.get_messages(request)
    storage.used = False  # Reset the used flag
    for message in storage:
        pass  # Iterate through messages to mark them as used
    storage.used = True
    
    if request.method == 'POST':
        form = MagicLinkForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            User = get_user_model()
            user = User.objects.filter(email=email).first()
            
            if user:
                send_magic_link(request, user)
            
            messages.success(request, "If an account exists for this email address, we'll send you a magic login link.")
            return render(request, 'VRO_App1/request_magic_link.html', {'form': MagicLinkForm()})
    else:
        form = MagicLinkForm()
    
    return render(request, 'VRO_App1/request_magic_link.html', {
        'form': form
    })

def magic_link_login(request, uidb64, token):
    # Add magic link login logic here
    return render(request, 'VRO_App1/magic-link-login.html')

def check_session(request):
    """Check if user is authenticated without clearing any session data"""
    is_authenticated = request.user.is_authenticated
    return JsonResponse({
        'is_authenticated': is_authenticated,
        'force_reload': False  # Don't force reload unless absolutely necessary
    })

@login_required
def app_home(request):
    return redirect('VRO_App2:home')

def article_list(request):
    return render(request, 'VRO_App1/article-list.html')

def article_detail(request, slug):
    return render(request, 'VRO_App1/article-detail.html')

def blog_list(request):
    return render(request, 'VRO_App1/blog-list.html')

def blog_detail(request, slug):
    return render(request, 'VRO_App1/blog-detail.html')

@login_required
def upload_document(request):
    return render(request, 'VRO_App1/upload-document.html')

def get_custom_style_map():
    """Define custom style mapping for mammoth conversion"""
    return """
        p[style-name='Center'] => p.center
        img => img.centered
    """

def article_view(request, article_name):
    article = get_object_or_404(Document, 
        url_name=article_name,
        category__name='Articles',
        is_published=True
    )
    
    # Get the path to the .docx file
    if article.file:
        try:
            with article.file.open('rb') as docx_file:
                # Convert the .docx content to HTML with custom styling
                custom_styles = get_custom_style_map()
                result = mammoth.convert_to_html(
                    docx_file,
                    style_map=custom_styles,
                    transform_document=lambda element: element
                )
                html_content = result.value
        except Exception as e:
            html_content = f"<p>Error loading article content: {str(e)}</p>"
    else:
        html_content = "<p>No content available for this article.</p>"

    return render(request, 'VRO_App1/article-content.html', {
        'article': article,
        'mammoth_content': html_content
    })

def blog_view(request, blog_name):
    blog = get_object_or_404(Document, 
        url_name=blog_name,
        category__name='Blogs',
        is_published=True
    )
    
    # Get the path to the .docx file
    if blog.file:
        try:
            with blog.file.open('rb') as docx_file:
                # Convert the .docx content to HTML with custom styling
                custom_styles = get_custom_style_map()
                result = mammoth.convert_to_html(
                    docx_file,
                    style_map=custom_styles,
                    transform_document=lambda element: element
                )
                html_content = result.value
        except Exception as e:
            html_content = f"<p>Error loading blog content: {str(e)}</p>"
    else:
        html_content = "<p>No content available for this blog post.</p>"

    return render(request, 'VRO_App1/blog-content.html', {
        'blog': blog,
        'mammoth_content': html_content
    })

def google_login_callback(request):
    try:
        request.session.flush()
        
        social_login = SocialLogin.deserialize(request.session.get('socialaccount_sociallogin'))
        user = social_login.user
        
        logger.info(f"Google login callback for user: {user.email}")
        logger.info(f"User profile exists: {hasattr(user, 'userprofile')}")
        if hasattr(user, 'userprofile'):
            logger.info(f"User group: {user.userprofile.group}")
        
        if not user.id:
            user.save()
            logger.info(f"Saved new user with id: {user.id}")
            
        # Force session creation
        if not request.session.session_key:
            request.session.create()
        
        # Log the user in
        login(request, user, backend='django.contrib.auth.backends.ModelBackend')
        
        # Debug logging
        logger.info(f"User authenticated: {request.user.is_authenticated}")
        logger.info(f"Session key after login: {request.session.session_key}")
        
        # Ensure session is saved
        request.session.save()
        
        if hasattr(user, 'userprofile') and user.userprofile.group:
            if user.userprofile.group.code == 'site-admins':
                logger.info("Redirecting to site admin home")
                return redirect('VRO_App3:site_admin_home')
        
        return redirect('VRO_App1:index')
            
    except Exception as e:
        logger.error(f"Google login error: {str(e)}")
        return redirect('VRO_App1:please_login')

def debug_user_status(request):
    if not request.user.is_authenticated:
        return JsonResponse({
            'authenticated': False
        })
    
    return JsonResponse({
        'authenticated': True,
        'email': request.user.email,
        'has_profile': hasattr(request.user, 'userprofile'),
        'group': request.user.userprofile.group.code if (hasattr(request.user, 'userprofile') and request.user.userprofile.group) else None
    })

def debug_auth_state(request):
    from django.http import JsonResponse
    return JsonResponse({
        'is_authenticated': request.user.is_authenticated,
        'user_email': request.user.email if request.user.is_authenticated else None,
        'has_profile': hasattr(request.user, 'userprofile') if request.user.is_authenticated else None,
        'group': request.user.userprofile.group.code if (request.user.is_authenticated and hasattr(request.user, 'userprofile') and request.user.userprofile.group) else None,
        'session_key': request.session.session_key,
        'session_data': dict(request.session.items()),
    })

def debug_full_state(request):
    output = []
    output.append(f"Path: {request.path}")
    output.append(f"Is Authenticated: {request.user.is_authenticated}")
    output.append(f"Session Key: {request.session.session_key}")
    output.append(f"User ID: {request.user.id if request.user.is_authenticated else 'None'}")
    output.append(f"User Email: {request.user.email if request.user.is_authenticated else 'None'}")
    
    if request.user.is_authenticated:
        output.append(f"Has Profile: {hasattr(request.user, 'userprofile')}")
        if hasattr(request.user, 'userprofile'):
            output.append(f"Group: {request.user.userprofile.group.code if request.user.userprofile.group else 'No Group'}")
    
    output.append("\nSession Data:")
    for key, value in request.session.items():
        output.append(f"{key}: {value}")
        
    output.append("\nCookies:")
    for key, value in request.COOKIES.items():
        output.append(f"{key}: {value}")

    return HttpResponse("<br>".join(output), content_type="text/plain")













