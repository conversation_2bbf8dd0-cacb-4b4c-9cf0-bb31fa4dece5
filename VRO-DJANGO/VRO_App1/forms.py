from django import forms
from .models import Contact, Document

class ContactForm(forms.ModelForm):
    class Meta:
        model = Contact
        fields = ['first_name', 'last_name', 'organisation', 'subject', 'email', 'message']
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500'
            }),
            'organisation': forms.TextInput(attrs={
                'class': 'w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500'
            }),
            'subject': forms.TextInput(attrs={
                'class': 'w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500'
            }),
            'message': forms.Textarea(attrs={
                'class': 'w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500',
                'rows': '6'  # This will make the textarea 6 rows tall
            }),
        }
        
    def clean_message(self):
        message = self.cleaned_data.get('message')
        if len(message) < 30:
            raise forms.ValidationError("Your message must be at least 30 characters long.")
        return message

    def clean_organisation(self):
        organisation = self.cleaned_data.get('organisation')
        # Return the original value instead of converting empty to 'N/A'
        return organisation if organisation else ''

class MagicLinkForm(forms.Form):
    email = forms.EmailField(
        widget=forms.EmailInput(
            attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'Enter your email'
            }
        )
    )

class DocumentUploadForm(forms.ModelForm):
    class Meta:
        model = Document
        fields = ['title', 'category', 'file', 'description', 'is_published', 'publication_date']
        widgets = {
            'publication_date': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
        }

    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            if not file.name.endswith('.docx'):
                raise forms.ValidationError('Only .docx files are allowed.')
            if file.size > 10 * 1024 * 1024:  # 10MB limit
                raise forms.ValidationError('File size cannot exceed 10MB.')
        return file
