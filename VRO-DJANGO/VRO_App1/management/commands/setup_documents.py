from django.core.management.base import BaseCommand
from VRO_App1.models import Document, DocumentCategory
from django.utils.text import slugify

class Command(BaseCommand):
    help = 'Sets up document categories and publishes documents'

    def handle(self, *args, **options):
        # Create categories with proper slugs
        articles_cat, _ = DocumentCategory.objects.get_or_create(
            name='Articles',
            defaults={
                'is_public': True,
                'slug': slugify('Articles')
            }
        )
        blogs_cat, _ = DocumentCategory.objects.get_or_create(
            name='Blogs',
            defaults={
                'is_public': True,
                'slug': slugify('Blogs')
            }
        )
        
        # Update all documents to be published
        Document.objects.all().update(is_published=True)
        
        # List of blog titles
        blog_titles = [
            'The Magnocellular Pathway',
            'Visual Reading Works with Children',
            'Changes to the DSA',
            'A Review of Visual Reading 2023'
        ]
        
        # Move matching documents to Blogs category
        for title in blog_titles:
            Document.objects.filter(title__icontains=title).update(
                category=blogs_cat,
                is_published=True
            )
        
        # Keep remaining documents in Articles category
        Document.objects.filter(category__isnull=True).update(
            category=articles_cat,
            is_published=True
        )
        
        # Print results
        self.stdout.write(self.style.SUCCESS(f'Articles: {Document.objects.filter(category=articles_cat).count()}'))
        self.stdout.write(self.style.SUCCESS(f'Blogs: {Document.objects.filter(category=blogs_cat).count()}'))
