from django.core.management.base import BaseCommand
from django.conf import settings
from django.core.files import File
from VRO_App1.models import Document, DocumentCategory
from pathlib import Path

class Command(BaseCommand):
    help = 'Import existing documents from static/documents directory'

    def handle(self, *args, **options):
        base_path = Path(settings.BASE_DIR) / 'static' / 'documents'
        
        if not base_path.exists():
            self.stdout.write(
                self.style.ERROR(f'Base directory not found: {base_path}')
            )
            self.stdout.write(
                self.style.WARNING('Creating directory structure...')
            )
            # Create the directory structure
            (base_path / 'articles').mkdir(parents=True, exist_ok=True)
            (base_path / 'blog').mkdir(parents=True, exist_ok=True)
            self.stdout.write(
                self.style.SUCCESS('Directory structure created at:')
            )
            self.stdout.write(f'  {base_path}/articles')
            self.stdout.write(f'  {base_path}/blog')
            return

        # Define categories and their directories
        categories = {
            'Articles': base_path / 'articles',
            'Blogs': base_path / 'blog'
        }

        for category_name, category_path in categories.items():
            if not category_path.exists():
                self.stdout.write(
                    self.style.WARNING(f'Creating directory: {category_path}')
                )
                category_path.mkdir(parents=True, exist_ok=True)
                continue

            # Get or create category
            category, created = DocumentCategory.objects.get_or_create(
                name=category_name
            )
            
            # Process documents in category
            docs_found = False
            for doc_path in category_path.glob('*.docx'):
                docs_found = True
                relative_path = doc_path.relative_to(Path(settings.BASE_DIR) / 'static')
                
                # Check if document already exists
                if not Document.objects.filter(file_path=str(relative_path)).exists():
                    with open(doc_path, 'rb') as f:
                        # Generate title from filename
                        title = doc_path.stem.replace('-', ' ').replace('_', ' ').title()
                        
                        document = Document(
                            title=title,
                            category=category,
                            original_filename=doc_path.name,
                            file_path=str(relative_path),
                            is_published=True  # Set default to published
                        )
                        document.file.save(doc_path.name, File(f), save=False)
                        document.save()
                        
                    self.stdout.write(
                        self.style.SUCCESS(f'Imported: {doc_path.name}')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Skipped existing: {doc_path.name}')
                    )
            
            if not docs_found:
                self.stdout.write(
                    self.style.WARNING(f'No .docx files found in {category_path}')
                )
