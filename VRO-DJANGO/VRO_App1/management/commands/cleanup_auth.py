from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from allauth.socialaccount.models import SocialAccount
from allauth.account.models import EmailAddress

User = get_user_model()

class Command(BaseCommand):
    help = 'Cleanup authentication related data'

    def handle(self, *args, **kwargs):
        # Get the email from the problematic social account
        social_accounts = SocialAccount.objects.filter(provider='google')
        for account in social_accounts:
            try:
                user = account.user
                self.stdout.write(f"Found social account for user: {user.email}")
            except User.DoesNotExist:
                self.stdout.write(f"Deleting orphaned social account: {account.id}")
                account.delete()

        # Clean up any duplicate email addresses
        emails = EmailAddress.objects.all()
        for email in emails:
            try:
                user = email.user
                self.stdout.write(f"Found email for user: {user.email}")
            except User.DoesNotExist:
                self.stdout.write(f"Deleting orphaned email: {email.email}")
                email.delete()