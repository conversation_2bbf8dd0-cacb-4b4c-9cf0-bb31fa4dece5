from django.core.management.base import BaseCommand
from django.contrib.sites.models import Site
from allauth.socialaccount.models import SocialApp

class Command(BaseCommand):
    help = 'Sets up social authentication providers'

    def handle(self, *args, **kwargs):
        # Create or get the site
        site, _ = Site.objects.get_or_create(
            domain='visualreadingonline.com',
            defaults={'name': 'Visual Reading Online'}
        )

        # Create or update Google provider
        google_app, created = SocialApp.objects.get_or_create(
            provider='google',
            name='Google OAuth',
            defaults={
                'client_id': '************-5r51hbu6gqkl5ps9ona179ub023d2lsn.apps.googleusercontent.com',
                'secret': '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',
            }
        )

        # Update the values even if the app already exists
        if not created:
            google_app.client_id = '************-5r51hbu6gqkl5ps9ona179ub023d2lsn.apps.googleusercontent.com'
            google_app.secret = '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'
            google_app.save()

        # Make sure the site is associated with the app
        google_app.sites.add(site)

        self.stdout.write(
            self.style.SUCCESS('Successfully set up social providers')
        )

