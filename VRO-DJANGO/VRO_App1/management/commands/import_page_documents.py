from django.core.management.base import BaseCommand
from django.conf import settings
from django.core.files import File
from django.utils.text import slugify
from VRO_App1.models import Document, DocumentCategory
from pathlib import Path
import os

class Command(BaseCommand):
    help = 'Import page documents from static/documents/pages directory'

    def handle(self, *args, **options):
        base_path = Path(settings.BASE_DIR) / 'static' / 'documents' / 'pages'
        
        self.stdout.write(f"Looking for documents in: {base_path}")
        
        if not base_path.exists():
            base_path.mkdir(parents=True, exist_ok=True)
            self.stdout.write(
                self.style.SUCCESS(f'Created directory: {base_path}')
            )
            return

        # Get or create the Pages category with a proper slug
        category, created = DocumentCategory.objects.get_or_create(
            name='Pages',
            defaults={
                'slug': slugify('Pages'),
                'is_public': True
            }
        )
        self.stdout.write(f"Using category: Pages (created: {created})")
        
        # Define the pages to import
        pages = {
            'cookie-policy.docx': 'Cookie Policy',
            'dr-cooper.docx': 'About Dr. Cooper',
            'faqs.docx': 'Frequently Asked Questions',
            'our-results.docx': 'Our Results',
            'student-feedback.docx': 'Student Feedback',
            'useful-links.docx': 'Useful Links',
            'legal-statement.docx': 'Legal Statement',
            'terms-of-service.docx': 'Terms of Service',
            'privacy-policy.docx': 'Privacy Policy',
        }
        
        # List all files in the directory
        self.stdout.write("Files found in directory:")
        for file in os.listdir(base_path):
            self.stdout.write(f"- {file}")
        
        for filename, title in pages.items():
            doc_path = base_path / filename
            self.stdout.write(f"\nProcessing {filename}...")
            
            if doc_path.exists():
                with open(doc_path, 'rb') as f:
                    url_name = filename.replace('.docx', '')
                    
                    # Check if document already exists
                    existing_doc = Document.objects.filter(url_name=url_name).first()
                    if existing_doc:
                        # Update existing document
                        existing_doc.title = title
                        existing_doc.category = category
                        existing_doc.is_published = True
                        existing_doc.file.save(filename, File(f), save=True)
                        self.stdout.write(
                            self.style.SUCCESS(f'Updated existing document: {filename}')
                        )
                    else:
                        # Create new document
                        document = Document(
                            title=title,
                            url_name=url_name,
                            category=category,
                            is_published=True
                        )
                        document.file.save(filename, File(f), save=True)
                        self.stdout.write(
                            self.style.SUCCESS(f'Created new document: {filename}')
                        )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Missing file: {filename}')
                )
        
        # Print final status
        all_docs = Document.objects.filter(category=category)
        self.stdout.write("\nFinal status - Documents in database:")
        for doc in all_docs:
            self.stdout.write(f"- {doc.url_name} (published: {doc.is_published})")
