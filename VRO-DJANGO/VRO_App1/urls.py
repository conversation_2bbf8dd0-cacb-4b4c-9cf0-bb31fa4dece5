from django.urls import path
from . import views

app_name = 'VRO_App1'

urlpatterns = [
    path('', views.index, name='index'),
    path('about/', views.about_view, name='about'),
    path('dr-cooper/', views.dr_cooper_view, name='dr_cooper'),
    path('learn-more/', views.learn_more_view, name='learn_more'),
    path('get-started/', views.get_started_view, name='get_started'),
    path('articles/', views.articles_index_view, name='articles'),
    path('blog-posts/', views.blog_posts_index_view, name='blog_posts'),
    path('our-results/', views.our_results_view, name='our_results'),
    path('our-products-and-services/', views.our_products_and_services_view, name='our_products_and_services'),
    path('products/', views.products_view, name='products'),
    path('social-media/', views.social_media_view, name='social_media'),
    path('student-feedback/', views.student_feedback_view, name='student_feedback'),
    path('useful-links/', views.useful_links_view, name='useful_links'),
    path('youtube/', views.youtube_view, name='youtube'),
    path('pay-per-view/', views.pay_per_view, name='pay_per_view'),
    path('presentations/', views.presentations, name='presentations'),
    
    # Articles with consistent naming
    path('article/science/', views.article_how_science_can_become_view, name='article_science'),
    path('article/labels/', views.article_how_many_labels_view, name='article_labels'),
    path('article/development-impact/', views.article_the_development_and_impact_view, name='article_development_impact'),
    path('article/social-mechanics/', views.article_the_social_mechanics_view, name='article_social_mechanics'),
    path('article/dyslexia/', views.article_why_dyslexia_is_not_view, name='article_dyslexia'),
    path('article/first-110/', views.article_the_first_110_visual_readers_view, name='article_first_110'),
    path('article/weekly-progress/', views.article_weekly_progress_of_view, name='article_weekly_progress'),
    
    # Get Started sections
    path('get-started/private-students/', 
         views.get_started_private_students, 
         name='get_started_private_students'),
    
    path('get-started/private-consultation/', 
         views.get_started_private_consultation_view,  # Updated to match view function name
         name='get_started_private_consultation'),
    
    path('get-started/gift-a-visual-reading-course/', 
         views.get_started_gift_a_visual_reading_course, 
         name='get_started_gift_a_visual_reading_course'),
    
    path('get-started/organisations/', 
         views.get_started_organisations, 
         name='get_started_organisations'),
    
    path('get-started/coaches/', 
         views.get_started_coaches_view,  # Make sure this matches your view function name
         name='get_started_coaches'),
    
    path('get-started/dsa-students/', 
         views.get_started_dsa_students_view,  # Make sure this matches your view function name
         name='get_started_dsa_students'),
    
    path('video-dsa-students/', views.video_dsa_students_view, name='video_dsa_students'),
    
    # Blog URLs
    path('blog/magnocellular-pathway/', views.blog_the_magnocellular_pathway_view, name='blog_magnocellular_pathway'),
    path('blog/visual-reading-children/', views.blog_visual_reading_works_with_children_view, name='blog_visual_reading_children'),
    path('blog/dsa-changes/', views.blog_changes_to_the_dsa_view, name='blog_dsa_changes'),
    path('blog/2023-review/', views.blog_a_review_of_visual_reading_2023_view, name='blog_2023_review'),
    
    # Legal and Support
    path('legal-statement/', views.legal_statement, name='legal_statement'),
    path('terms-of-service/', views.terms_of_service, name='terms_of_service'),
    path('privacy-policy/', views.privacy_policy, name='privacy_policy'),
    path('support/', views.support_view, name='support'),  # Make sure we're using support_view
    path('contact/', views.contact, name='contact'),
    path('contact/success/', views.contact_success, name='contact_success'),
    path('cookie-policy/', views.cookie_policy, name='cookie_policy'),
    
    # Authentication URLs
    path('please-login/', views.please_login_view, name='please_login'),
    path('logged-out/', views.logged_out_view, name='logged_out'),
    path('logout/', views.logout_view, name='logout'),
    path('signup/', views.signup_view, name='signup'),
    path('signup/confirmation/', views.signup_confirmation, name='signup_confirmation'),
    path('signup/resend-verification/', views.resend_verification, name='resend_verification'),
    path('verify-email/<str:uidb64>/<str:token>/', views.verify_email, name='verify_email'),
    path('login/', views.login_view, name='login'),
    path('word-test/', views.word_test_view, name='word_test'),
    path('blog-demo/', views.blog_demo_view, name='blog_demo'),
    path('resources/articles-and-blogs/', views.articles_and_blogs_view, name='articles_and_blogs'),
    path('resources/articles/', views.articles_index_view, name='articles'),
    path('resources/blog-posts/', views.blog_posts_index_view, name='blog_posts'),
    path('faqs/', views.faqs_view, name='faqs'),
    path('magic-link/', views.request_magic_link, name='request_magic_link'),
    path('magic-link/<uidb64>/<token>/', views.magic_link_login, name='magic_link_login'),
    path('check-session/', views.check_session, name='check_session'),
    path('articles/', views.articles_index_view, name='articles'),
    path('article/<str:article_name>/', views.article_view, name='article'),
    path('blogs/', views.blog_list, name='blog_list'),
    path('blogs/<slug:slug>/', views.blog_detail, name='blog_detail'),
    path('upload/', views.upload_document, name='upload_document'),
    # Blogs
    path('blog-posts/', views.blog_posts_index_view, name='blog_posts'),
    path('blog/<str:blog_name>/', views.blog_view, name='blog'),
    path('debug/user-status/', views.debug_user_status, name='debug_user_status'),
    path('debug-auth/', views.debug_auth_state, name='debug_auth'),
    path('debug-full/', views.debug_full_state, name='debug_full'),
]



