from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db import models
from django.utils import timezone  # For default timestamps
from django.utils.html import escape
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from pathlib import Path
import os
from django.utils.text import slugify

class CustomUserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        return self.create_user(email, password, **extra_fields)

class User(AbstractUser):
    username = None
    email = models.EmailField(_('email address'), unique=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []
    
    objects = CustomUserManager()

class Profile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)

class Contact(models.Model):
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    organisation = models.CharField(max_length=100, blank=True, default='')
    subject = models.CharField(max_length=200)
    email = models.EmailField()
    message = models.TextField()
    submission_time = models.DateTimeField(auto_now_add=True)
    mail_read = models.DateTimeField(null=True, blank=True)
    have_replied = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        self.message = escape(self.message)
        if not self.organisation:
            self.organisation = ''
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.subject}"

class UserLoginActivity(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='login_activities')
    login_datetime = models.DateTimeField(auto_now_add=True)
    logout_datetime = models.DateTimeField(null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.CharField(max_length=255, null=True, blank=True)
    session_key = models.CharField(max_length=40, null=True, blank=True)  # To track specific sessions
    is_active_session = models.BooleanField(default=True)  # To easily query active sessions
    
    class Meta:
        ordering = ['-login_datetime']
        indexes = [
            models.Index(fields=['user', 'is_active_session']),
            models.Index(fields=['session_key']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.login_datetime}"

    def end_session(self):
        self.logout_datetime = timezone.now()
        self.is_active_session = False
        self.save()

class DocumentCategory(models.Model):
    name = models.CharField(max_length=255)
    slug = models.SlugField(unique=True)
    is_public = models.BooleanField(default=True)
    
    class Meta:
        verbose_name_plural = "Document Categories"
        ordering = ['name']

    def __str__(self):
        return self.name

class Document(models.Model):
    title = models.CharField(max_length=254)
    url_name = models.SlugField(max_length=254, unique=True, blank=True, db_index=True)
    category = models.ForeignKey('DocumentCategory', on_delete=models.PROTECT)
    file = models.FileField(upload_to='documents/')
    file_path = models.CharField(max_length=255)  # Increased from 50 to 255
    original_filename = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    author = models.CharField(max_length=100, blank=True)  # New field
    posted_date = models.DateField(null=True, blank=True)  # New field
    is_published = models.BooleanField(default=False)
    publication_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_documents'
    )
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='updated_documents'
    )

    class Meta:
        ordering = ['-publication_date', '-created_at']
        indexes = [
            models.Index(fields=['category', '-publication_date']),
            models.Index(fields=['url_name']),
            models.Index(fields=['is_published']),
        ]

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        # Only generate url_name for new documents or if title changed and url_name is empty
        if not self.url_name or (not self.id and not self.url_name):
            base_slug = slugify(self.title)
            url_name = base_slug
            counter = 1
            
            # Check if the slug exists and generate a unique one if needed
            while Document.objects.filter(url_name=url_name).exclude(id=self.id).exists():
                url_name = f"{base_slug}-{counter}"
                counter += 1
            
            self.url_name = url_name

        if not self.title and self.original_filename:
            self.title = os.path.splitext(self.original_filename)[0].replace('-', ' ').replace('_', ' ').title()
            
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        if self.category.slug == 'articles':
            return reverse('VRO_App1:article', kwargs={'article_name': self.url_name})
        elif self.category.slug == 'blogs':
            return reverse('VRO_App1:blog', kwargs={'blog_name': self.url_name})
        return ''

class Blog(models.Model):
    title = models.CharField(max_length=200)
    slug = models.SlugField(unique=True, blank=True)
    content = models.TextField()
    document = models.FileField(upload_to='blogs/', null=True, blank=True)
    publication_date = models.DateTimeField(auto_now_add=True)
    last_modified = models.DateTimeField(auto_now=True)
    is_published = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['-publication_date']


