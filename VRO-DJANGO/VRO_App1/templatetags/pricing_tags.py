from django import template
from VRO_App0.models import PricingCard

register = template.Library()

@register.inclusion_tag('VRO_App1/components/pricing_card.html')
def render_pricing_card(card_data):
    # If we're passed a PricingCard instance, use it directly
    if isinstance(card_data, PricingCard):
        return {'card': card_data}
    
    # If we're passed a dictionary, use it directly
    if isinstance(card_data, dict):
        return {'card': card_data}
    
    # Otherwise, treat it as a card title and look it up
    try:
        card = PricingCard.objects.get(Card_Title__icontains=card_data)
        return {'card': card}
    except PricingCard.DoesNotExist:
        return {'card': None}

@register.filter
def format_price(price):
    if price == 0:
        return 'TBA'
    return f'£{price}'

@register.simple_tag
def get_stripe_link(course_type):
    try:
        card = PricingCard.objects.get(Card_Title__icontains=course_type)
        return card.Card_Button_Link
    except PricingCard.DoesNotExist:
        return None

@register.simple_tag
def get_consultation_cards():
    try:
        # Get both consultation and coaching session cards, ordered by duration/price
        cards = PricingCard.objects.filter(
            Card_Title__iregex=r'(15 Min|30 Min|1 Hr|2 Hr)'
        ).filter(
            Card_Title__iregex=r'(Consultation|Coaching Session)'
        ).exclude(
            Card_Title__icontains='Gift'
        ).order_by('Price')
        return cards
    except Exception as e:
        print(f"Error in get_consultation_cards: {e}")  # Add debugging
        return []

@register.simple_tag
def get_individual_course_cards():
    try:
        return PricingCard.objects.filter(Card_Title__icontains='Visual Reading Online')
    except Exception:
        return []

@register.simple_tag
def get_distance_learning_cards():
    try:
        return PricingCard.objects.filter(Card_Title__icontains='Self Study')
    except Exception:
        return []

@register.simple_tag
def get_coach_cards():
    try:
        return PricingCard.objects.filter(Card_Title__icontains='Coaching The Coaches')
    except Exception:
        return []




