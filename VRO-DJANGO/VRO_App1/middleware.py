import logging
from django.shortcuts import redirect
from django.urls import reverse

logger = logging.getLogger(__name__)

class UserActivityMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Debug logging
        if request.user.is_authenticated:
            logger.debug(f"User authenticated: {request.user.email}")
            logger.debug(f"Session key: {request.session.session_key}")
            logger.debug(f"Path accessed: {request.path}")

        # List of public paths that don't require authentication
        public_paths = [
            '/please-login/',
            '/logged-out/',
            '/signup/',
            '/contact/',
            '/about/',
            '/',
            '/accounts/google/',
            '/accounts/google/login/callback/',
            '/accounts/social/',
            '/accounts/social/signup/',
            '/static/',
            '/media/',
            '/check-session/',
            '/admin/',
            '/accounts/',
            '/favicon.ico',
            '/robots.txt',
        ]

        # Check if the current path is public
        is_public_path = any(request.path.startswith(path) for path in public_paths)

        # Debug logging for authentication check
        if not is_public_path and not request.user.is_authenticated:
            logger.warning(f"Unauthenticated access to protected path: {request.path}")
            logger.warning(f"Session key at redirect: {request.session.session_key}")
            return redirect('VRO_App1:please_login')

        response = self.get_response(request)
        return response









