import mammoth
from pathlib import Path
from bs4 import BeautifulSoup

def load_word_document(doc_path, request):
    """
    Load and convert a Word document to HTML.
    Returns a tuple of (content, error).
    """
    try:
        with open(doc_path, 'rb') as docx_file:
            result = mammoth.convert_to_html(docx_file)
            return result.value, None
    except FileNotFoundError:
        return None, f"Document not found: {doc_path.name}"
    except Exception as e:
        return None, f"Error loading document: {str(e)}"

def transform_html_urls(html_content, request=None):
    """Transform local URLs to work with any domain"""
    soup = BeautifulSoup(html_content, 'html.parser')
    
    for link in soup.find_all('a', href=True):
        href = link['href']
        if href.startswith(('http://127.0.0.1:', 'http://localhost:')):
            path = href.split('/', 3)[-1] if len(href.split('/', 3)) > 3 else ''
            
            if request:
                new_url = request.build_absolute_uri(f'/{path}')
            else:
                new_url = f'/{path}'
                
            link['href'] = new_url
    
    return str(soup)
