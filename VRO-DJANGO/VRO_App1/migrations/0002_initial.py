# Generated by Django 4.2.9 on 2025-03-22 13:53

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("VRO_App1", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="document",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_documents",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="document",
            name="updated_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="updated_documents",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="userloginactivity",
            index=models.Index(
                fields=["user", "is_active_session"], name="VRO_App1_us_user_id_5b03ce_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="userloginactivity",
            index=models.Index(fields=["session_key"], name="VRO_App1_us_session_68a4c7_idx"),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(
                fields=["category", "-updated_at"], name="VRO_App1_do_categor_59d966_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(fields=["file_path"], name="VRO_App1_do_file_pa_3041f8_idx"),
        ),
    ]
