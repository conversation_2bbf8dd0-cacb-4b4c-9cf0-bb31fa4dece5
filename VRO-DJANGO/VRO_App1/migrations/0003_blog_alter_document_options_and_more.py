# Generated by Django 4.2.9 on 2025-03-22 21:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("VRO_App1", "0002_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Blog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("slug", models.SlugField(blank=True, unique=True)),
                ("content", models.TextField()),
                ("document", models.FileField(blank=True, null=True, upload_to="blogs/")),
                ("publication_date", models.DateTimeField(auto_now_add=True)),
                ("last_modified", models.DateTimeField(auto_now=True)),
                ("is_published", models.BooleanField(default=False)),
            ],
            options={
                "ordering": ["-publication_date"],
            },
        ),
        migrations.AlterModelOptions(
            name="document",
            options={"ordering": ["-publication_date", "-created_at"]},
        ),
        migrations.AlterModelOptions(
            name="documentcategory",
            options={"ordering": ["name"], "verbose_name_plural": "Document Categories"},
        ),
        migrations.RemoveIndex(
            model_name="document",
            name="VRO_App1_do_categor_59d966_idx",
        ),
        migrations.RemoveIndex(
            model_name="document",
            name="VRO_App1_do_file_pa_3041f8_idx",
        ),
        migrations.RemoveField(
            model_name="documentcategory",
            name="description",
        ),
        migrations.AddField(
            model_name="document",
            name="description",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="document",
            name="is_published",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="document",
            name="publication_date",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="document",
            name="slug",
            field=models.SlugField(blank=True, unique=True),
        ),
        migrations.AddField(
            model_name="documentcategory",
            name="is_public",
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name="documentcategory",
            name="name",
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name="documentcategory",
            name="slug",
            field=models.SlugField(unique=True),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(
                fields=["category", "-publication_date"], name="VRO_App1_do_categor_54ccd8_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(fields=["slug"], name="VRO_App1_do_slug_bc64b0_idx"),
        ),
        migrations.AddIndex(
            model_name="document",
            index=models.Index(fields=["is_published"], name="VRO_App1_do_is_publ_b22a16_idx"),
        ),
    ]
