# Python base stage
FROM python:3.13-slim AS python-base
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100

# Python builder stage
FROM python-base AS builder
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    gcc \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*
WORKDIR /app
COPY requirements.txt .
RUN pip wheel --no-cache-dir --no-deps --wheel-dir /wheels -r requirements.txt

# Production stage
FROM python-base AS production
WORKDIR /app

# Install Node.js and npm
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    curl \
    && curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y --no-install-recommends \
    nodejs \
    && rm -rf /var/lib/apt/lists/* \
    && npm install -g npm@latest

# Set NPM path environment variable
ENV NPM_BIN_PATH=/usr/bin/npm

# Install Python packages
COPY --from=builder /wheels /wheels
COPY requirements.txt .
RUN pip install --no-cache-dir /wheels/* \
    && rm -rf /wheels \
    && rm requirements.txt

# Create necessary directories
RUN mkdir -p /app/logs /app/media /app/staticfiles

# Copy project files
COPY . .

# Install Tailwind dependencies and build CSS
RUN cd tailwindcss/static_src && npm install
RUN python manage.py tailwind install
RUN python manage.py tailwind build

# Set up entrypoint
COPY docker/entrypoint.prod.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
CMD ["gunicorn", "VRO_Project.wsgi:application", "--bind", "0.0.0.0:8000"]


