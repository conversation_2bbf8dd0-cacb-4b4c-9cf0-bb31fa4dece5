services:
  web:
    container_name: ${VRO_Project}-dev
    build:
      context: .
      target: development
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - /app/node_modules
    ports:
      - "8000:8000"
    networks:
      - app_network
    environment:
      - DOCKER_BUILDKIT=1
      - DEBUG=1

volumes:
  static_volume:
  media_volume:

networks:
  app_network:
    driver: bridge
