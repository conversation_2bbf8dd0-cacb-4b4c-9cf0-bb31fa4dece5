#!/bin/bash

# Set environment variables
export ADDR="**********:8000"
export GO_ENV="development"
export SESSION_SECRET="your-secret-key-here"

# Change to the Buffalo application directory
cd visualreadingonline

# Build the application
echo "Building the Buffalo application..."
go build -v -tags development -o tmp/visualreadingonline-build ./cmd/app

# Run the application with the correct address
echo "Starting the Buffalo application on $ADDR..."
cd tmp
./visualreadingonline-build
