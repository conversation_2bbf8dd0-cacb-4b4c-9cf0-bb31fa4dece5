#!/bin/bash

# Build and run the Docker container
echo "Building and starting the GOVROv1 container..."

# Navigate to the project directory
cd "$(dirname "$0")"

# Run go mod tidy first to ensure dependencies are correct
echo "Updating Go dependencies..."
cd visualreadingonline
go mod tidy
cd ..

# Build and start the container using docker-compose
echo "Building and starting Docker container..."
docker-compose up -d --build

# Check if the container is running
if [ "$(docker ps -q -f name=GOVROv1)" ]; then
    echo "Container GOVROv1 is now running on **********:8000"
    echo "You can check the logs with: docker logs GOVROv1"
    echo "You can stop the container with: docker-compose down"
else
    echo "Error: Container failed to start. Checking logs..."
    docker-compose logs
    echo "You can try to fix the issues and run this script again."
fi
