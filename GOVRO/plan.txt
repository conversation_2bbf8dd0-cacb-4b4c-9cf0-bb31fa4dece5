# Visual Reading Online - Buffalo Project Plan

## Overview
This plan outlines the steps to create a new Go Buffalo project for visualreadingonline.com using Tailwind CSS 3.1.8, Alpine.js, and HTMX, and converting Django templates to Buffalo's Plush templates.

## Step 1: Create a new Buffalo project
- Create a new Buffalo project with minimal dependencies
- <PERSON><PERSON> Bootstrap and other default frontend libraries
- Use the `--skip-pop` and `--skip-yarn` flags to minimize dependencies

```bash
buffalo new visualreadingonline --skip-pop --skip-yarn
```

## Step 2: Set up the basic template structure
- Create a base template (application.plush.html)
- Create a header partial (_header.plush.html)
- Create a footer partial (_footer.plush.html)
- Create a home template (home/index.plush.html)
- Ensure the templates are properly linked together

## Step 3: Install and configure Tailwind CSS 3.1.8
- Install Tailwind CSS 3.1.8 and its dependencies
```bash
npm install --save tailwindcss@3.1.8 postcss@8.4.14 autoprefixer@10.4.7
```

- Create and configure tailwind.config.js
```bash
npx tailwindcss init
```

- Update postcss.config.js to include Tailwind CSS
- Add Tailwind directives to application.scss
- Configure Tailwind to scan Buffalo templates

## Step 4: Add Alpine.js and HTMX
- Install Alpine.js and HTMX
```bash
npm install --save alpinejs htmx.org
```

- Import Alpine.js and HTMX in application.js
- Configure them to be available in templates
- Add necessary initialization code

## Step 5: Verify everything works
- Create a simple demo page with Tailwind CSS, Alpine.js, and HTMX
- Run the Buffalo server to test the setup
- Verify that all components are working correctly
- Test responsive design and interactivity

## Step 6: Translate Django templates
- Copy Django templates to a reference directory
- Analyze the Django templates to understand their structure
- Convert Django template syntax to Buffalo's Plush syntax
- Adapt CSS classes from Django to Tailwind CSS
- Ensure all links and paths are correctly formatted for Buffalo
- Test each converted template

## Step 7: Implement additional features
- Set up routes for all pages
- Create controllers for dynamic content
- Implement any backend functionality needed
- Add form handling and validation
- Set up error pages

## Step 8: Final testing and deployment
- Test all pages and features
- Optimize assets for production
- Prepare for deployment
- Document the project structure and setup

## Reference
- Django templates are located in the old-django directory
- Buffalo documentation: https://gobuffalo.io/documentation/
- Tailwind CSS documentation: https://v3.tailwindcss.com/docs
- Alpine.js documentation: https://alpinejs.dev/
- HTMX documentation: https://htmx.org/docs/
