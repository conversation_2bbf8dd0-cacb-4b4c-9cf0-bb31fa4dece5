package main

import (
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
)

func main() {
	// Set environment variables for the Buffalo application
	os.Setenv("ADDR", "127.0.0.1:3000")
	os.Setenv("GO_ENV", "development")
	os.Setenv("SESSION_SECRET", "your-secret-key-here")

	// Start the Buffalo application in a separate goroutine
	go func() {
		cmd := exec.Command("./visualreadingonline/tmp/visualreadingonline-build")
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
		err := cmd.Run()
		if err != nil {
			log.Fatalf("Failed to start Buffalo application: %v", err)
		}
	}()

	// Create a reverse proxy to forward requests to the Buffalo application
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		// Forward the request to the Buffalo application
		resp, err := http.Get("http://127.0.0.1:3000" + r.URL.Path)
		if err != nil {
			http.Error(w, "Failed to connect to Buffalo application", http.StatusInternalServerError)
			return
		}
		defer resp.Body.Close()

		// Copy the response headers
		for k, v := range resp.Header {
			for _, vv := range v {
				w.Header().Add(k, vv)
			}
		}

		// Copy the status code
		w.WriteHeader(resp.StatusCode)

		// Copy the response body
		buf := make([]byte, 4096)
		for {
			n, err := resp.Body.Read(buf)
			if n > 0 {
				w.Write(buf[:n])
			}
			if err != nil {
				break
			}
		}
	})

	// Start the HTTP server
	log.Println("Starting server on 10.10.10.8:8000...")
	err := http.ListenAndServe("10.10.10.8:8000", nil)
	if err != nil {
		log.Fatal(err)
	}
}
