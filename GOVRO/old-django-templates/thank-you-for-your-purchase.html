{% extends 'base.html' %}
{% load static %}
{% block title %}Learn More - Visual Reading Online{% endblock %}

{% block content %}

<div class="max-w-screen-xl px-0 py-0 mx-auto sm:py-0 lg:py-0">
  <div class="p-0 mt-0 mb-8 md:p-0">
    <!-- support intro -->
    <div class="py-6 sm:py-8 lg:py-8">
      <div class="max-w-screen-xl px-0 mx-auto lg:px-0">
        <div class="grid gap-8 xl:grid-cols-2 xl:gap-12">
          <div>
            <div class="h-64 overflow-hidden rounded-lg  md:h-auto">
              <img
                src="{% static 'images/thank-you-for-your-purchase.jpg' %}"
                loading="lazy"
                alt="Log In To Our Website"
                class="object-cover object-center w-full h-full" />
            </div>
          </div>

          <div class="md:pt-2">
            <h2
              class="mb-2 text-3xl font-extrabold leading-none tracking-tight text-left md:max-w-full lg:mb-4 md:text-4xl xl:text-4xl">
              Thank you for your purchase!
            </h2>

            <p class="my-6 text-blue-900 text-md md:text-lg">
              All our products and services are managed via a user account on
              our website.
            </p>

            <p class="my-6 text-blue-900 text-md md:text-lg">
              You should receive a welcom email from us to the email address
              that you just entered within the Stripe payment screen shortly.
            </p>

            <p class="my-6 text-blue-900 text-md md:text-lg">
              Our email will contain a link that you should use to set your
              Visual Reading Online website user account password. Once this is
              done, you will then be able to access your website account and
              manage your services.
            </p>

            <p class="my-6 text-blue-900 text-md md:text-lg">
              If your purchase requires the delivery of our physical Structured
              Saccade Overlays, please check and confirm your delivery address
              details and tick the box to confirm that we can go ahead and send
              these out to you.
            </p>
            <p class="my-6 text-blue-900 text-md md:text-lg">
              You can also set your prefered schedule for your coach-led
              coaching or consultation sessions and also manage any other
              aspects of your purchase from us from within your user account.
            </p>

            <p class="my-6 text-blue-900 text-md md:text-lg">
              If you purchased a Distance Learning course, you will be able to
              access the course material once you confirm you have received our
              physical Structured Saccade Overlays as these are needed for you
              to begin the course correctly.
            </p>

            <p class="my-6 text-blue-900 text-md md:text-lg">
              Our digital Saccade software can be downloaded once you have
              completed the first session of our coach-led or distance learning
              course, and this is to ensure that you receive some guidance
              before starting to use them.
            </p>
            <p class="my-6 text-blue-900 text-md md:text-lg">
              Once again thank you for your purchase, with a little time and
              effort you will soon improve your reading speed tremendously!
            </p>
            <p class="my-6 font-bold text-blue-900 text-md md:text-lg">
              * Please note, there may be a delay in us sending you our welcome
              email but you will receive this in the next few hours and we are
              working to remove this delay and fully automate our service
              (31/01/24).
            </p>
          </div>
        </div>
      </div>
    </div>
    <!-- END OF support intro -->

    <script>
      const emailInput = document.getElementById("emailInput");
      const passwordInput = document.getElementById("passwordInput");
      const emailError = document.getElementById("emailError");
      const passwordError = document.getElementById("passwordError");
      const submitButton = document.getElementById("submitButton");

      submitButton.addEventListener("click", function (event) {
        event.preventDefault();

        const email = emailInput.value;
        const password = passwordInput.value;

        // Email validation using a simple pattern
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const isEmailValid = emailPattern.test(email);

        // Password validation
        const passwordPattern =
          /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[a-zA-Z\d]{8,}$/;
        const isPasswordValid = passwordPattern.test(password);

        // Display error messages if validation fails
        if (!isEmailValid) {
          emailError.classList.remove("hidden");
        } else {
          emailError.classList.add("hidden");
        }

        if (!isPasswordValid) {
          passwordError.classList.remove("hidden");
        } else {
          passwordError.classList.add("hidden");
        }

        // If both fields are valid, proceed with form submission
        if (isEmailValid && isPasswordValid) {
          // You can add code here to submit the form or perform other actions
          console.log("Form submitted successfully!");
        }
      });
    </script>

    {% endblock content %}
  </div>
</div>
