{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="max-w-screen-xl mx-auto px-4 py-8">
    <div class="border border-blue-900 rounded-lg p-6 bg-white">
        <h1 class="text-3xl font-bold text-blue-900 mb-8">Blog Posts</h1>
        
        <!-- Blog Posts List -->
        <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {% for blog in blogs %}
                <a href="{% url 'VRO_App1:blog' blog.url_name %}" class="block transition duration-300 transform hover:scale-105">
                    <div class="p-6 bg-white rounded-lg shadow hover:shadow-lg border border-blue-900 flex flex-col h-full">
                        <h2 class="mb-4 text-xl font-semibold text-blue-900 flex-grow">{{ blog.title }}</h2>
                        <div class="mt-auto pt-4 border-t border-gray-200">
                            {% if blog.author %}
                                <p class="text-sm text-gray-500">{{ blog.author }}</p>
                            {% endif %}
                            {% if blog.publication_date %}
                                <p class="text-xs text-gray-400">{{ blog.publication_date|date:"F j, Y" }}</p>
                            {% endif %}
                        </div>
                    </div>
                </a>
            {% empty %}
                <p>No blog posts found.</p>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}


