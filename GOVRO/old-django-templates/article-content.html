{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="py-8">
    <div class="max-w-screen-xl px-4 mx-auto">
        <div class="p-8">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-blue-900 mb-4">{{ article.title }}</h1>
                {% if article.publication_date %}
                    <p class="text-gray-600 mb-8">Published: {{ article.publication_date|date:"F j, Y" }}</p>
                {% endif %}
            </div>
            
            <div class="prose max-w-none">
                <style>
                    /* Center all images within mammoth content */
                    .prose img {
                        display: block;
                        margin-left: auto;
                        margin-right: auto;
                    }
                    /* Optional: add some spacing around images */
                    .prose img {
                        margin-top: 1.5rem;
                        margin-bottom: 1.5rem;
                    }
                    /* Center first heading and paragraph after title */
                    .prose h1:first-of-type,
                    .prose h2:first-of-type,
                    .prose h3:first-of-type,
                    .prose p:first-of-type {
                        text-align: center;
                    }
                </style>
                {{ mammoth_content|safe }}
            </div>
            
            <div class="mt-8 pt-4 border-t">
                <a href="{% url 'VRO_App1:articles' %}" class="text-blue-600 hover:text-blue-800">
                    ← Back to Articles
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
