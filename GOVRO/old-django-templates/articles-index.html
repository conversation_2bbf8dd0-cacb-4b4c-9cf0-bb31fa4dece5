{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="py-8">
    <div class="max-w-screen-xl px-4 mx-auto">
        <h1 class="mb-8 text-3xl font-bold text-blue-900 md:text-4xl">Articles</h1>
        
        <!-- Articles List -->
        <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {% for article in articles %}
                <a href="{% url 'VRO_App1:article' article.url_name %}" class="block transition duration-300 transform hover:scale-105">
                    <div class="p-6 bg-white rounded-lg shadow hover:shadow-lg border border-blue-900 flex flex-col h-full">
                        <h2 class="mb-4 text-xl font-semibold text-blue-900 flex-grow">{{ article.title }}</h2>
                        <div class="mt-auto pt-4 border-t border-gray-200">
                            {% if article.author %}
                                <p class="text-sm text-gray-500">{{ article.author }}</p>
                            {% endif %}
                            {% if article.publication_date %}
                                <p class="text-xs text-gray-400">{{ article.publication_date|date:"F j, Y" }}</p>
                            {% endif %}
                        </div>
                    </div>
                </a>
            {% empty %}
                <p>No articles found.</p>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock content %}


