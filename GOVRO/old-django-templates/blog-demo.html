{% extends 'base.html' %}
{% load static %}
{% block title %}Blog Demo - Visual Reading Online{% endblock %}

{% block content %}
<div style="display: flex; justify-content: center; align-items: center; width: 100%;">
  <div style="width: 100%; max-width: 600px; margin: 0px auto;">
    <!-- Content Container -->
    <div class="prose prose-lg text-blue-900 my-8">
      {{ mammoth_content|safe }}
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const content = document.querySelector('.prose');
    const links = content.getElementsByTagName('a');
    for (let link of links) {
      link.setAttribute('target', '_blank');
      link.setAttribute('rel', 'noopener noreferrer');
    }
  });
</script>
{% endblock %}