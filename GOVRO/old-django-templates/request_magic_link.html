{% extends "base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block content %}
<div class="max-w-screen-xl px-0 py-0 mx-auto sm:py-0 lg:py-0">
  <div class="p-0 mt-0 mb-8 md:p-0">
    <div class="py-4">
      <div class="max-w-screen-xl px-0 mx-auto lg:px-0">
        <div class="grid gap-8 xl:grid-cols-2 xl:gap-12">
          <!-- Image Section -->
          <div>
            <div class="h-64 overflow-hidden rounded-lg md:h-auto">
              <img
                src="{% static 'images/magic-link.jpg' %}"
                loading="lazy"
                alt="Magic Link"
                class="object-cover object-center w-full h-full" />
            </div>
          </div>

          <!-- Form Section -->
          <div class="flex mt-4">
            <div class="w-full">
              <!-- Header -->
              <div class="mb-8">
                <h1 class="text-3xl font-extrabold text-blue-900 sm:text-4xl">
                  Request Magic Link
                </h1>
                <p class="mt-4 text-lg text-blue-800">
                  Enter your email to receive a secure login link
                </p>
              </div>

              <!-- Messages -->
              {% if messages %}
              <div class="mb-6">
                {% for message in messages %}
                <div class="p-4 mb-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'info' %}bg-blue-100 text-blue-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% endif %}">
                  {{ message }}
                </div>
                {% endfor %}
              </div>
              {% endif %}

              <!-- Form Container -->
              <div class="bg-white rounded-lg shadow-md p-8 border border-blue-900">
                <form method="post">
                  {% csrf_token %}
                  {{ form|crispy }}
                  <button type="submit" 
                    class="w-full px-4 py-3 mt-6 text-sm font-bold text-white transition-colors duration-200 bg-blue-900 rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Send Magic Link
                  </button>
                </form>

                <div class="mt-6 text-center">
                  <p class="text-sm text-gray-600">
                    Note: If you signed up with Google, please use the Google Sign-In button instead.
                  </p>
                </div>
                
                <!-- Back Link -->
                <div class="text-center mt-4 border-2 border-blue-900 rounded-lg p-2 text-blue-900 hover:text-white hover:bg-blue-900 transition-colors duration-200">
                  <a href="{% url 'VRO_App1:login' %}" 
                     class="block w-full font-bold hover:text-white">
                    Back to Login
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
