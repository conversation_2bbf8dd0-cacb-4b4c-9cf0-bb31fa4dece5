{% load pricing_tags %}
<div class="relative flex flex-col h-full p-6 overflow-hidden text-white bg-blue-900 border-4 border-blue-900 rounded-lg {% if card.card_style == 'featured' %}border-yellow-400{% endif %}">
    <h2 class="mb-1 text-lg font-bold tracking-widest text-white title-font text-center">
        {{ card.Card_Title|default:"Visual Reading Online" }}
    </h2>
    <h1 class="pb-4 mb-4 text-5xl leading-none text-white border-b border-gray-200 text-center font-bold">
        {{ card.Price|format_price }}
    </h1>
    <ul class="mb-8 text-white">
        {% if card.Point1 %}<li class="mb-2">• {{ card.Point1 }}</li>{% endif %}
        {% if card.Point2 %}<li class="mb-2">• {{ card.Point2 }}</li>{% endif %}
        {% if card.Point3 %}<li class="mb-2">• {{ card.Point3 }}</li>{% endif %}
    </ul>
    {% if card.Card_Button_Link %}
    <a href="{{ card.Card_Button_Link }}" class="flex items-center justify-center w-full px-4 py-2 mt-auto text-black font-bold bg-yellow-400 border-0 rounded focus:outline-none hover:bg-yellow-500 transition-colors duration-200">
        {{ card.Card_Button_Text|default:"Learn More" }}
    </a>
    {% endif %}
</div>
