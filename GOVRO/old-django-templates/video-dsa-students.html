{% extends 'base.html' %}
{% load static %}

{% block title %}DSA Students Video - Visual Reading Online{% endblock %}

{% block extra_head %}
<style>
    /* Hide the overflow menu (three dots) */
    video::-webkit-media-controls-overflow-menu-button,
    video::-webkit-media-controls-overflow-button {
        display: none !important;
    }

    /* Hide download button in any browser */
    video::-internal-media-controls-download-button {
        display: none !important;
    }

    video::-webkit-media-controls-enclosure {
        overflow: hidden !important;
    }

    video::-webkit-media-controls-panel {
        width: calc(100% + 30px) !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-screen-xl px-2 mx-auto mt-4">
    <div class="w-full aspect-video">
        <video 
            id="dsa-video"
            class="w-full h-full rounded-lg"
            controls
            autoplay
            muted
            controlsList="nodownload noplaybackrate"
            disablePictureInPicture
            poster="{% static 'images/video-placeholder.jpg' %}"
        >
            <source src="{% static 'video/DSA-STUDENTS.mp4' %}" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const video = document.getElementById('dsa-video');
        
        // Prevent right-click on video
        video.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });

        // Unmute video after it starts playing
        video.addEventListener('playing', function() {
            video.muted = false;
        });
    });
</script>
{% endblock %}
