{% extends 'base.html' %}
{% load pricing_tags %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Manage Pricing Cards</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for card in pricing_cards %}
        <div class="border rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-2">{{ card.Card_Title }}</h3>
            <p class="text-gray-600 mb-4">{{ card.Price|format_price }}</p>
            <div class="space-y-2 mb-4">
                {% if card.Point1 %}<p>• {{ card.Point1 }}</p>{% endif %}
                {% if card.Point2 %}<p>• {{ card.Point2 }}</p>{% endif %}
                {% if card.Point3 %}<p>• {{ card.Point3 }}</p>{% endif %}
            </div>
            <div class="flex justify-end">
                <a href="{% url 'edit_pricing_card' card.Card_ID %}" 
                   class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Edit
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}
