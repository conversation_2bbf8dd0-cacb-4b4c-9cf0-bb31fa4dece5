{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load account %}
{% load socialaccount %}

{% block title %}Sign Up - Visual Reading Online{% endblock %}

{% block content %}
<div class="max-w-screen-xl px-0 py-0 mx-auto sm:py-0 lg:py-0">
  <div class="p-0 mt-0 mb-8 md:p-0">
    <div class="py-4">
      <div class="max-w-screen-xl px-0 mx-auto lg:px-0">
        <div class="grid gap-8 xl:grid-cols-2 xl:gap-12">
          <!-- Image Section -->
          <div>
            <div class="h-64 overflow-hidden rounded-lg md:h-auto">
              <img
                src="{% static 'images/start.png' %}"
                loading="lazy"
                alt="Sign Up"
                class="object-cover object-center w-full h-full" />
            </div>
          </div>

          <!-- Form Section -->
          <div class="md:pt-2">
            <div class="sm:mx-auto sm:w-full">
              <h2 class="mb-2 text-2xl font-extrabold leading-none tracking-tight text-left text-blue-900 md:max-w-full lg:mb-4 md:text-4xl xl:text-4xl">
                Join Visual Reading Online
              </h2>
              <p class="mb-6 text-blue-900 text-md md:text-lg">
                Create a free account to access* our full range of visual reading tools and resources.
              </p>
              <div class="px-4 py-8 bg-[#fefde8] shadow sm:rounded-lg sm:px-10 border border-blue-900">
                <!-- Email Signup Button -->
                <div class="space-y-6" id="initialView">
                  <button type="button" id="showEmailFormBtn"
                    class="w-full px-4 py-3 text-sm font-medium text-white transition-colors duration-200 bg-blue-900 rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Sign up with Your Email and a Password
                  </button>

                  <!-- Move the "Or continue with" and Google button into this div -->
                  <div id="alternativeSignup">
                    <div class="mt-6">
                      <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                          <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                          <span class="px-2 bg-[#fefde8] text-blue-800">Or continue with</span>
                        </div>
                      </div>

                      <!-- Google Sign Up Button -->
                      <div class="mt-6">
                        <a href="{% provider_login_url 'google' process='signup' %}"
                          class="w-full flex items-center justify-center px-4 py-2 border border-[#1034a6] rounded-md shadow-sm text-sm font-medium text-[#1034a6] bg-white hover:bg-gray-50 transition-colors duration-200">
                          <img src="{% static 'images/Google.jpg' %}" alt="Google" class="w-5 h-5 mr-2" />
                          Sign up with your Google account
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Email Signup Form -->
                <form method="post" id="emailForm" class="hidden mt-6 space-y-6">
                  {% csrf_token %}
                  
                  <div class="space-y-4">
                    <div class="relative">
                      <input type="email" name="email" id="id_email" 
                        class="form-input"
                        placeholder="Email address" required>
                    </div>
                    
                    <div class="relative">
                      <input type="password" name="password1" id="id_password1"
                        class="form-input"
                        placeholder="Password" required>
                    </div>

                    <!-- Password Help Link -->
                    <div class="text-center">
                      <h4 class="text-blue-900 text-sm font-medium">Password Requirements</h4>
                    </div>

                    <!-- Password Requirements Section -->
                    <div id="passwordRequirements" class="mt-4 p-4 bg-gray-50 rounded-md border border-blue-900">
                      <ul class="space-y-1 text-sm">
                        <li><span id="lengthCheck" class="text-red-500">✕</span> At least 9 characters long</li>
                        <li><span id="uppercaseCheck" class="text-red-500">✕</span> Contains uppercase letter</li>
                        <li><span id="lowercaseCheck" class="text-red-500">✕</span> Contains lowercase letter</li>
                        <li><span id="numberCheck" class="text-red-500">✕</span> Contains number</li>
                        <li><span id="symbolCheck" class="text-red-500">✕</span> Contains symbol (!@#$%^&*)</li>
                      </ul>
                    </div>

                    <div class="relative">
                      <input type="password" name="password2" id="id_password2"
                        class="form-input"
                        placeholder="Confirm Password" required>
                    </div>
                  </div>

                  <button type="submit" id="signupButton" disabled
                    class="w-full px-4 py-3 text-sm font-medium text-white transition-colors duration-200 bg-[#4a5568] rounded-md shadow-sm cursor-not-allowed"
                    style="background-color: #4a5568 !important;">
                    Becomes Active When Strong Password Set
                  </button>
                </form>

                <div class="mt-6 text-center">
                  <p class="text-sm text-gray-600">
                    Already have an account?
                    <a href="{% url 'VRO_App1:login' %}" class="font-medium text-[#1034a6] hover:text-blue-700">Log in here</a>
                  </p>
                </div>
              </div>
            </div>
            <p class="my-6 text-blue-900 text-md md:text-lg">
              * If you plan on purchasing a course or consulation session from us, please use the same email address as you will use to make the Stripe purchase, as we will be able to autmatically link your purchase to your account. 
            </p>
            <p class="my-6 text-blue-900 text-md md:text-lg">
              If you have already made a purchase via Stripe, you should have received an email within the email account linked with your Stripe purchase with a link to create your website account and set your password. 
            </p>
          </div>
          
        </div>
      </div>
    </div>
  </div>
</div>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const showEmailFormBtn = document.getElementById('showEmailFormBtn');
    const emailForm = document.getElementById('emailForm');
    const initialView = document.getElementById('initialView');
    const alternativeSignup = document.getElementById('alternativeSignup');

    showEmailFormBtn.addEventListener('click', function() {
      emailForm.classList.remove('hidden');
      alternativeSignup.classList.add('hidden');
      showEmailFormBtn.classList.add('hidden');
    });
  });
</script>
<script>
  function togglePasswordHelp() {
    const passwordReqs = document.getElementById('passwordRequirements');
    const alternativeSignup = document.getElementById('alternativeSignup');
    
    if (passwordReqs.style.display === 'none') {
      passwordReqs.style.display = 'block';
      if (alternativeSignup) {
        alternativeSignup.style.display = 'none';
      }
    } else {
      passwordReqs.style.display = 'none';
      if (alternativeSignup) {
        alternativeSignup.style.display = 'block';
      }
    }
  }
</script>
{% endblock %}
