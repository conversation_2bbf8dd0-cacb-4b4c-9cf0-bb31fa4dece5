#!/bin/bash
export ADDR="10.10.10.8:8000"
export GO_ENV="development"
export SESSION_SECRET="your-secret-key-here"
cd visualreadingonline

# Check if buffalo is in PATH
if command -v buffalo >/dev/null 2>&1; then
  buffalo dev
else
  # Try to use buffalo from go/bin
  if [ -f "$HOME/go/bin/buffalo" ]; then
    $HOME/go/bin/buffalo dev
  else
    echo "Buffalo not found. Please install it with: go install github.com/gobuffalo/cli/cmd/buffalo@latest"
    exit 1
  fi
fi
