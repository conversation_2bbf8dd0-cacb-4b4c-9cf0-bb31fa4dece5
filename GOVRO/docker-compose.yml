version: '3'

services:
  govrov1:
    container_name: GOVROv1
    build:
      context: ./visualreadingonline
      dockerfile: Dockerfile
    ports:
      - "10.10.10.8:8000:8000"
    environment:
      - GO_ENV=production
      - ADDR=0.0.0.0:8000
      - SESSION_SECRET=your-secret-key-here
      - POP_PATH=/app/visualreadingonline
      - NO_POP=0
    restart: always
    networks:
      - govro-network

networks:
  govro-network:
    driver: bridge
