#!/bin/bash

# Build and run the Docker container directly without docker-compose
echo "Building and starting the GOVROv1 container..."

# Navigate to the project directory
cd "$(dirname "$0")"

# Run go mod tidy first to ensure dependencies are correct
echo "Updating Go dependencies..."
cd visualreadingonline
go mod tidy
cd ..

# Build the Docker image
echo "Building Docker image..."
docker build -t govrov1 ./visualreadingonline

# Check if the build was successful
if [ $? -ne 0 ]; then
    echo "Error: Docker build failed. Please check the error messages above."
    exit 1
fi

# Stop and remove any existing container with the same name
echo "Stopping any existing container..."
docker stop GOVROv1 2>/dev/null || true
docker rm GOVROv1 2>/dev/null || true

# Run the container
echo "Starting container..."
docker run -d \
  --name GOVROv1 \
  -p **********:8000:8000 \
  -e GO_ENV=production \
  -e ADDR=0.0.0.0:8000 \
  -e SESSION_SECRET=your-secret-key-here \
  -e POP_PATH=/app/visualreadingonline \
  -e NO_POP=0 \
  --restart always \
  govrov1

# Check if the container is running
if [ "$(docker ps -q -f name=GOVROv1)" ]; then
    echo "Container GOVROv1 is now running on **********:8000"
    echo "You can check the logs with: docker logs GOVROv1"
    echo "You can stop the container with: docker stop GOVROv1"
else
    echo "Error: Container failed to start. Checking logs..."
    docker logs GOVROv1
    echo "You can try to fix the issues and run this script again."
fi
