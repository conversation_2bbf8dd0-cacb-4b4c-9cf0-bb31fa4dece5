package services

import (
	"bytes"
	"context"
	"fmt"
	"html/template"
	"time"
	"visualreadingonline/models"

	"github.com/gobuffalo/envy"
	"github.com/gofrs/uuid"
	"github.com/mailersend/mailersend-go"
	"github.com/pkg/errors"
)

// EmailService handles sending emails
type EmailService struct {
	MailerSendClient *mailersend.Mailersend
	SiteURL          string
	LogoURL          string
	FromEmail        string
	FromName         string
	Templates        map[string]*template.Template
}

// NewEmailService creates a new email service
func NewEmailService() *EmailService {
	apiToken := envy.Get("MAILERSEND_API_TOKEN", "mlsn.b36d527d2edf140cdc9b5d3b3ba1cf97babbd49b5d0eb084ce4183c5dc5e0a07")
	siteURL := envy.Get("SITE_URL", "http://localhost:8000")
	logoURL := fmt.Sprintf("%s/assets/images/vro-heading.png", siteURL)
	fromEmail := envy.Get("FROM_EMAIL", "<EMAIL>")
	fromName := envy.Get("FROM_NAME", "Visual Reading Online")

	// Load email templates
	templates := make(map[string]*template.Template)

	// Base template
	baseTemplate := `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #1d3a8a;
            background-color: #fefde8; /* yellow-100 */
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fefde8; /* yellow-100 */
        }
        .header {
            text-align: center;
            padding: 20px 0;
            background-color: #1d3a8a;
            color: white;
        }
        .logo {
            max-width: 300px;
            height: auto;
        }
        .content {
            padding: 20px;
            background-color: #fefde8; /* yellow-100 */
            color: #1d3a8a;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            background-color: #1d3a8a;
            font-size: 12px;
            color: white;
        }
        .button {
            display: inline-block;
            background-color: #1d3a8a;
            color: white !important;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            margin: 20px 0;
        }
        a {
            color: #1d3a8a;
        }
        .footer a {
            color: white;
        }
        .tracking-pixel {
            display: block;
            width: 1px;
            height: 1px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{.LogoURL}}" alt="Visual Reading Online" class="logo">
        </div>

        <div class="content">
            {{.Content}}
        </div>

        <div class="footer">
            <p>&copy; {{.Year}} Visual Reading Online. All rights reserved.</p>
            <p>
                <a href="{{.SiteURL}}/terms-of-service">Terms of Service</a> |
                <a href="{{.SiteURL}}/privacy-policy">Privacy Policy</a>
            </p>
        </div>

        <!-- Tracking pixel -->
        <img src="{{.TrackingURL}}" alt="" class="tracking-pixel">
    </div>
</body>
</html>`

	// Parse templates
	templates["magic_link"] = template.Must(template.New("magic_link").Parse(baseTemplate))
	templates["welcome"] = template.Must(template.New("welcome").Parse(baseTemplate))
	templates["password_reset"] = template.Must(template.New("password_reset").Parse(baseTemplate))

	return &EmailService{
		MailerSendClient: mailersend.NewMailersend(apiToken),
		SiteURL:          siteURL,
		LogoURL:          logoURL,
		FromEmail:        fromEmail,
		FromName:         fromName,
		Templates:        templates,
	}
}

// SendEmail sends an email using the specified template
func (s *EmailService) SendEmail(to string, templateName string, data map[string]interface{}) error {
	// Generate a tracking ID
	trackingID := uuid.Must(uuid.NewV4()).String()

	// Create tracking URL
	trackingURL := fmt.Sprintf("%s/email-tracking/%s", s.SiteURL, trackingID)

	// Add common data
	data["SiteURL"] = s.SiteURL
	data["LogoURL"] = s.LogoURL
	data["TrackingURL"] = trackingURL
	data["Year"] = time.Now().Year()

	// Render the email template
	htmlContent, err := s.renderTemplate(templateName, data)
	if err != nil {
		return errors.WithStack(err)
	}

	// Create plain text version - strip HTML tags for plain text
	textContent := fmt.Sprintf("Welcome to Visual Reading Online. Please view this email in an HTML email client or visit: %s", s.SiteURL)

	// Create recipients
	recipients := []mailersend.Recipient{
		{
			Email: to,
		},
	}

	// Create the email message
	message := s.MailerSendClient.Email.NewMessage()

	// Set from
	from := mailersend.From{
		Email: s.FromEmail,
		Name:  s.FromName,
	}
	message.SetFrom(from)

	message.SetRecipients(recipients)
	message.SetSubject(data["Title"].(string))

	// Set HTML content with proper Content-Type header
	message.SetHTML(htmlContent)

	// Set plain text version
	message.SetText(textContent)

	// We'll skip setting additional headers as it's not supported in this version

	// Send the email
	ctx := context.Background()
	_, err = s.MailerSendClient.Email.Send(ctx, message)
	if err != nil {
		return errors.WithStack(err)
	}

	// Store the tracking information in the database
	err = s.storeEmailTracking(to, templateName, trackingID)
	if err != nil {
		// Log the error but don't fail the email send
		fmt.Printf("Error storing email tracking: %v\n", err)
	}

	return nil
}

// renderTemplate renders an email template with the provided data
func (s *EmailService) renderTemplate(templateName string, data map[string]interface{}) (string, error) {
	// Create a buffer to store the rendered template
	var buf bytes.Buffer

	// Get the template
	tmpl, ok := s.Templates[templateName]
	if !ok {
		return "", errors.New("template not found: " + templateName)
	}

	// Add content to data
	var contentBuf bytes.Buffer
	switch templateName {
	case "magic_link":
		contentBuf.WriteString(`
<h1>Your Magic Link</h1>

<p>Hello,</p>

<p>You requested a magic link to log in to Visual Reading Online. Click the button below to log in:</p>

<p style="text-align: center;">
    <a href="`)
		contentBuf.WriteString(data["MagicLinkURL"].(string))
		contentBuf.WriteString(`" class="button">Log In Now</a>
</p>

<p>If you didn't request this link, you can safely ignore this email.</p>

<p>This link will expire in 15 minutes.</p>

<p>Best wishes,</p>

<p>Ross</p>

<p><strong>Dr Ross Cooper</strong><br>
***********<br>
"If I were not dyslexic, I would not be me."</p>`)
	case "welcome":
		contentBuf.WriteString(`
<h1>Welcome to Visual Reading Online!</h1>

<p>Hello`)
		if firstName, ok := data["FirstName"]; ok && firstName != "" {
			contentBuf.WriteString(" ")
			contentBuf.WriteString(firstName.(string))
		}
		contentBuf.WriteString(`</p>

<p>Thank you for creating an account with Visual Reading Online. We're excited to have you join our community!</p>

<p>With your account, you can:</p>
<ul>
    <li>Access our visual reading courses</li>
    <li>Track your progress</li>
    <li>Connect with other learners</li>
    <li>Get personalized recommendations</li>
</ul>

<p style="text-align: center;">
    <a href="`)
		contentBuf.WriteString(data["SiteURL"].(string))
		contentBuf.WriteString(`/dashboard" class="button">Go to Your Dashboard</a>
</p>

<p>If you have any questions or need assistance, please don't hesitate to contact <NAME_EMAIL>.</p>

<p>Best wishes,</p>

<p>Ross</p>

<p><strong>Dr Ross Cooper</strong><br>
***********<br>
"If I were not dyslexic, I would not be me."</p>`)
	case "password_reset":
		contentBuf.WriteString(`
<h1>Reset Your Password</h1>

<p>Hello,</p>

<p>We received a request to reset your password for your Visual Reading Online account. Click the button below to reset your password:</p>

<p style="text-align: center;">
    <a href="`)
		contentBuf.WriteString(data["ResetURL"].(string))
		contentBuf.WriteString(`" class="button">Reset Password</a>
</p>

<p>If you didn't request a password reset, you can safely ignore this email.</p>

<p>This link will expire in 15 minutes.</p>

<p>Best wishes,</p>

<p>Ross</p>

<p><strong>Dr Ross Cooper</strong><br>
***********<br>
"If I were not dyslexic, I would not be me."</p>`)
	}

	data["Content"] = contentBuf.String()

	// Render the template
	err := tmpl.Execute(&buf, data)
	if err != nil {
		return "", errors.WithStack(err)
	}

	return buf.String(), nil
}

// storeEmailTracking stores email tracking information in the database
func (s *EmailService) storeEmailTracking(to string, templateName string, trackingID string) error {
	// Create a new email tracking record
	emailTracking := &models.EmailTracking{
		TrackingID:   trackingID,
		Email:        to,
		TemplateName: templateName,
		SentAt:       time.Now(),
	}

	// Store in the database
	// This would typically use a database connection, but for now we'll just log it
	fmt.Printf("Email tracking: %s, %s, %s, %v\n", trackingID, to, templateName, emailTracking.SentAt)

	return nil
}

// SendWelcomeEmail sends a welcome email to a new user
func (s *EmailService) SendWelcomeEmail(user *models.User) error {
	data := map[string]interface{}{
		"Title":     "Welcome to Visual Reading Online",
		"FirstName": user.FirstName,
	}

	return s.SendEmail(user.Email, "welcome", data)
}

// SendMagicLinkEmail sends a magic link email to a user
func (s *EmailService) SendMagicLinkEmail(user *models.User, token string) error {
	magicLinkURL := fmt.Sprintf("%s/auth/magic-link/%s", s.SiteURL, token)

	data := map[string]interface{}{
		"Title":        "Magic Link for Visual Reading Online",
		"MagicLinkURL": magicLinkURL,
	}

	return s.SendEmail(user.Email, "magic_link", data)
}

// SendPasswordResetEmail sends a password reset email to a user
func (s *EmailService) SendPasswordResetEmail(user *models.User, token string) error {
	resetURL := fmt.Sprintf("%s/reset-password/%s", s.SiteURL, token)

	data := map[string]interface{}{
		"Title":    "Reset Your Password - Visual Reading Online",
		"ResetURL": resetURL,
	}

	return s.SendEmail(user.Email, "password_reset", data)
}
