<div class="max-w-screen-xl px-0 py-0 mx-auto sm:py-0 lg:py-0">
  <div class="p-0 mt-0 mb-8 md:p-0">
    <div class="py-4">
      <div class="max-w-screen-xl px-0 mx-auto lg:px-0">
        <div class="grid gap-8 xl:grid-cols-2 xl:gap-12">
          <!-- Image Section -->
          <div>
            <div class="h-64 overflow-hidden rounded-lg md:h-auto">
              <img
                src="<%= assetPath("images/magic-link.jpg") %>"
                loading="lazy"
                alt="Magic Link"
                class="object-cover object-center w-full h-full" />
            </div>
          </div>

          <!-- Form Section -->
          <div class="flex mt-4">
            <div class="w-full">
              <!-- Header -->
              <div class="mb-8">
                <h1 class="text-3xl font-extrabold text-blue-900 sm:text-4xl">
                  Request Magic Link
                </h1>
                <p class="mt-4 text-lg text-blue-800">
                  Enter your email to receive a secure login link
                </p>
              </div>

              <!-- Messages -->
              <!-- Flash messages will be implemented later -->

              <!-- Form Container -->
              <div class="bg-white rounded-lg shadow-md p-8 border border-blue-900">
                <form method="post" action="/request-magic-link">
                  <input name="authenticity_token" type="hidden" value="<%= authenticity_token %>" />

                  <div class="space-y-4">
                    <div class="relative">
                      <input type="email" name="email" id="email" required
                        class="w-full px-4 py-3 border border-blue-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-900 focus:border-blue-900 shadow-sm placeholder-gray-400"
                        placeholder="Email address">
                    </div>
                  </div>

                  <button type="submit"
                    class="w-full px-4 py-3 mt-6 text-sm font-bold text-white transition-colors duration-200 bg-blue-900 rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Send Magic Link
                  </button>
                </form>

                <div class="mt-6 text-center">
                  <p class="text-sm text-gray-600">
                    Note: If you signed up with Google, please use the Google Sign-In button instead.
                  </p>
                </div>

                <!-- Back Link -->
                <div class="text-center mt-4 border-2 border-blue-900 rounded-lg p-2 text-blue-900 hover:text-white hover:bg-blue-900 transition-colors duration-200">
                  <a href="/login"
                     class="block w-full font-bold hover:text-white">
                    Back to Login
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
