
<div class="max-w-screen-xl px-0 py-0 mx-auto sm:py-0 lg:py-0">
  <div class="p-0 mt-0 mb-8 md:p-0">
    <div class="py-4">
      <div class="max-w-screen-xl px-0 mx-auto lg:px-0">
        <div class="grid gap-8 xl:grid-cols-2 xl:gap-12">
          <!-- Image Section -->
          <div>
            <div class="h-64 overflow-hidden rounded-lg md:h-auto">
              <img
                src="<%= assetPath("images/start.png") %>"
                loading="lazy"
                alt="Sign Up"
                class="object-cover object-center w-full h-full" />
            </div>
          </div>

          <!-- Form Section -->
          <div class="md:pt-2">
            <div class="sm:mx-auto sm:w-full">
              <h2 class="mb-2 text-2xl font-extrabold leading-none tracking-tight text-left text-blue-900 md:max-w-full lg:mb-4 md:text-4xl xl:text-4xl">
                Join Visual Reading Online
              </h2>
              <p class="mb-6 text-blue-900 text-md md:text-lg">
                Create a free account to access* our full range of visual reading tools and resources.
              </p>
              <div class="px-4 py-8 bg-[#fefde8] shadow sm:rounded-lg sm:px-10 border border-blue-900">
                <!-- Email Signup Button -->
                <div class="space-y-6" id="initialView">
                  <button type="button" id="showEmailFormBtn"
                    class="w-full px-4 py-3 text-sm font-medium text-white transition-colors duration-200 bg-blue-900 rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Sign up with Your Email and a Password
                  </button>

                  <!-- Empty alternativeSignup div - content moved below -->
                  <div id="alternativeSignup"></div>
                </div>

                <!-- Email Signup Form -->
                <form method="post" action="/signup" id="emailForm" class="hidden mt-6 space-y-6">
                  <input name="authenticity_token" type="hidden" value="<%= authenticity_token %>" />

                  <div class="space-y-4">
                    <div class="relative">
                      <input type="email" name="email" id="id_email"
                        class="w-full px-4 py-3 border border-blue-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-900 focus:border-blue-900 shadow-sm placeholder-gray-400"
                        placeholder="Email address" required>
                    </div>

                    <div class="relative">
                      <input type="password" name="password1" id="id_password1"
                        class="w-full px-4 py-3 border border-blue-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-900 focus:border-blue-900 shadow-sm placeholder-gray-400"
                        placeholder="Password" required
                        onfocus="document.getElementById('passwordRequirements').classList.remove('hidden');"
                        onblur="if(!this.value) { document.getElementById('passwordRequirements').classList.add('hidden'); }">
                      <p class="mt-1 text-xs text-gray-500">Click to see password requirements</p>
                    </div>

                    <!-- Password Help Link -->
                    <div class="text-center">
                      <h4 class="text-blue-900 text-sm font-medium">Password Requirements</h4>
                    </div>

                    <!-- Password Requirements Section -->
                    <div id="passwordRequirements" class="mt-4 p-4 bg-gray-50 rounded-md border border-blue-900 hidden">
                      <ul class="space-y-1 text-sm">
                        <li><span id="lengthCheck" class="text-red-500">✕</span> At least 9 characters long</li>
                        <li><span id="uppercaseCheck" class="text-red-500">✕</span> Contains uppercase letter</li>
                        <li><span id="lowercaseCheck" class="text-red-500">✕</span> Contains lowercase letter</li>
                        <li><span id="numberCheck" class="text-red-500">✕</span> Contains number</li>
                        <li><span id="symbolCheck" class="text-red-500">✕</span> Contains symbol (!@#$%^&*)</li>
                      </ul>
                    </div>

                    <div class="relative">
                      <input type="password" name="password2" id="id_password2"
                        class="w-full px-4 py-3 border border-red-500 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 shadow-sm placeholder-gray-400"
                        placeholder="Confirm Password" required>
                    </div>
                  </div>

                  <button type="submit" id="signupButton" disabled
                    class="w-full px-4 py-3 text-sm font-medium text-white transition-colors duration-200 bg-[#4a5568] rounded-md shadow-sm cursor-not-allowed"
                    style="background-color: #4a5568 !important;">
                    Sign Up Now
                  </button>
                </form>

                <div class="mt-6 text-center">
                  <p class="text-sm text-gray-600">
                    Already have an account?
                    <a href="/login" class="font-medium text-[#1034a6] hover:text-blue-700">Log in here</a>
                  </p>
                </div>

                <!-- Or continue with section -->
                <div class="mt-6">
                  <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                      <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                      <span class="px-2 bg-[#fefde8] text-blue-800">Or continue with</span>
                    </div>
                  </div>

                  <!-- Google Sign Up Button -->
                  <div class="mt-6">
                    <a href="/auth/google"
                      class="w-full flex items-center justify-center px-4 py-2 border border-[#1034a6] rounded-md shadow-sm text-sm font-medium text-[#1034a6] bg-white hover:bg-gray-50 transition-colors duration-200">
                      <img src="<%= assetPath("images/Google.jpg") %>" alt="Google" class="w-5 h-5 mr-2" />
                      Sign up with your Google account
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <p class="my-6 text-blue-900 text-md md:text-lg">
              * If you plan on purchasing a course or consulation session from us, please use the same email address as you will use to make the Stripe purchase, as we will be able to autmatically link your purchase to your account.
            </p>
            <p class="my-6 text-blue-900 text-md md:text-lg">
              If you have already made a purchase via Stripe, you should have received an email within the email account linked with your Stripe purchase with a link to create your website account and set your password.
            </p>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const showEmailFormBtn = document.getElementById('showEmailFormBtn');
    const emailForm = document.getElementById('emailForm');
    const initialView = document.getElementById('initialView');
    const alternativeSignup = document.getElementById('alternativeSignup');

    showEmailFormBtn.addEventListener('click', function() {
      emailForm.classList.remove('hidden');
      alternativeSignup.classList.add('hidden');
      showEmailFormBtn.classList.add('hidden');
    });
  });
</script>
<script>
  function togglePasswordHelp() {
    const passwordReqs = document.getElementById('passwordRequirements');

    if (passwordReqs.classList.contains('hidden')) {
      passwordReqs.classList.remove('hidden');
    } else {
      passwordReqs.classList.add('hidden');
    }
  }

  // Password requirement checks
  const checks = {
    length: { regex: /.{9,}/, element: document.getElementById('lengthCheck') },
    uppercase: { regex: /[A-Z]/, element: document.getElementById('uppercaseCheck') },
    lowercase: { regex: /[a-z]/, element: document.getElementById('lowercaseCheck') },
    number: { regex: /[0-9]/, element: document.getElementById('numberCheck') },
    symbol: { regex: /[!@#$%^&*]/, element: document.getElementById('symbolCheck') }
  };

  document.getElementById('id_password1').addEventListener('input', function(e) {
    const password = e.target.value;
    let allValid = true;

    // Check each requirement
    for (let check in checks) {
      const isValid = checks[check].regex.test(password);
      checks[check].element.textContent = isValid ? '✓' : '✕';
      checks[check].element.className = isValid ? 'text-green-500' : 'text-red-500';
      if (!isValid) allValid = false;
    }

    // Enable/disable signup button based on password validity
    const confirmPassword = document.getElementById('id_password2').value;
    const signupButton = document.getElementById('signupButton');
    const password1Field = document.getElementById('id_password1');
    const password2Field = document.getElementById('id_password2');

    // Update password field styling
    if (allValid && password.length > 0) {
      password1Field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
      password1Field.classList.add('border-green-500', 'focus:ring-green-500', 'focus:border-green-500');
    } else {
      password1Field.classList.remove('border-green-500', 'focus:ring-green-500', 'focus:border-green-500');
      password1Field.classList.add('border-blue-900', 'focus:ring-blue-900', 'focus:border-blue-900');
    }

    // Update confirm password field styling
    if (password.length > 0) {
      if (password === confirmPassword && confirmPassword.length > 0) {
        password2Field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
        password2Field.classList.add('border-green-500', 'focus:ring-green-500', 'focus:border-green-500');
      } else {
        password2Field.classList.remove('border-green-500', 'focus:ring-green-500', 'focus:border-green-500');
        password2Field.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
      }
    }

    // Update button state
    if (allValid && password === confirmPassword && password.length > 0) {
      signupButton.disabled = false;
      signupButton.classList.remove('bg-[#4a5568]', 'cursor-not-allowed');
      signupButton.classList.add('bg-blue-900', 'hover:bg-blue-700');
      signupButton.style.backgroundColor = '';
    } else {
      signupButton.disabled = true;
      signupButton.classList.add('bg-[#4a5568]', 'cursor-not-allowed');
      signupButton.classList.remove('bg-blue-900', 'hover:bg-blue-700');
      signupButton.style.backgroundColor = '#4a5568 !important';
    }
  });

  document.getElementById('id_password2').addEventListener('input', function(e) {
    const password = document.getElementById('id_password1').value;
    const confirmPassword = e.target.value;
    const signupButton = document.getElementById('signupButton');
    const password1Field = document.getElementById('id_password1');
    const password2Field = document.getElementById('id_password2');
    let allValid = true;

    // Check if all password requirements are met
    for (let check in checks) {
      if (!checks[check].regex.test(password)) {
        allValid = false;
        break;
      }
    }

    // Update confirm password field styling
    if (password === confirmPassword && confirmPassword.length > 0) {
      password2Field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
      password2Field.classList.add('border-green-500', 'focus:ring-green-500', 'focus:border-green-500');
    } else {
      password2Field.classList.remove('border-green-500', 'focus:ring-green-500', 'focus:border-green-500');
      password2Field.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    }

    // Update password field styling if all requirements are met
    if (allValid && password.length > 0) {
      password1Field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500', 'border-blue-900', 'focus:ring-blue-900', 'focus:border-blue-900');
      password1Field.classList.add('border-green-500', 'focus:ring-green-500', 'focus:border-green-500');
    }

    // Update button state
    if (allValid && password === confirmPassword && password.length > 0) {
      signupButton.disabled = false;
      signupButton.classList.remove('bg-[#4a5568]', 'cursor-not-allowed');
      signupButton.classList.add('bg-blue-900', 'hover:bg-blue-700');
      signupButton.style.backgroundColor = '';
    } else {
      signupButton.disabled = true;
      signupButton.classList.add('bg-[#4a5568]', 'cursor-not-allowed');
      signupButton.classList.remove('bg-blue-900', 'hover:bg-blue-700');
      signupButton.style.backgroundColor = '#4a5568 !important';
    }
  });
</script>
