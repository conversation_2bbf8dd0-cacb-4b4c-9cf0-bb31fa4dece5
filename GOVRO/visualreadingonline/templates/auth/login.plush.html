
<div class="max-w-screen-xl px-0 py-0 mx-auto sm:py-0 lg:py-0">
  <div class="p-0 mt-0 mb-8 md:p-0">
    <div class="py-4">
      <div class="max-w-screen-xl px-0 mx-auto lg:px-0">
        <div class="grid gap-8 xl:grid-cols-2 xl:gap-12">
          <!-- Image Section -->
          <div>
            <div class="h-64 overflow-hidden rounded-lg md:h-auto">
              <img
                src="<%= assetPath("images/log-in.jpg") %>"
                loading="lazy"
                alt="Log In"
                class="object-cover object-center w-full h-full" />
            </div>
          </div>

          <!-- Form Section -->
          <div class="md:pt-2">
            <div class="sm:mx-auto sm:w-full">
              <h2 class="mb-2 text-2xl font-extrabold leading-none tracking-tight text-left text-blue-900 md:max-w-full lg:mb-4 md:text-4xl xl:text-4xl">
                Welcome Back
              </h2>
              <p class="mb-6 text-blue-900 text-md md:text-lg">
                Log in to your account to access your personalised visual reading experience.
              </p>
              <div class="px-4 py-8 bg-[#fefde8] shadow sm:rounded-lg sm:px-10 border border-blue-900">
                <!-- Email Login Form -->
                <form method="post" action="/login" class="space-y-6">
                  <input name="authenticity_token" type="hidden" value="<%= authenticity_token %>" />

                  <div class="space-y-4">
                    <div class="relative">
                      <input type="email" name="email" id="id_email"
                        class="w-full px-4 py-3 border border-blue-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-900 focus:border-blue-900 shadow-sm placeholder-gray-400"
                        placeholder="Email address" required>
                    </div>

                    <div class="relative">
                      <input type="password" name="password" id="id_password"
                        class="w-full px-4 py-3 border border-blue-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-900 focus:border-blue-900 shadow-sm placeholder-gray-400"
                        placeholder="Password" required>
                    </div>

                    <!-- Password Help Link -->
                    <div class="text-center">
                      <h4 class="text-[#1034a6] text-sm font-medium">Password Requirements</h4>
                    </div>

                    <!-- Password Requirements Section -->
                    <div id="passwordRequirements" class="mt-4 p-4 bg-gray-50 rounded-md border border-blue-900">
                      <ul class="space-y-1 text-sm">
                        <li><span id="lengthCheck" class="text-red-500">✕</span> At least 9 characters long</li>
                        <li><span id="uppercaseCheck" class="text-red-500">✕</span> Contains uppercase letter</li>
                        <li><span id="lowercaseCheck" class="text-red-500">✕</span> Contains lowercase letter</li>
                        <li><span id="numberCheck" class="text-red-500">✕</span> Contains number</li>
                        <li><span id="symbolCheck" class="text-red-500">✕</span> Contains symbol (!@#$%^&*)</li>
                      </ul>
                    </div>

                    <div class="flex items-center justify-between">
                      <button type="button" onclick="showForgotPasswordModal()"
                        class="font-medium text-[#1034a6] hover:text-blue-700">
                        Forgot your password?
                      </button>
                    </div>
                  </div>

                  <button type="submit"
                    class="w-full px-4 py-3 text-sm font-bold text-white transition-colors duration-200 bg-blue-900 rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Log In
                  </button>
                </form>

                <!-- Magic Link Login -->
                <div class="mt-6">
                  <a href="/request-magic-link"
                    class="w-full block text-center px-4 py-3 text-sm font-bold text-[#1034a6] transition-colors duration-200 border border-[#1034a6] rounded-md hover:bg-gray-50">
                    Log In with Magic Link
                  </a>
                </div>

                <!-- Alternative Login Section -->
                <div id="alternativeLogin">
                  <div class="mt-6">
                    <div class="relative">
                      <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                      </div>
                      <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-[#fefde8] text-blue-800">Or continue with</span>
                      </div>
                    </div>

                    <!-- Google Login Button -->
                    <div class="mt-6">
                      <a href="/auth/google"
                        class="w-full flex items-center justify-center px-4 py-2 border border-[#1034a6] rounded-md shadow-sm text-sm font-bold text-[#1034a6] bg-white hover:bg-gray-50 transition-colors duration-200">
                        <img src="<%= assetPath("images/Google.jpg") %>" alt="Google" class="w-5 h-5 mr-2" />
                        Log In with Google
                      </a>
                    </div>
                  </div>
                </div>

                <div class="mt-6 text-center">
                  <p class="text-sm text-gray-600">
                    Don't have an account?
                    <a href="/signup" class="font-medium text-[#1034a6] hover:text-blue-700">Join Us here</a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Forgot Password Modal -->
<div id="forgotPasswordModal" class="fixed inset-0 hidden z-50 overflow-y-auto">
  <!-- Modal overlay -->
  <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

  <!-- Modal container - adjusted for perfect centering -->
  <div class="fixed inset-0 z-10">
    <div class="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
      <div class="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-auto p-6 border-2 border-blue-900">
        <div class="text-center">
          <h3 class="text-lg leading-6 font-medium text-blue-900 mb-4">Reset Your Password</h3>
          <div class="mt-2 px-7 py-3">
            <p class="text-sm text-blue-900 mb-4">
              To reset your password, please:
            </p>
            <ol class="text-left text-sm text-blue-900 space-y-2">
              <li>1. Click "Log In with Magic Link"</li>
              <li>2. Enter your registered email address</li>
              <li>3. Click magic link sent to your email to log in</li>
              <li>4. Set new password within your profile</li>
            </ol>
            <p class="text-sm text-blue-900 mt-4 text-left">
              <strong>Note:</strong> If you signed up with Google, please reset your password through Google's account recovery process.
            </p>
            <p class="text-sm text-blue-900 mt-4 text-left">
              Your VisualReadingOnline services are tied to the email address you originally used or which was linked to the Stripe account you used to make a purchase from us.
            </p>
          </div>
          <div class="mt-5">
            <button id="closeModal"
              class="w-full px-4 py-2 bg-blue-900 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function showForgotPasswordModal() {
    document.getElementById('forgotPasswordModal').classList.remove('hidden');
  }

  document.getElementById('closeModal').addEventListener('click', function() {
    document.getElementById('forgotPasswordModal').classList.add('hidden');
  });

  // Close modal when clicking outside
  window.onclick = function(event) {
    const modal = document.getElementById('forgotPasswordModal');
    if (event.target == modal) {
      modal.classList.add('hidden');
    }
  }

  function togglePasswordHelp() {
    const passwordReqs = document.getElementById('passwordRequirements');
    const alternativeLogin = document.getElementById('alternativeLogin');

    if (passwordReqs.classList.contains('hidden')) {
      passwordReqs.classList.remove('hidden');
      alternativeLogin.classList.add('hidden');
    } else {
      passwordReqs.classList.add('hidden');
      alternativeLogin.classList.remove('hidden');
    }
  }

  // Password requirement checks
  const checks = {
    length: { regex: /.{9,}/, element: document.getElementById('lengthCheck') },
    uppercase: { regex: /[A-Z]/, element: document.getElementById('uppercaseCheck') },
    lowercase: { regex: /[a-z]/, element: document.getElementById('lowercaseCheck') },
    number: { regex: /[0-9]/, element: document.getElementById('numberCheck') },
    symbol: { regex: /[!@#$%^&*]/, element: document.getElementById('symbolCheck') }
  };

  document.getElementById('id_password').addEventListener('input', function(e) {
    const password = e.target.value;

    // Check each requirement
    for (let check in checks) {
      const isValid = checks[check].regex.test(password);
      checks[check].element.textContent = isValid ? '✓' : '✕';
      checks[check].element.className = isValid ? 'text-green-500' : 'text-red-500';
    }
  });
</script>


