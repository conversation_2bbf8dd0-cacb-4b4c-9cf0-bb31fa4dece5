<!-- HEADER AND MENU -->
<div class="mt-2 xl:mt-4 bg-blue-900 py-0.5 max-w-screen-xl mx-2 xl:mx-auto rounded">
  <img
    alt="Visual Reading Online Header"
    src="<%= assetPath("images/vro-heading.png") %>"
    class="mx-auto"
  />
</div>

<!-- Navigation wrapper with Alpine.js state management -->
<div x-data="{ mobileMenuOpen: false, resourcesDropdownOpen: false }">
  <!-- Desktop Menu (md and above) -->
  <div class="mx-2 mt-1">
    <div class="max-w-screen-xl px-0 mx-auto md:px-0">
      <!-- Main Navigation Bar -->
      <div class="hidden mx-auto mt-0 text-lg bg-blue-900 rounded-md text-yellow-50 xl:block p-0">
        <div class="flex justify-between items-center font-bold p-0 nav-container">
          <!-- Home -->
          <a href="/"
             class="nav-link <%= if (is_current_route(c, "index")) { %>nav-link-active<% } else { %>nav-link-inactive<% } %>">
            Home
          </a>

          <!-- Learn More -->
          <a href="/learn-more"
             class="nav-link <%= if (is_current_route(c, "learn_more")) { %>nav-link-active<% } else { %>nav-link-inactive<% } %>">
            Learn More
          </a>

          <!-- Get Started -->
          <a href="/get-started"
             class="nav-link <%= if (is_current_route(c, "get_started")) { %>nav-link-active<% } else { %>nav-link-inactive<% } %>">
            Get Started
          </a>

          <!-- Resources Dropdown -->
          <div class="relative">
            <a href="#"
              onclick="event.preventDefault(); document.getElementById('resources-dropdown').classList.toggle('hidden');"
              class="nav-link resources-button <%= if (contains(current_route_name(c), ["articles_and_blogs","articles","blog_posts","blog_demo","our_results","pay_per_view","presentations","our_products_and_services","social_media","student_feedback","useful_links","youtube","word_test","faqs","dr_cooper"])) { %>nav-link-active<% } else { %>nav-link-inactive<% } %>"
            >
              Resources
            </a>

            <!-- Dropdown menu -->
            <div
              id="resources-dropdown"
              class="absolute z-10 w-60 bg-blue-900 divide-y divide-blue-700 rounded-lg shadow-lg hidden"
            >
              <ul class="py-2 text-yellow-50">
                <li>
                  <a href="/dr-cooper"
                     class="dropdown-item <%= if (is_current_route(c, "dr_cooper")) { %>dropdown-item-active<% } %>">
                     About Dr. Cooper
                  </a>
                </li>
                <li>
                  <a href="/articles-and-blogs"
                     class="dropdown-item <%= if (contains(current_route_name(c), ["articles_and_blogs","articles","blog_posts"])) { %>dropdown-item-active<% } %>">
                     Articles & Blogs
                  </a>
                </li>
                <li>
                  <a href="/faqs"
                     class="dropdown-item <%= if (is_current_route(c, "faqs")) { %>dropdown-item-active<% } %>">
                     FAQs
                  </a>
                </li>
                <li>
                  <a href="/our-results"
                     class="dropdown-item <%= if (is_current_route(c, "our_results")) { %>dropdown-item-active<% } %>">
                     Our Results
                  </a>
                </li>
                <li>
                  <a href="/products"
                     class="dropdown-item <%= if (is_current_route(c, "products")) { %>dropdown-item-active<% } %>">
                     Products & Services
                  </a>
                </li>
                <li>
                  <a href="/student-feedback"
                     class="dropdown-item <%= if (is_current_route(c, "student_feedback")) { %>dropdown-item-active<% } %>">
                     Student Feedback
                  </a>
                </li>
                <li>
                  <a href="/useful-links"
                     class="dropdown-item <%= if (is_current_route(c, "useful_links")) { %>dropdown-item-active<% } %>">
                     Useful Links
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Support -->
          <a href="/support"
             class="nav-link <%= if (is_current_route(c, "support")) { %>nav-link-active<% } else { %>nav-link-inactive<% } %>">
            Support
          </a>

          <!-- Join Us -->
          <a href="/signup"
             class="nav-link <%= if (is_current_route(c, "signup")) { %>nav-link-active<% } else { %>nav-link-inactive<% } %>">
            Join Us
          </a>

          <!-- Log In -->
          <a href="/login"
             class="nav-link <%= if (is_current_route(c, "login")) { %>nav-link-active<% } else { %>nav-link-inactive<% } %>">
            Log In
          </a>
        </div>
      </div>

      <!-- Mobile Menu Button -->
      <div class="xl:hidden">
        <button
          onclick="event.preventDefault(); document.getElementById('mobile-menu').classList.toggle('hidden');"
          class="w-full p-3 mb-2 text-base font-bold text-yellow-50 bg-blue-900 rounded-md hover:bg-blue-700 mobile-menu-button"
        >
          Main Menu
        </button>
      </div>

      <!-- Mobile Menu -->
      <div
        id="mobile-menu"
        class="xl:hidden absolute z-50 w-full left-0 right-0 hidden"
      >
        <div class="mt-2 bg-blue-900 rounded-md mx-2">
          <nav class="flex flex-col">
            <!-- Mobile menu items -->
            <a href="/"
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 <%= if (is_current_route(c, "index")) { %>bg-blue-700<% } %>">
               Home
            </a>
            <a href="/learn-more"
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 <%= if (is_current_route(c, "learn_more")) { %>bg-blue-700<% } %>">
               Learn More
            </a>
            <a href="/get-started"
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 <%= if (is_current_route(c, "get_started")) { %>bg-blue-700<% } %>">
               Get Started
            </a>

            <!-- Mobile Resources Section -->
            <div x-data="{ mobileResourcesOpen: false }">
              <button
                onclick="event.preventDefault(); document.getElementById('mobile-resources-dropdown').classList.toggle('hidden');"
                class="flex items-center justify-between w-full p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 <%= if (contains(current_route_name(c), ["articles_and_blogs","articles","blog_posts","blog_demo","our_results","pay_per_view","presentations","our_products_and_services","social_media","student_feedback","useful_links","youtube","word_test"])) { %>bg-blue-700<% } %>"
              >
                <span>Resources</span>
                <svg
                  class="w-5 h-5 transition-transform mobile-resources-icon"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>

              <div id="mobile-resources-dropdown" class="bg-blue-800 hidden">
                <a href="/dr-cooper"
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 <%= if (is_current_route(c, "dr_cooper")) { %>bg-blue-700<% } %>">
                   About Dr. Cooper
                </a>
                <a href="/articles-and-blogs"
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 <%= if (contains(current_route_name(c), ["articles_and_blogs","articles","blog_posts"])) { %>bg-blue-700<% } %>">
                   Articles & Blogs
                </a>
                <a href="/faqs"
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 <%= if (is_current_route(c, "faqs")) { %>bg-blue-700<% } %>">
                   FAQs
                </a>
                <a href="/our-results"
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 <%= if (is_current_route(c, "our_results")) { %>bg-blue-700<% } %>">
                   Our Results
                </a>
                <a href="/products"
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 <%= if (is_current_route(c, "products")) { %>bg-blue-700<% } %>">
                   Products & Services
                </a>
                <a href="/student-feedback"
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 <%= if (is_current_route(c, "student_feedback")) { %>bg-blue-700<% } %>">
                   Student Feedback
                </a>
                <a href="/useful-links"
                   class="block p-4 pl-8 text-yellow-50 hover:bg-blue-700 <%= if (is_current_route(c, "useful_links")) { %>bg-blue-700<% } %>">
                   Useful Links
                </a>
              </div>
            </div>

            <a href="/support"
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 <%= if (is_current_route(c, "support")) { %>bg-blue-700<% } %>">
               Support
            </a>
            <a href="/signup"
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 <%= if (is_current_route(c, "signup")) { %>bg-blue-700<% } %>">
               Join Us
            </a>
            <a href="/login"
               class="p-4 text-lg font-bold text-yellow-50 hover:bg-blue-700 <%= if (is_current_route(c, "login")) { %>bg-blue-700<% } %>">
               Log In
            </a>
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Script to close dropdown when clicking outside -->
<script>
  document.addEventListener('click', function(event) {
    // Desktop resources dropdown
    const dropdown = document.getElementById('resources-dropdown');
    const resourcesButton = document.querySelector('.resources-button');

    if (dropdown && !dropdown.contains(event.target) && !resourcesButton.contains(event.target)) {
      dropdown.classList.add('hidden');
    }

    // Mobile menu
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuButton = document.querySelector('.mobile-menu-button');

    if (mobileMenu && !mobileMenu.contains(event.target) && !mobileMenuButton.contains(event.target)) {
      mobileMenu.classList.add('hidden');
    }

    // Mobile resources dropdown
    const mobileResourcesDropdown = document.getElementById('mobile-resources-dropdown');
    const mobileResourcesButton = mobileResourcesDropdown ? mobileResourcesDropdown.previousElementSibling : null;

    // Only close if clicking outside both the dropdown and its button
    if (mobileResourcesDropdown && mobileResourcesButton &&
        !mobileResourcesDropdown.contains(event.target) &&
        !mobileResourcesButton.contains(event.target) &&
        mobileMenu && mobileMenu.contains(mobileResourcesDropdown)) {
      mobileResourcesDropdown.classList.add('hidden');
    }
  });
</script>

<!-- END OF HEADER AND MENU -->
