<div class="max-w-screen-xl px-4 mx-auto mt-8 sm:px-6 lg:px-8">
    <!-- Header Section -->
    <div class="text-center mb-2">
        <h1 class="text-3xl font-extrabold text-blue-900 sm:text-4xl">
            About Dr<PERSON> <PERSON>
        </h1>
        <p class="mt-4 text-lg text-blue-800">
            An overview of my professional background and experience
        </p>
    </div>

    <!-- Content -->
    <div style="display: flex; justify-content: center; align-items: center; width: 100%;">
        <div style="width: 100%; max-width: 850px; margin: 0px auto;">
            <div class="prose prose-lg text-blue-900 mb-8" style="max-width: none;">
                <style>
                    /* Center all images within mammoth content */
                    .prose img {
                        display: block;
                        margin-left: auto;
                        margin-right: auto;
                    }
                    /* Optional: add some spacing around images */
                    .prose img {
                        margin-top: 1.5rem;
                        margin-bottom: 1.5rem;
                    }
                </style>
                <div class="flex flex-col md:flex-row gap-8 mb-8">
                  <div class="md:w-1/3">
                    <img
                      src="<%= assetPath("images/dr-ross-cooper.png") %>"
                      alt="Dr. <PERSON> Cooper"
                      class="rounded-full w-64 h-64 mx-auto object-cover border-4 border-blue-100"
                    />
                  </div>

                  <div class="md:w-2/3">
                    <p class="text-lg mb-4">
                      This page will contain detailed information about Dr. Cooper, the founder of Visual Reading.
                    </p>

                    <p class="text-lg mb-4">
                      We'll be migrating content from the Django site soon. In the meantime, this is a placeholder page
                      to ensure navigation works correctly.
                    </p>
                  </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Section -->
    <div class="my-12 text-center bg-blue-100 rounded-lg p-6">
        <h2 class="text-xl font-bold text-blue-900 mb-4">
            Have any questions or want to get in touch with me?
        </h2>
        <p class="text-blue-800 mb-6">
            I am here to help! Contact me and I'll get back to you as soon as possible.
        </p>
        <a href="/contact"
           class="inline-block px-6 py-3 bg-blue-900 text-yellow-50 rounded-md hover:bg-blue-700 transition-colors">
            Contact Us
        </a>
    </div>
</div>
