package models

import (
	"strings"
	"time"

	"github.com/gobuffalo/pop/v6"
	"github.com/gobuffalo/validate/v3"
	"github.com/gobuffalo/validate/v3/validators"
	"github.com/gofrs/uuid"
	"github.com/pkg/errors"
	"golang.org/x/crypto/bcrypt"
)

// User represents a user account
type User struct {
	ID                   uuid.UUID `json:"id" db:"id"`
	Email                string    `json:"email" db:"email"`
	PasswordHash         string    `json:"-" db:"password_hash"`
	Password             string    `json:"-" db:"-"`
	PasswordConfirmation string    `json:"-" db:"-"`
	FirstName            string    `json:"first_name" db:"first_name"`
	LastName             string    `json:"last_name" db:"last_name"`
	Provider             string    `json:"provider" db:"provider"` // "email", "google", etc.
	ProviderID           string    `json:"provider_id" db:"provider_id"`
	MagicLinkToken       string    `json:"-" db:"magic_link_token"`
	MagicLinkExpiry      time.Time `json:"-" db:"magic_link_expiry"`
	CreatedAt            time.Time `json:"created_at" db:"created_at"`
	UpdatedAt            time.Time `json:"updated_at" db:"updated_at"`
}

// Users is a slice of User
type Users []User

// Validate gets run every time you call a "pop.Validate*" method.
func (u *User) Validate(tx *pop.Connection) (*validate.Errors, error) {
	var err error
	return validate.Validate(
		&validators.StringIsPresent{Field: u.Email, Name: "Email"},
		&validators.EmailIsPresent{Field: u.Email, Name: "Email"},
		&validators.StringIsPresent{Field: u.Provider, Name: "Provider"},
		&validators.FuncValidator{
			Field:   u.Email,
			Name:    "Email",
			Message: "%s is already taken",
			Fn: func() bool {
				var b bool
				q := tx.Where("email = ?", u.Email)
				if u.ID != uuid.Nil {
					q = q.Where("id != ?", u.ID)
				}
				b, err = q.Exists(u)
				if err != nil {
					return false
				}
				return !b
			},
		},
	), err
}

// ValidateCreate gets run every time you call "pop.ValidateAndCreate" method.
func (u *User) ValidateCreate(tx *pop.Connection) (*validate.Errors, error) {
	var err error
	if u.Provider == "email" {
		err = u.validatePassword()
	}
	return validate.NewErrors(), err
}

// ValidateUpdate gets run every time you call "pop.ValidateAndUpdate" method.
func (u *User) ValidateUpdate(tx *pop.Connection) (*validate.Errors, error) {
	return validate.NewErrors(), nil
}

// Create wraps up the pattern of encrypting the password and
// running validations. Useful when writing tests.
func (u *User) Create(tx *pop.Connection) (*validate.Errors, error) {
	if u.Provider == "email" && u.Password != "" {
		ph, err := bcrypt.GenerateFromPassword([]byte(u.Password), bcrypt.DefaultCost)
		if err != nil {
			return validate.NewErrors(), errors.WithStack(err)
		}
		u.PasswordHash = string(ph)
	}
	return tx.ValidateAndCreate(u)
}

// Update wraps up the pattern of encrypting the password and
// running validations. Useful when writing tests.
func (u *User) Update(tx *pop.Connection) (*validate.Errors, error) {
	if u.Provider == "email" && u.Password != "" {
		ph, err := bcrypt.GenerateFromPassword([]byte(u.Password), bcrypt.DefaultCost)
		if err != nil {
			return validate.NewErrors(), errors.WithStack(err)
		}
		u.PasswordHash = string(ph)
	}
	return tx.ValidateAndUpdate(u)
}

// Authenticate checks the password for a user.
func (u *User) Authenticate(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.PasswordHash), []byte(password))
	return err == nil
}

func (u *User) validatePassword() error {
	if u.Provider != "email" {
		return nil
	}

	if u.Password == "" {
		return errors.New("Password can't be blank")
	}

	if u.Password != u.PasswordConfirmation {
		return errors.New("Password and confirmation don't match")
	}

	return nil
}

// GenerateMagicLink generates a magic link token for the user
func (u *User) GenerateMagicLink(tx *pop.Connection) (string, error) {
	token, err := uuid.NewV4()
	if err != nil {
		return "", errors.WithStack(err)
	}

	u.MagicLinkToken = token.String()
	u.MagicLinkExpiry = time.Now().Add(15 * time.Minute) // Token expires in 15 minutes

	err = tx.Update(u)
	if err != nil {
		return "", errors.WithStack(err)
	}

	return u.MagicLinkToken, nil
}

// ValidateMagicLink checks if a magic link token is valid
func (u *User) ValidateMagicLink(token string) bool {
	if u.MagicLinkToken == "" {
		return false
	}

	if u.MagicLinkExpiry.Before(time.Now()) {
		return false
	}

	return strings.TrimSpace(token) == strings.TrimSpace(u.MagicLinkToken)
}
