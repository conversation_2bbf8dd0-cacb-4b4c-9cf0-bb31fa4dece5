package models

import (
	"time"

	"github.com/gobuffalo/pop/v6"
	"github.com/gobuffalo/validate/v3"
	"github.com/gobuffalo/validate/v3/validators"
	"github.com/gofrs/uuid"
)

// ContactMessage represents a message submitted through the contact form
type ContactMessage struct {
	ID        uuid.UUID `json:"id" db:"id"`
	Name      string    `json:"name" db:"name"`
	Email     string    `json:"email" db:"email"`
	Subject   string    `json:"subject" db:"subject"`
	Message   string    `json:"message" db:"message"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// ContactMessages is a slice of ContactMessage
type ContactMessages []ContactMessage

// Validate gets run every time you call a "pop.Validate*" method.
func (c *ContactMessage) Validate(tx *pop.Connection) (*validate.Errors, error) {
	return validate.Validate(
		&validators.StringIsPresent{Field: c.Name, Name: "Name"},
		&validators.StringIsPresent{Field: c.Email, Name: "Email"},
		&validators.EmailIsPresent{Field: c.Email, Name: "Email"},
		&validators.StringIsPresent{Field: c.Subject, Name: "Subject"},
		&validators.StringIsPresent{Field: c.Message, Name: "Message"},
	), nil
}
