package models

import (
	"time"

	"github.com/gobuffalo/pop/v6"
	"github.com/gobuffalo/validate/v3"
	"github.com/gobuffalo/validate/v3/validators"
	"github.com/gofrs/uuid"
)

// EmailTracking represents an email tracking record
type EmailTracking struct {
	ID           uuid.UUID `json:"id" db:"id"`
	TrackingID   string    `json:"tracking_id" db:"tracking_id"`
	Email        string    `json:"email" db:"email"`
	TemplateName string    `json:"template_name" db:"template_name"`
	SentAt       time.Time `json:"sent_at" db:"sent_at"`
	OpenedAt     time.Time `json:"opened_at" db:"opened_at"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// EmailTrackings is a slice of EmailTracking
type EmailTrackings []EmailTracking

// Validate gets run every time you call a "pop.Validate*" method.
func (e *EmailTracking) Validate(tx *pop.Connection) (*validate.Errors, error) {
	return validate.Validate(
		&validators.StringIsPresent{Field: e.TrackingID, Name: "TrackingID"},
		&validators.StringIsPresent{Field: e.Email, Name: "Email"},
		&validators.EmailIsPresent{Field: e.Email, Name: "Email"},
		&validators.StringIsPresent{Field: e.TemplateName, Name: "TemplateName"},
		&validators.TimeIsPresent{Field: e.SentAt, Name: "SentAt"},
	), nil
}

// MarkAsOpened marks the email as opened
func (e *EmailTracking) MarkAsOpened(tx *pop.Connection) error {
	e.OpenedAt = time.Now()
	return tx.Update(e)
}
