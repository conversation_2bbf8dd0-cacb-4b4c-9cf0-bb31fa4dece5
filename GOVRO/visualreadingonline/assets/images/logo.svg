<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 21.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Buffalo_x5F_Gopher" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
	 y="0px" viewBox="0 0 900 900" enable-background="new 0 0 900 900" xml:space="preserve">
<g>
	<path fill="none" stroke="#211915" stroke-width="16" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M168.4,188c69-72,165.9-117.3,273.3-117.3C651.2,70.7,821,240.4,821,449.8C821,659.3,651.2,829,441.7,829
		C232.3,829,62.5,659.2,62.5,449.8l-0.2,0c-0.1-1.1,0.1-2.2,0-3.2c-1.7-27.6-3.4-55.2-5.1-82.8c-0.7-11.6-1.6-23.1-2.1-34.7
		c-0.2-3.6-0.6-7.2-0.3-10.7c0.9-13.3,12.8-11,24.1-13.5c0,0,0-87.7,0-87.7s0.8-15,24.2-15L127,188h9.4l-0.5-20.2
		c0,0-7.2-0.7-7.9-6.7c0,0-0.7-5.4,1.6-6.4c0,0,2.4-1.6,2.4,5.9c0,0,1.2,2.6,3,2.6l0.2-19.4c0,0,0.2-3.2,2.7-3.2
		c0,0,3.1-0.7,3.1,2.7c0,0,0,27.4,0,27.6c0,0.2,3.1-1.6,3.3-2c0.6-1,0.4-2.4,0.5-3.5c0.1-1.8-0.1-8.4,1.6-8.8
		c2.2-0.6,2.7,3.2,2.8,4.8c0.3,3.5-0.2,8-2.6,10.9c-0.4,0.5-4.6,3.6-4.6,3.2c0,0-0.7,12.7-0.7,12.7h0.3H168.4z"/>
	<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="494.644" y1="540.6563" x2="494.644" y2="70.5313">
		<stop  offset="0" style="stop-color:#D17C56"/>
		<stop  offset="0.4089" style="stop-color:#D0805B"/>
		<stop  offset="1" style="stop-color:#B2E6EA"/>
	</linearGradient>
	<path fill="url(#SVGID_1_)" d="M168.3,187c69-71.8,166-116.4,273.4-116.4C651.2,70.5,821,240.3,821,449.8c0,24.3-2.3,48.1-6.7,71.2
		l-151.9,7.4l-63.2,12.3L181.9,230.2L168.3,187z"/>
	<polygon fill="#AF7133" points="764.3,649 645.2,607.4 574.6,597.2 578.2,548 625.4,538.5 638.4,538.5 809.4,538.8 809.4,541.5 
		805.7,556.7 798.1,579.7 788,606.2 779.7,620.5 772.9,634.6 	"/>
	<polygon fill="#AF7133" points="62.3,448.3 55.2,332.5 54.7,318.3 56.1,313 58.6,309.7 64.1,307.2 75.7,305.4 78.7,304.8 
		78.4,266.1 78.4,228.5 79.2,215.5 82.6,208.9 88.7,204.3 98,201.8 101.9,201.5 109.9,197.4 127,187 181.9,187.5 202.6,176.4 
		217.8,175.5 248.9,173.6 258.2,173.1 268.8,178.1 275.9,183 283.2,190.4 285.2,203.6 290.3,302.5 287.4,449.8 291.4,561.8 
		297.1,624.2 271.1,629.4 160.1,651.5 125.2,658.6 117.2,646.1 104.9,624.2 92.4,597.5 77.9,557.1 72.1,535 68.7,518.5 65.6,498.2 
		63.5,477.8 62.8,459.5 	"/>
	<polygon fill="#7B3D24" points="764.8,648.5 643.8,607.5 574.8,596.1 294.7,624.7 125.2,658.6 141.7,681.7 154.8,697.7 181.9,726 
		227.5,762.7 261.6,783.5 316.2,807.7 359.8,820.1 382,824.3 412.7,827.9 439.1,829 454.4,829 482.9,826.8 518.3,821.2 543.9,815.1 
		566.1,808.1 595.4,796.5 612.4,788.5 621.6,783.7 649.3,767.2 668.1,754.1 694.8,732.2 712.8,715 734.5,690.8 751.3,668.8 	"/>
	<path fill="#AF7133" d="M305.9,673.9l-53.1,1.8l20.8,73.5l28.7,6l48.7,1.3h83.3l15-3c0,0,36.7-9.5,37.7-9.5
		c1,0,61.4-15.4,61.4-15.4l76.9-19.2v-19l-24.8-26.3l-49.2-6.9L305.9,673.9z"/>
	<path fill="#BC9E6C" d="M533.5,231.3l-10.1-4.3h-14.3l-25.7-6h-34.6l-39.4,6h-51.6l-25.7,3.3c-26.8,15.8-41.1,46-52.2,73.8
		c-8.9,22.4-19.3,45-21.9,69.2c-1.3,11.8-0.2,24.8,2.9,37.2l-2.1,2.1l-1.3,5.4l6.7,22.8l32.7,35.6l67.7,35.5l55.7,12.5l44.9-1.9
		l23.7-14.7l66.2-31.5l30.4-14.3l16.3-42.7v-12l0.5-0.4c9.5-45.1-4.3-80.4-24.7-118.8c-12.4-23.3-21.6-43.6-45.7-57.9"/>
	<path fill="#6AD7E5" d="M299.1,463.3c-19.8-66.4,11.8-149.6,89.2-149.4l47.1-1.4h41l-0.1,0.3c77.4-0.2,109,83,89.2,149.3l-12.4,8.4
		l-26.4,19.6l-10.2,10.4h-15.1l-24.6,11.7l-14.4,6.1l-28.5,2.7l-33.3-0.9l-28.8-11.6L332.2,490l-25.7-19.4L299.1,463.3z"/>
	<path fill="#F6D2A2" d="M406.2,438.7c4.5-2.6,10.4-5.5,15.7-5.1c5.1,0.4,10.1,1.6,15.2,2.4c4.8,0.8,9.3,0.8,13.9,2.6
		c4.6,1.8,9,4.9,12.1,8.7c3.6,4.5,1.6,11.4-3.7,14.5c-9.8,5.8-18.4-5.6-28.3-5.5c-9.5,0.1-18.1,9.1-27.3,1.3
		C397.8,452.6,400.3,442.1,406.2,438.7z"/>
	<path fill="#FFFFFF" d="M443.5,474.3c4.4-1.2,5.2-6.8,3.9-10.7c-1.6-5-8.2-8.4-13.4-5.9c-4.3,2-2.8,6.4-2.9,11.4
		C430.9,475.8,438.5,475.6,443.5,474.3z"/>
	<path fill="#FFFFFF" d="M412.3,461.6c1.7,0.5,3.6,0,5.3-0.6c3.3-1.2,9.3-5.8,11.6-3.9c2.7,2.1,1.5,15.5,0.8,15.9
		c-3.4,2-8.4,2.9-13.3,1.3c-5.9-1.9-3.6-7-3.5-10.8c0-0.6,0.4-1.1,0.3-1.7"/>
	<ellipse transform="matrix(0.128 -0.9918 0.9918 0.128 45.5048 835.4331)" fill="#FFFFFF" cx="497.8" cy="391.8" rx="49" ry="49"/>
	
		<ellipse transform="matrix(0.128 -0.9918 0.9918 0.128 -72.0028 704.4983)" fill="#FFFFFF" cx="364.6" cy="393.2" rx="49" ry="49"/>
	
		<ellipse transform="matrix(0.128 -0.9918 0.9918 0.128 7.9834 574.7572)" fill="#FFFFFF" cx="330.8" cy="282.8" rx="24.3" ry="24.3"/>
	<path fill="#726958" d="M294.2,271.6c-5.3-1.7-11.7-13.6-14.2-18.3c-5.1-9.2-8.4-19.4-9-30c-0.3-5.5,0.1-17.7,7.4-19.5
		c9.1-2.3,14.1,11.7,20.6,15.8c9.3,5.9,21.7,4.5,32,2.4c0,0,0.7,4.5,0.8,4.8c0.1,0.9,0.7,2.3,0.5,3.1c-0.2,1.1-2,1.6-2.9,2.1
		c-3.2,1.7-6,3.6-8.9,5.8c-1.9,1.5-3.5,3.2-5.2,5c-0.8,0.8-1.6,1.5-2.3,2.4c-0.7,0.9-1.3,1.9-2,2.8c-1.9,2.1-3.2,4.6-4.9,6.8
		c-1.4,1.7-3,3.5-4,5.5c-1,1.9-2.4,3.7-3.2,5.6c-0.5,1.3-0.7,2.5-1.8,3.5C296,270.3,295,270.9,294.2,271.6z"/>
	<path fill="#BC9E6C" d="M259.4,518c-6.9,4-17,12.8-21.8,17.6c-2.6,2.6-11.7,10.7-13,14.3c-2.5,6.8,7.5,2.5,8.8,8.6
		c1.5,6.9-5.1,11,5.3,10.7c5.8-0.2,11.4-2.1,16.7-4c8.7-3.2,20.5-5.3,30.2-10.6l0.2-4l0.2-3.6l-8.3-8.3l-10.3-17.8l-2.7-2.8H259.4z"
		/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M317.5,664.9c-8.3,6.8-16.6,13.6-20.8,23.6c-5,11.8-0.9,27.8,13.1,28.6c9.8,0.5,27-6.3,35.6-10.5c8.8-4.3,23.5-13.1,31.8-18.9"/>
	<path fill="#BC9E6C" d="M283.3,259c-6.4,0-28.9,8-30.7,14.9c-3.2,12.2,24.7,15.7,32.6,17.2l8.9-19.5L283.3,259z"/>
	
		<ellipse transform="matrix(0.9918 -0.128 0.128 0.9918 -31.8144 70.6109)" fill="#FFFFFF" cx="533.5" cy="282.8" rx="24.3" ry="24.3"/>
	<path fill="#726958" d="M533.5,222.1c10.2,2.1,22.6,3.5,32-2.3c6.5-4.1,11.6-18.1,20.6-15.8c7.2,1.8,7.7,14,7.3,19.6
		c-0.6,10.5-4,20.7-9,30c-2.6,4.7-9.7,17.1-15,18.7l-10.3-13.7l-11-15.8l-12.1-10.1l-3.6-2.7L533.5,222.1"/>
	<path fill="#BC9E6C" d="M286,546.9c-2.6,33.1,6.4,70.2,20.1,100.1c6,13.2,15.1,24.5,28.5,30.6c21.6,9.8,46.8,10.5,70.1,11.8
		c9.1,0.5,21,1.2,29.2,1.4c8.3-0.2,16.6-0.9,25.7-1.4c23.3-1.4,48.5-2,70.1-11.8c13.4-6.1,22.5-17.4,28.5-30.6
		c13.7-29.9,22.6-67,20.1-100.1l-5.7,3.1l-14.7,3.2l-9.7,2.9l-6.4,17.7l-21.9,20.6l-19.4,3.1l-11.7,1.6l-7.5,7.5l-8.2-8.7l-8.4,8.4
		L454,609l-28.3-1.8l-12.5,0.4l-18.5-9.1l-8.6,2.1l-16-4.2l-38.1-12.9l-12.2-14.2l-4.4-11.7l-9.6-2.5L286,546.9z"/>
	<path fill="#BC9E6C" d="M604.9,518.3c6.9,4.3,17,12.7,21.8,17.4c2.6,2.6,11.7,10.6,13,14.2c2.5,6.8-7.5,2.4-8.8,8.5
		c-1.5,6.9,5.1,11-5.3,10.7c-5.8-0.2-11.4-2.1-16.7-4.1c-8.7-3.2-20.5-5.3-30.2-10.6l-0.5-7.6l8.2-6.1l6.7-7.4l2.5-8.9l3.1-5.8
		L604.9,518.3"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M546.8,664.9c8.3,6.8,16.6,13.6,20.8,23.6c5,11.8,0.9,27.8-13.1,28.6c-9.8,0.5-27-6.3-35.6-10.5c-8.8-4.3-23.5-13.1-31.8-18.9"/>
	<path fill="#BC9E6C" d="M579.2,289.8c7.9-1.5,35.6-3.4,32.4-15.6c-1.8-6.9-24.2-15-30.6-15l-11.6,13L579.2,289.8z"/>
	<path fill="#493628" d="M294.8,462.1c27.8-1.2,51.6,19.5,64.3,42l-7-10.7c14.2-2.2,32.2,4.7,41.5,16c10.4-3,16.9,0.6,20.6,5.3
		c10.2-6.8,25.1-3.8,35,0c3.6-5,10.5-8.4,21.4-5.3c9.3-11.4,27.3-18.2,41.5-16l-7,10.7c12.7-22.6,36.5-43.3,64.3-42
		c18.5-14.8,29.8-30.7,33-54.6l-0.5,3c31.9-6.5,36.9,118.6-6.8,107.6l3.5,0.6c-1.8,22.1-31.5,38.1-51.5,37.4l3.2,0.1
		c-5.7,28.7-35.4,46.9-59.5,42.9c0,0.6-3.7,8.1-13,7.7c-7.4-0.3-3.8-6.7-7.3-10.7c-0.2,2.7-3.1,10.9-6.7,11.8
		c-3.7,0.9-3.9,1.4-12.4-0.9c-0.7,11.1-7.6,27.4-16.7,34.5c-8.2-5.6-18-24.8-20.4-34.8c-4.6,3.2-7.8,3.6-12.1,0.1
		c-2.4-1.9-8.3-8.2-8.5-10.8c-3.5,4,0,10.4-7.3,10.7c-4.5,0.2-9.9-5.7-10-10c-26.2-1.2-56.8-11.9-62.6-40.6l3.2-0.1
		c-20,0.7-49.7-15.3-51.5-37.4l3.5-0.6c-43.7,11-40-114.1-8.1-107.6c2.3,9.1,5.7,17.9,10.3,25.5
		C277.3,446.1,285.6,454.7,294.8,462.1z"/>
	<path fill="#6E5128" d="M432.7,288.4c9.9-0.2,20.7-0.6,29.5,3.5c10.5,4.9,17.9,23.8,13,34.9c-4.8,11-16.3,17-27.5,19.7
		c-18.9,4.5-49.7,0.8-58.7-19.7c-4.9-11.1,2.5-30,13-34.9C411.1,287.7,422.6,288.1,432.7,288.4"/>
	<polygon fill="#5B5B5F" points="234.5,539 241.6,543.7 245.8,550.9 248,556.8 248,563.5 247.4,567.7 234.4,568.9 232.8,567.7 
		233.4,558.5 228.7,554.3 224.2,553.9 226.6,546.9 	"/>
	<polygon fill="#5B5B5F" points="621.4,543.9 625.4,540.7 628.9,538.9 633.4,542.6 640.1,549.8 639.6,553.3 635.6,555.1 
		630.8,556.7 630.2,563.4 631.5,567.7 625.6,569.2 616.7,567.7 615,564.5 615,557.5 616.4,550 	"/>
	<polygon fill="#BC9E6C" points="377.2,686.7 365.7,695 342.3,707.7 323.7,715 311.4,716.8 304.9,715.7 299.6,713.1 296,705.7 
		294.7,699.4 295.8,691 297.1,684.3 306,675.5 316.5,665.6 321.4,670.5 330.4,675.5 346.7,681.7 359.8,685.3 	"/>
	<path fill="#BC9E6C" d="M546.8,664.9c8.3,6.8,16.6,13.6,20.8,23.6c5,11.8,0.9,27.8-13.1,28.6c-9.8,0.5-27-6.3-35.6-10.5
		c-8.8-4.3-23.5-13.1-31.8-18.9l9-1.1l18.4-3.6l17.6-6.6l11.4-8.1L546.8,664.9z"/>
	<polygon fill="#5B5B5F" points="566.1,686.2 557.7,687.4 548.1,690.5 537.1,697.7 527.8,706.3 526,709.2 533.7,712.4 547.7,716.3 
		555.1,717 561.1,715.3 566.1,711.6 569.7,705.6 569,698.2 567.5,690.1 	"/>
	<polygon fill="#5B5B5F" points="297.1,684.3 307.6,687.3 318.3,692.1 331.2,701.6 339.1,709.5 326.4,713.9 311.4,716.8 
		304.9,715.7 299.6,713.1 294.7,701.6 293.4,695.3 296.2,688.8 	"/>
	<polygon fill="#AF7133" points="722.3,673.7 706.4,665.5 685.4,665.5 680.8,691.3 693.6,691 	"/>
	<polygon fill="#7B3D24" points="618.1,528.1 662.4,497.5 772.4,497.5 787.4,498 813.1,520.5 812.8,528.3 809.4,538.8 727.8,538.6 
		631.4,538.6 	"/>
	<polygon fill="#FFD6AC" points="647.5,608.8 808.1,548.1 806.4,554.1 654.9,611.3 	"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M762.5,559v-6.2
		c0,0-2.6-0.1-2.9-2.1c0,0-0.3-1.7,0.4-2.1c0,0,0.7-0.5,0.7,2c0,0,0.4,0.9,1,0.9l0-6.4c0,0,0.4-1.1,1.3-1.1c0,0,1.4-0.2,1.4,0.9
		c0,0,0,9.1,0,9.1c0,0.1,0.6-0.5,0.7-0.7c0.2-0.3-0.1-0.8,0-1.2c0-0.6-0.1-2.8,0.4-2.9c0.7-0.2,0.8,1.1,0.9,1.6
		c0.1,1.2,0.2,2.6-0.7,3.6c-0.1,0.2-1.3,1-1.3,0.9c0,0,0,3.7,0,3.7"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M754.5,626v-6.2
		c0,0-2.6-0.1-2.9-2.1c0,0-0.3-1.7,0.4-2.1c0,0,0.7-0.5,0.7,2c0,0,0.4,0.9,1,0.9l0-6.4c0,0,0.4-1.1,1.3-1.1c0,0,1.4-0.2,1.4,0.9
		c0,0,0,9.1,0,9.1c0,0.1,0.6-0.5,0.7-0.7c0.2-0.3-0.1-0.8,0-1.2c0-0.6-0.1-2.8,0.4-2.9c0.7-0.2,0.8,1.1,0.9,1.6
		c0.1,1.2,0.2,2.6-0.7,3.6c-0.1,0.2-1.3,1-1.3,0.9c0,0,0,3.7,0,3.7"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M676.5,564v-6.2
		c0,0,2-0.1,2.2-2.1c0,0,0-1.7-0.7-2.1c0,0-0.9-0.5-0.9,2c0,0-0.4,0.9-1,0.9l-0.1-6.4c0,0,0.2-1.1-0.6-1.1c0,0-0.8-0.2-0.8,0.9
		c0,0,0,9.1,0,9.1c0,0.1-1.3-0.5-1.4-0.7c-0.2-0.3-0.3-0.8-0.3-1.2c0-0.6,0-2.8-0.6-2.9c-0.7-0.2-0.9,1.1-1,1.6
		c-0.1,1.2,0.5,2.6,1.3,3.6c0.1,0.2,1.9,1,1.9,0.9c0,0,0,3.7,0,3.7"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M593.5,590v-6.2
		c0,0,2-0.1,2.2-2.1c0,0,0-1.7-0.7-2.1c0,0-0.9-0.5-0.9,2c0,0-0.4,0.9-1,0.9l-0.1-6.4c0,0,0.2-1.1-0.6-1.1c0,0-0.8-0.2-0.8,0.9
		c0,0,0,9.1,0,9.1c0,0.1-1.3-0.5-1.4-0.7c-0.2-0.3-0.3-0.8-0.3-1.2c0-0.6,0-2.8-0.6-2.9c-0.7-0.2-0.9,1.1-1,1.6
		c-0.1,1.2,0.5,2.6,1.3,3.6c0.1,0.2,1.9,1,1.9,0.9c0,0,0,3.7,0,3.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M136.4,187l-0.5-19.8c0,0-7.2-0.3-7.9-6.3c0,0-0.7-5.3,1.6-6.3c0,0,2.4-1.5,2.4,5.9c0,0,1.2,2.6,3,2.6l0.2-19.4
		c0,0,0.7-3.2,3.2-3.2c0,0,3.6-0.7,3.6,2.7c0,0,0,27.4,0,27.6c0,0.2,2.6-1.6,2.8-2c0.6-1,0.2-2.4,0.2-3.5c0.1-1.8-0.2-8.4,1.4-8.8
		c2.2-0.6,2.6,3.2,2.7,4.8c0.3,3.5-0.2,8-2.6,10.9c-0.4,0.5-4.7,3.1-4.7,2.7c0,0-0.8,11.9-0.8,11.9"/>
	<path fill="#ACDD00" d="M762,559v-6.2c0,0-2.4-0.1-2.6-2.1c0,0-0.2-1.7,0.5-2.1c0,0,0.8-0.5,0.8,2c0,0,0.4,0.9,1,0.9l0.1-6.4
		c0,0,0.2-1.1,1-1.1c0,0,1.2-0.2,1.2,0.9c0,0,0,9.1,0,9.1c0,0.1,0.9-0.5,1-0.7c0.2-0.3,0.1-0.8,0.1-1.2c0-0.6-0.1-2.8,0.5-2.9
		c0.7-0.2,0.9,1.1,0.9,1.6c0.1,1.2-0.1,2.6-0.9,3.6c-0.1,0.2-1.6,1-1.6,0.9c0,0,0,3.7,0,3.7H762z"/>
	<path fill="#ACDD00" d="M754,626v-6.2c0,0-2.4-0.1-2.6-2.1c0,0-0.2-1.7,0.5-2.1c0,0,0.8-0.5,0.8,2c0,0,0.4,0.9,1,0.9l0.1-6.4
		c0,0,0.2-1.1,1-1.1c0,0,1.2-0.2,1.2,0.9c0,0,0,9.1,0,9.1c0,0.1,0.9-0.5,1-0.7c0.2-0.3,0.1-0.8,0.1-1.2c0-0.6-0.1-2.8,0.5-2.9
		c0.7-0.2,0.9,1.1,0.9,1.6c0.1,1.2-0.1,2.6-0.9,3.6c-0.1,0.2-1.6,1-1.6,0.9c0,0,0,3.7,0,3.7H754z"/>
	<path fill="#ACDD00" d="M676,564v-6.2c0,0,2.2-0.1,2.4-2.1c0,0,0.1-1.7-0.6-2.1c0,0-0.8-0.5-0.8,2c0,0-0.4,0.9-1,0.9l-0.1-6.4
		c0,0,0-1.1-0.9-1.1c0,0-1-0.2-1,0.9c0,0,0,9.1,0,9.1c0,0.1-1.1-0.5-1.1-0.7c-0.2-0.3-0.1-0.8-0.2-1.2c0-0.6,0-2.8-0.5-2.9
		c-0.7-0.2-0.9,1.1-0.9,1.6c-0.1,1.2,0.2,2.6,1,3.6c0.1,0.2,1.7,1,1.7,0.9c0,0,0,3.7,0,3.7H676z"/>
	<path fill="#ACDD00" d="M593,590v-6.2c0,0,2.2-0.1,2.4-2.1c0,0,0.1-1.7-0.6-2.1c0,0-0.8-0.5-0.8,2c0,0-0.4,0.9-1,0.9l-0.1-6.4
		c0,0,0-1.1-0.9-1.1c0,0-1-0.2-1,0.9c0,0,0,9.1,0,9.1c0,0.1-1.1-0.5-1.1-0.7c-0.2-0.3-0.1-0.8-0.2-1.2c0-0.6,0-2.8-0.5-2.9
		c-0.7-0.2-0.9,1.1-0.9,1.6c-0.1,1.2,0.2,2.6,1,3.6c0.1,0.2,1.7,1,1.7,0.9c0,0,0,3.7,0,3.7H593z"/>
	<path fill="#ACDD00" d="M136.4,187l-0.5-19.8c0,0-7.2-0.3-7.9-6.3c0,0-0.7-5.3,1.6-6.3c0,0,2.4-1.5,2.4,5.9c0,0,1.2,2.6,3,2.6
		l0.2-19.4c0,0,0.7-3.2,3.2-3.2c0,0,3.6-0.7,3.6,2.7c0,0,0,27.4,0,27.6c0,0.2,2.6-1.6,2.8-2c0.6-1,0.2-2.4,0.2-3.5
		c0.1-1.8-0.2-8.4,1.4-8.8c2.2-0.6,2.6,3.2,2.7,4.8c0.3,3.5-0.2,8-2.6,10.9c-0.4,0.5-4.7,3.1-4.7,2.7c0,0-0.8,11.9-0.8,11.9
		L136.4,187z"/>
	
		<radialGradient id="SVGID_2_" cx="737.6794" cy="406.1918" r="45.3992" gradientTransform="matrix(0.937 -0.3492 0.3492 0.937 -91.5873 282.9574)" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#C47826"/>
		<stop  offset="0" style="stop-color:#B55314"/>
		<stop  offset="0.8092" style="stop-color:#C98F31"/>
	</radialGradient>
	<path fill="url(#SVGID_2_)" d="M700,424.1c-2.6-6.1-4.2-13.1-3.9-20.2c1-25,22.1-44.5,47.2-43.4c25,1,44.5,22.1,43.4,47.2
		c-0.7,16.4-11.4,32.5-25,39.3L700,424.1z"/>
	<path fill="#FFFFFF" d="M705,234.9c0-0.2,1-0.4,1.7-0.5c2.6-0.4,5.2-0.4,7.8,0c4.1,0.6,7.9,2.2,11.2,4.8c4.6,3.7,7.4,9.1,9.1,14.7
		c0,0,17.3-3,19.2,14c6.3,0,15.6-1.2,18.1,6.5c2.2,6.8-1.9,11.1-7,12.9c-2.2,0.8-4.6,1.2-6.7,1.1c-12.3-0.3-24.6-0.2-36.9,0
		c-21.3-0.2-54.4-0.6-75.7,0c-2.2,0.1-4.5-0.3-6.7-1.1c-5.1-1.9-9.2-6.1-7-12.9c2.5-7.6,11.8-6.5,18.1-6.5c1.9-17,19.5-14,19.5-14
		C676.9,229.4,705,234.9,705,234.9L705,234.9z"/>
	<path fill="#FFFFFF" d="M380.7,167.5c-0.7,0-5.5,0-6.3,0c-21.3-0.2-54.4-0.6-75.7,0c-2.2,0.1-4.5-0.3-6.7-1.1
		c-5.1-1.9-9.2-6.1-7-12.9c2.5-7.6,11.8-6.5,18.1-6.5c1.9-17,19.5-14,19.5-14c7.2-24.6,35.3-19,35.3-19v0c0-0.2,1-0.4,1.7-0.5
		c2.6-0.4,5.2-0.4,7.8,0c4.1,0.6,7.9,2.2,11.2,4.8c4.6,3.7,7.4,9.1,9.1,14.7c0,0,17.3-3,19.2,14c7.1-3.8,12.9-0.8,16.2,3.4"/>
	<path fill="#FFFFFF" d="M599.3,332.3c2.3-11.3,15-9.9,15-9.9h0c0-0.1,0.4-0.2,0.8-0.3c1.2-0.3,2.3-0.4,3.5-0.3
		c1.9,0.1,3.6,0.7,5.2,1.7c2.2,1.5,3.7,3.8,4.6,6.2c0,0,7.6-2,9.1,5.5c2.8-0.2,6.9-1.1,8.3,2.2c1.3,3-0.4,5-2.6,6.1
		c-0.9,0.4-2,0.7-3,0.8c-5.5,0.3-11,0.9-16.5,1.4c-6,0.5-14.1,1.1-21.6,1.7"/>
	<path fill="#FFFFFF" d="M661.5,433c-1.9-0.2-3.7-0.4-5.6-0.6c-3.7-0.4-9.4-1.1-13.1-1.4c-0.4,0-0.8-0.1-1.1-0.3
		c-0.9-0.4-1.5-1.2-1-2.4c0.6-1.3,2.2-0.9,3.3-0.8c0.6-2.9,3.6-2.1,3.6-2.1c1.7-4.1,6.5-2.6,6.5-2.6l0,0c0,0,0.2-0.1,0.3-0.1
		c0.5,0,0.9,0,1.4,0.2c0.7,0.2,1.3,0.5,1.8,1c0.7,0.7,1.1,1.7,1.3,2.7c0,0,2.2-0.1,2.9,1.7"/>
	<path fill="#493628" d="M508.6,255.4c0,1.2-0.3,2.8-0.5,4.2c-0.4,2.9-1.4,5.7-2.2,8.5c5.9-6.6,12.4-10.7,16.5-19.3
		c4.6-9.6,11.1-15.9,11.1-26.7c-0.9-11.9-7-31.3-22.1-31.3c-4.4-9.4-17-17.2-24.2-15.5c-0.9-15.8-22.3-25.7-38.8-21.3l-0.1,0
		c-8.3-6.8-20.1-8.6-31.2,0c-16.7-4.5-38.2,5.6-38.6,21.7c-6.9-2.6-20.9,5.2-25.5,15c-15.1,0-21.2,19.4-22.1,31.3
		c0,10.8,6.5,17.1,11.1,26.7c4.1,8.7,10.6,12.8,16.5,19.3c-0.8-2.7-1.8-5.6-2.2-8.5c-0.2-1.4-0.5-3-0.5-4.2l0,0.1
		c3.2,6.8,3.4,12.8,9.5,17.8c5.4,4.5,12,6.4,17.9,8.8c-1.2-2-2.4-10,0-13.3c1.4,7,20.1,16.5,27.3,15.3c-1.2,0-1.3-8.5-1.3-11.3
		c6.4,0,16.9,6.7,23.1,10.2c6.2-3.5,16.6-10.3,23.1-10.2c0,2.8-0.1,11.4-1.3,11.3c7.3,1.1,25.9-8.3,27.3-15.3
		c2.4,3.3,1.2,11.3,0,13.3c5.8-2.4,12.4-4.4,17.9-8.8C505.2,268.3,505.3,262.3,508.6,255.4"/>
	<polygon fill="#AF7133" points="788,500.5 787.4,498 787.4,473.5 786.9,465.5 783.4,459.5 783.4,451.5 783.4,438.5 786.4,431.5 
		786.4,422.5 783.7,416.9 782.4,410.5 774.4,410.5 774.4,425.5 772.4,430.5 772.4,456.5 766.4,453.1 766.4,446.5 755.4,444.1 
		755.4,405.5 754.8,402.8 752.2,400.2 745.5,398.8 735.8,392.5 711.4,392.5 702.3,387.8 679.4,386.5 676.4,386.5 669.3,390.8 
		665.4,395.5 665.4,407.5 662.3,410.3 661.4,428.3 661.4,497.5 772.4,497.5 	"/>
	<polygon fill="#444242" points="433.2,512 433.1,512.5 431.4,515.4 431,518.2 431.8,521.7 432.8,520.5 433.9,518.2 433.5,516.5 
		433.5,513.3 433.9,512 	"/>
	<polygon fill="#444242" points="437.6,513.2 438.6,515.1 439.1,517.4 438.9,520.6 438.1,522.2 436.4,518.9 437.1,517.5 
		437.4,516.1 437.3,514 	"/>
	<polygon fill="#5B5B5F" points="435.4,520 436.5,522.1 438,525.7 439,530.5 439,532.5 438.4,533.7 437.1,534.8 435.6,535.1 
		433.9,534.7 432.7,533.5 432.2,532 432.9,526.4 433.9,523.5 435,520.9 435.3,520.6 435.1,519.7 433.9,518.3 432.1,522.2 
		430.4,526.7 429.4,531.1 429.6,533.8 430.3,535.4 432.1,536.5 433.9,536.8 436.2,536.8 438.4,536.2 439.8,535.1 440.5,534 
		440.7,532 440.5,529.6 439.4,525.4 436.9,519.3 436.4,518.9 	"/>
	<polygon fill="#5B5B5F" points="433.7,512.9 433.5,514 433.5,516.5 434.6,519.1 435.4,520 436.4,518.9 437.1,517.5 437.4,516.1 
		437.2,513.2 	"/>
	<polygon fill="#726958" points="435.3,520.4 434.1,523.4 433,528.3 433,530.5 433.5,529.7 434.6,529 435.6,528.8 437.3,529.4 
		438.4,530.5 438.1,528.3 437.6,525.3 436.4,522.3 	"/>
	<circle fill="#444242" cx="435.4" cy="532" r="3.2"/>
	<rect x="432" y="536" fill="#727176" width="8" height="104"/>
	<path fill="#444242" d="M435,542h-3c0,0,0-5.5,0-5.5c0-0.2,0.9,0.2,0.9,0.2c0.4,0.2,0.7,0.3,1.2,0.4c0.5,0.1,0.7,0.1,0.7,0.6
		c0,0.7,0.2,1.4,0.2,2.1C435,540.6,435,542,435,542z"/>
	<rect x="437" y="542" fill="#444242" width="3" height="4"/>
	<rect x="437" y="550" fill="#444242" width="3" height="4"/>
	<rect x="432" y="546" fill="#444242" width="3" height="4"/>
	<rect x="432" y="554" fill="#444242" width="3" height="4"/>
	<rect x="437" y="558" fill="#444242" width="3" height="4"/>
	<rect x="437" y="566" fill="#444242" width="3" height="4"/>
	<rect x="432" y="562" fill="#444242" width="3" height="4"/>
	<rect x="432" y="570" fill="#444242" width="3" height="4"/>
	<rect x="437" y="574" fill="#444242" width="3" height="4"/>
	<rect x="437" y="582" fill="#444242" width="3" height="4"/>
	<rect x="432" y="578" fill="#444242" width="3" height="4"/>
	<rect x="432" y="586" fill="#444242" width="3" height="4"/>
	<rect x="437" y="590" fill="#444242" width="3" height="4"/>
	<rect x="437" y="598" fill="#444242" width="3" height="4"/>
	<rect x="432" y="594" fill="#444242" width="3" height="4"/>
	<rect x="432" y="602" fill="#444242" width="3" height="4"/>
	<rect x="437" y="606" fill="#444242" width="3" height="4"/>
	<rect x="437" y="614" fill="#444242" width="3" height="4"/>
	<rect x="432" y="610" fill="#444242" width="3" height="4"/>
	<rect x="432" y="618" fill="#444242" width="3" height="4"/>
	<rect x="437" y="622" fill="#444242" width="3" height="4"/>
	<rect x="437" y="630" fill="#444242" width="3" height="4"/>
	<rect x="432" y="626" fill="#444242" width="3" height="4"/>
	<rect x="432" y="634" fill="#444242" width="3" height="4"/>
	<path fill="none" d="M569.8,218.1c-0.3-1.4,0.2-2.7,1.3-3.3"/>
	<path fill="none" d="M197.9,758.5c-1.9,0-3.6-0.4-5.5-0.2"/>
	<path fill="none" d="M189.4,760c0.1-0.2,0.3-0.3,0.5-0.2"/>
	<path opacity="0.2" d="M693.3,673.8c0.9,1.1,2.1,2,3,3.1c0.7,0.9,1.2,2.5,2.4,2.8c1.5,0.3,2.7-0.6,4.2,0c1.4,0.6,2.9,1.3,3.9,2.4
		c-3.9,2.7-7.5,5.6-11.9,7.6c-1.4,0.6-2.5,0.5-3.9,0.8c-2.9,0.5-6.7,2.2-9.5,0.7c-0.1-1,0.2-2.2,0.2-3.3c0-3.2,0.8-6.3,1.6-9.3
		c0.6-2.3,0.6-4.7,1-7c0.2-1.2,0.6-2.4,0.9-3.6c0.2-0.6,0.1-2,1-2.2c0.9-0.2,2.8,2.4,3.2,3C690.7,670.5,691.9,672.2,693.3,673.8z"/>
	<path fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M154.5,242v54.4c0,0-13.7-3-13.7,25.7c0,0-7.3-3-7.3,12.1V419"/>
	
		<line fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="78.5" y1="461" x2="78.5" y2="555"/>
	
		<line fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="182.5" y1="394" x2="182.5" y2="647"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M658.4,520.5"/>
	<path opacity="0.2" d="M754,626v7.6c0,0-2.6,0.1-2.9,2.1c0,0-0.2,1.7,0.7,2.1c0,0,1,0.5,1-2c0,0,0.5-0.9,1.2-0.9l0.1,6.4
		c0,0-0.1,1.1,0.9,1.1c0,0,1.1,0.2,1.1-0.9c0,0,0-9.1,0-9.1c0-0.1,1.4,0.5,1.4,0.7c0.2,0.3,0.2,0.8,0.3,1.2c0.1,0.6,0,2.8,0.6,2.9
		c0.9,0.2,1-1.1,1.1-1.6c0.1-1.2-0.4-2.6-1.3-3.6c-0.2-0.2-2.1-1-2.1-0.9c0,0,0-5.1,0-5.1"/>
	<path opacity="0.2" d="M673.2,564.3l-1.5,7c0,0-2.3-0.4-3,1.5c0,0-0.4,1.7,0.4,2.2c0,0,0.9,0.7,1.4-1.7c0,0,0.7-0.7,1.4-0.6
		l-1.3,6.3c0,0-0.6,1,0.4,1.2c0,0,0.7,0.4,1-0.7c0,0,1.9-8.8,2-8.9c0-0.1,1.5,0.9,1.5,1c0.2,0.4,0.2,0.9,0.1,1.2
		c-0.1,0.6-0.5,2.7,0.1,3c0.8,0.4,1.3-0.8,1.4-1.3c0.3-1.1-0.1-2.7-0.8-3.9c-0.1-0.2-2.1-1.5-2.1-1.4c0,0,1-4.5,1-4.5"/>
	<path opacity="0.2" d="M590.2,591.3c-0.5,1.2-1,2.3-1.4,3.5c-0.2,0.5-0.4,1-0.6,1.5c-0.1,0.1-0.3,0.4-0.3,0.5
		c-0.1,0.5,0.7,0.5,1,0.5c0.2,0,0.4-0.2,0.6-0.2c0.3-0.1,0.6-0.1,0.9-0.1c0.3,0,0.7,0.1,1,0.2c0.3,0.1,0.5,0.2,0.8,0.3
		c0.9,0.1-0.2-0.9-0.4-1.1c-0.1-0.1-1.1-1-1.1-0.9c0.2-0.7,0.6-1.4,0.9-2.1c0.3-0.8,0.9-1.8,1.5-2.4c0.3-0.4-1.7-0.5-1.7-0.5
		C590.5,590.6,590.5,590.5,590.2,591.3z"/>
	<path opacity="0.2" d="M762.1,559.2l-1.4,5.2c0,0-2.3-0.6-2.9,0.8c0,0-0.4,1.2,0.4,1.7c0,0,0.9,0.6,1.4-1.1c0,0,0.7-0.5,1.3-0.3
		l-1.2,4.7c0,0-0.5,0.7,0.4,0.9c0,0,0.7,0.4,0.9-0.4c0,0,1.8-6.5,1.8-6.6c0-0.1,1.5,0.8,1.5,0.9c0.2,0.3,0.2,0.7,0.2,0.9
		c-0.1,0.5-0.5,2,0.1,2.3c0.8,0.4,1.3-0.5,1.4-0.8c0.3-0.8-0.1-2.1-0.8-3c-0.1-0.2-2.1-1.4-2.1-1.3c0,0,0.9-3.3,0.9-3.3"/>
	<line fill="none" x1="845" y1="521" x2="846" y2="521"/>
	<path opacity="0.3" fill="#C47826" d="M599.4,524c6-0.9,16.9,7.9,21.1,11.9c7.3,6.8,5,15,10.4,22.4c0.7-3.5,4.2-3.7,7.4-3.4
		c1.7-11.1-12.2-19.4-18.5-26.7c-2.3-2.7-3.6-4.8-6.9-6.6c-1.3-0.7-13.7-4.3-14-1c-0.1,1.7-0.3,4.2-0.1,5.4"/>
	<path opacity="0.3" fill="#C47826" d="M686.7,269.8c2.9-0.4,5-2.5,8.1-2.6c4.2-0.1,8,2.3,12.3,2.6c7,0.5,13.8-3.3,20.7-1.9
		c3.5,0.7,3.9,2.7,6.4,3.9c2.5,1.2,5.4,0.6,8,1.4c-0.8,5.6-12.6,4.2-15.9,2.8c-2.6-1.1-4.1-3.5-7-3.3c-2.2,0.2-5.5,2.3-8,2.7
		c-2.9,0.6-6.2,0.9-9.1,1c-6.6,0.3-12.9-2.6-19.5-2.1c-4,0.3-9.8,2.8-11.7-2.7c2.7-0.1,4.9-0.9,7.4-1.4
		C680.9,269.6,683.8,270.2,686.7,269.8z"/>
	<path opacity="0.3" fill="#C47826" d="M744.8,255.3c-3.8-1.9-7-1.3-10.7,0.2c1.8,1,3.9,0.7,5.7,1.7c1.3,0.7,2.7,1.6,3.9,2.5
		c1.3,1,2.4,2.3,3.2,3.7c0.2,0.4,1.9,4.5,1.6,4.5c6.9-1.1,4.8,8.9,0.9,11.3c-5.1,3.1-11.9,2.5-17.8,2.3c-15.1-0.6-30,3.6-45,3
		c-13.3-0.5-32.7-2-37.4-17.8c-7,3.2-27.2,4.8-14.6,17.4c6.3,6.3,14.1,4.1,22.2,3.9c10.4-0.2,20.9-0.7,31.3,0.1
		c14.3,1.2,28.8,2,43.1,1.4c7.3-0.3,14.4-1.5,21.5-2c5.6-0.5,13.1,1.3,17.2-3.6c3.4-4.1,2.9-12.8-2.3-15.1c-2.3-1-6.1-0.1-8.8-0.3
		c-4.4-0.3-4.1-1.1-6.1-4.1C750.7,261.1,748.3,257,744.8,255.3z"/>
	<path opacity="0.3" fill="#C47826" d="M731.5,246.1c0.4,0.9,1.4,2.3,1,3.2c-6.6-7.7-16.1-9.9-25.9-7.8c-2.8,0.6-3.9,2.8-6.8,2
		c-4-1.1-7.9-2-12.1-1.6c-5.8,0.5-11.2,3.6-15.2,7.6c1.3-5.8,5.4-8.7,10.1-11.6c2.7-1.7,5.8-2.6,9-2.9c2.5-0.2,5.3-0.6,7.8-0.3
		c2.4,0.3,4.2,1.3,6.6,0.8c2.5-0.5,5.2-0.7,7.8-0.5c4.9,0.4,10.5,2.4,13.6,6.3C728.6,242.9,730.6,244.4,731.5,246.1z"/>
	<path opacity="0.3" fill="#C47826" d="M616.2,322.7c2.7,0,5.8,0.2,6.7,3.1c0.5,1.8,0.5,4.3,2,5.8c1.5,1.4,4.9-0.4,5.9,1.7
		c0.8,1.6-1.9,3.5-3.1,4.3c-1.5,0.9-2.5,0.8-4.1,0.6c0.9,1.3,3.5,1,4,2.2c0.9,2-4.4,2.7-5.5,2.9c-4,0.8-8.2,1.1-11.7,3.3
		c4.4-0.1,8.7-1,13-1.2c3.1-0.1,6.1,0,9.2-0.1c2.5-0.1,4.7-0.7,7.1-1c2.3-0.4,5.3,0,6.2-2.6c0.6-1.6,0.3-3.5-0.5-4.9
		c-0.9-1.8-2-1.8-3.9-1.7c-0.9,0-2.3,0.8-3.1,0.5c-1.4-0.5-2.7-3.9-3.8-5c-1.2-1.2-2.8-1.2-4.3-1c-2,0.3-1.7,0.1-2.9-1.7
		c-1.1-1.6-3.1-4-4.9-4.7c-2.1-0.9-5.6-1.7-7.4,0c0.8,0,1.7-0.4,2.5-0.1"/>
	<path opacity="0.3" fill="#C47826" d="M357.5,115.2c7.3-0.6,13,3.6,18,8.3c5.3,5,3.4,9,3.2,15.5c5.2,0.2,16.1,2.7,10,10.1
		c-4.6,5.5-11.1,3.2-16.4,1.1c4.7,3.1-4.7,7.9-7.2,9c-5.6,2.4-12.2,2.8-18.1,3.5c-5.7,0.7-11.7-0.5-17.4-1.1
		c-5.8-0.6-11-0.7-16.3-3.5c-2.7-1.5-6.1-2.8-7.9-5.4c-1.1-1.6-0.8-4.2-2.4-5.2c-2.4-1.6-8.7-1-11.5-0.2c-3.5,1-7.1,3.5-7.6,7.3
		c-2.4,17.9,26.2,11.1,35.2,12.2c14.2,1.8,28.9-0.3,43.1,0.3c4.1,0.2,10.9,1.1,14.7,0.3c4-0.9,5.4-6.8,9.2-8.9
		c9.9-5.5,22.3-4.2,33.1-6.1c6.2-1.1,1.6-5.9-2.4-6.8c-1.9-0.4-4-0.7-6-0.2c-5.8,1.5-2.8-1.2-6-5.1c-2.2-2.6-6.5-7.4-10.2-7.7
		c-2.6-0.2-5,1.6-7.2-0.3c-2.3-1.9-2.2-6.2-3.9-8.6c-1.7-2.4-4.5-5.7-6.8-7.6c-6.3-5.3-13.6-2.1-20.6-0.8c-0.1,0.4,0.2,0.4,0.2,0.8"
		/>
	<path opacity="0.3" fill="#C47826" d="M658.7,426.7c-0.4-0.1-1.3-2.1-1.6-2.5c-1-1.2-1.9-1.7-3.5-1.3c0.9,0.5,2.5,0.8,2.7,2.1
		c0.2,1.6-1.2,1.7-2.4,2c2.2,1.4-3.4,2-4,2.1c-1.5,0.2-3.8-1.4-3.8-3.2c-1,0.1-1.2,0.8-2,1.2c-0.8,0.4-1.8,0.1-2.6,0.5
		c-1.8,0.8-0.5,2.6,0.8,3.4c1.3,0.7,3,0.3,4.4,0.3c2,0,4,0.1,6,0.4c1.4,0.2,2.6,0.7,4.1,0.8c1.6,0.1,3.1,0.2,4.6,0.4
		C662.2,430.2,661.9,427.1,658.7,426.7z"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M291.8,561.5c1.1,0,0.4,3.1,1.3,5.3"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M293.8,573.5c1.2,0.7,1.6,1.9,1.3,3.3"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="298" y1="566" x2="298" y2="570"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M571.8,562.1c0.1,3.2-0.4,6.2-0.7,9.3"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="566" y1="559" x2="566" y2="562"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M565.1,572.8c-0.1,1.8-0.3,3.6-0.7,5.3"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M510.4,694.1c3.1,2.4,7.4,2.4,10.7,4.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M517.1,691.5c-0.1,0.5,0.2,0.7,0.7,0.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M521.1,693.5c0.6,0.5,1.2,0.7,2,0.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M351.8,690.1c-0.6,1.3-1.9,2.3-3.3,2.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M357.8,693.5c-3.2,1.7-6.7,3.2-10,4.7"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="340" y1="696" x2="342" y2="696"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M304.4,713.5c2.8-6,7.2-18.7,14.7-20"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M559.8,713.5c-1.7-7.4-6.1-17-12.7-21.3"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M488,585.1c10.1,1.2,17.1-2.9,21.3-11.7c13.8,0.8,17.7-6.1,22.2-18.3"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M348.5,571.6c1.9,5.1,7.5,7.8,13.5,7.5"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M509,190.6c-5-3.7-12.8-1.7-13.8,5.8c-5.1-2.3-11.2-4.8-16.2,0.2"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M353,190.6c3.7-1.6,7.8-2,11.7-1.2c3.7,11.3,19.6-11.8,24.3,2.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M414.5,180.1c0-1.3,2.7-4,7.5-4.2c7.3-0.2,6.7,4.6,10.8,5.4c7.7,1.6,17.6-5.6,23.7,3.2"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M485,244.6c6.1-2.2,10.3-5.5,17.7-4.8c0.2-0.8,0.3-1.7,0.5-2.5c7.2,1,9.8-4.9,10.2-10.8"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M345.5,219.1c0.1,8.3,9.8,16.6,17.7,12.1c-1.7,11.8,9.6,12.2,18.3,11.8"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M401,228.1c12.7,10,28,18.1,44.7,9.2c4.3,10.1,22.6,3.4,25.8-4.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M413,210.1c5.5-2.3,11.8-1.5,18-1.5"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M487.9,175.8c-2.7-0.3-3,1.7-4.7,3c-1.5,1.2-4.6,0.2-5.4,1.2"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M447.8,155.6c-2.7-0.5-2.5,3-2.5,4.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M380,174.1c1.8,1.3,10.1,0.1,12.3,5.7c2.2,0.3,5,0.6,7.2,0.3"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M571.4,261.4c1.8-1.5,4.9-4.2,5.7-6.6"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M579.7,250.3c1.1-1.3,2-3.1,2-4.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M571.1,254.6c1.1-0.4,2.7-1.5,3.2-2.5"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M543.1,228.1c0.6,0.2,1.3,0.3,2,0.2"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M581.1,210c1.1-0.5,2.6-0.7,3.8-0.2"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M286.9,253.5c1.9,2.6,4.3,6.2,7,8"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M288.9,248.5c0.4,0.8,1.2,1.5,2,2"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M294.4,256c0.8,0.2,1.7,0.3,2.5,0.5"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M278.4,210.5c-2.2,0.8-2.8,4.2-2.5,6.5"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M311.9,229.5c1.2,0.7,3.2,0.5,5,0.5"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="322" y1="228" x2="324" y2="228"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="316" y1="233" x2="317" y2="233"/>
	<path fill="none" d="M575.8,449.5c0,0,2.9-2,3.6-3"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M569.2,464.5c-0.8,1.1-1.4,2.2-1.8,3.6"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M617.2,457.3c3.7,18.6-2.9,38.5-15.6,52.2"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M575.4,447.8c1.3-2.5,3.4-4.4,4.6-6.9"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M584.6,435.7c1.8-2.1,3-4.8,3.5-7.5"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M577.7,435.7c0.7-0.8,1.3-1.8,1.7-2.9"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M533.4,241.8c0,0.2,1,1.2,2.3,1.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M532.2,248.1c0.2,0.4,0.4,0.7,0.6,1.2"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M539.7,246.4c2.7,0.4,5.2,2.3,7.5,4"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M325.1,240.7c-1.7,0.8-3.5,1.7-4,3.5"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M323.4,247.6c1.2-0.9,2.6-1.6,4-1.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M314.7,253.3c0.3-0.7,0.9-1.1,1.7-1.2"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M425.8,342.5c2,0.9,5.8,0.9,7.5,0"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M438.4,341.9c1.6,0.2,3.1,0,4.6-0.6"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="432" y1="336" x2="436" y2="336"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M276.2,430.5c0.1,2.5,2,4.9,3.5,6.3"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M283.1,442.6c0.3,1.5,1.3,2.8,2.9,3.5"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M282.5,434c0,0.8,0.4,1.5,1.2,1.7"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="276" y1="264" x2="277" y2="264"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="280" y1="264" x2="281" y2="264"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="278" y1="269" x2="280" y2="269"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M581.1,264.8c1,0.1,2-0.1,2.9-0.6"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="579" y1="270" x2="581" y2="270"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="588" y1="268" x2="589" y2="268"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M604.5,522.3c0.5,0.2,1,0.7,1.3,1.1"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M608.4,525.6c0.5,0.5,1.1,0.9,1.6,1.3"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M604.8,525.4c0.5,0.1,0.9,0.4,1.3,0.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M618.7,560.8c0.5,0.2,1,0.3,1.5,0.5"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="623" y1="562" x2="625" y2="562"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M630.1,545.9c1.2,0.7,2.3,2.1,3,3"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M228.5,549.8c0.7-0.8,1.8-1.6,3-2"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="238" y1="565" x2="240" y2="565"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M241.9,565.2c-0.4-0.4,0.3-0.5,1-0.5"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="239" y1="562" x2="241" y2="562"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M322.4,660.5c2.9,6.9,11.8,11.9,19,13"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M330.4,660.5c0.5,0.7,1.2,1,2,1"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="338" y1="665" x2="341" y2="665"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M415.4,680.5c8.2,0,21.6-2.4,28,1"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M514.4,673.5c4.8,0.7,8.8-2.2,13-4"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M535.4,665.5c1.3-0.8,2.6-1.4,4-2"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M526.4,661.5c1.9-0.1,3.3-0.4,5-1"/>
	
		<circle fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" cx="435.4" cy="532" r="3.2"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M435.4,520.2
		c0,0,3.2,4.8,3.2,11.4"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M435.4,520.2
		c0,0-3.2,4.9-3.2,11.5"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M436.6,518.5
		c0.2,0.4,0.7,1.8,1.1,2.8c0.4,1.1,0.9,2.2,1.3,3.3c1,2.8,2.2,6.2,1.4,9.2c-0.5,1.9-2.1,2.7-3.9,2.9c-2.1,0.3-5.2,0.2-6.4-1.8
		c-0.9-1.5-0.8-3.5-0.5-5.2c0.3-1.7,0.8-3.4,1.4-5.1c0.4-1.1,0.9-2.2,1.3-3.3c0.5-1.1,1-2.2,1.5-3.3"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M433.2,512.6
		c0,0-4.1,4-1.2,9.7"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M437,512.6
		c0,0,4,4.1,1.1,9.7"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M433.9,512.9
		c0,0-1.7,3.9,1.6,7.3"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M437,512.9
		c0,0,1.7,3.9-1.6,7.3"/>
	
		<line fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="431.2" y1="536" x2="430.9" y2="639.5"/>
	
		<line fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="439.2" y1="535.7" x2="439.4" y2="639.5"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M479,529.6c7.3-11.8,17.8-9.6,29.8-12.2c1.1-12.7,13.7-21.7,25.4-16.4c2.1-8.8,10.1-13.9,18.3-10.3"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M273.5,483.1c6.6-1.4,18.7-5.7,25.6-3c8.5,3.4,7.8,11.6,15,15.1c4.5,2.3,13.1-2,19.8,2.5c7.2,4.8,11.4,10.9,11.9,19.6
		c13.4,0.2,34.9-0.2,37.2,16.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M285.5,525.1c7.6,8.7,22.4,21.2,36,11.9c6.4,6.5,14.9,10.2,24,10.6"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M398.4,549.5c0.3,8.1-2,12.8,5.8,16.5c-3.8,10.5,3.8,15.5,14.2,15.5"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M476.4,545.5c2,11.4-4.8,19.2-16.3,20.1c10.5,3.5,9.7,16.9-2.9,22.4c5.8,8.1,1.6,10.7-2.8,17.5"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M245,472.6c-3.3-5.5-2.3-12.2,4.2-15.3c-2.7-6.3-1-10.2,3.3-14.7"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M542.4,531.5c11.1,1.5,30.3-8.1,30-24"/>
	<path fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M285.5,205v-5.4c0-17.4-27-26.5-27-26.5l-55.8,3.1l-20.4,10.3H127l-23.9,14.8c-23.5,0-24.2,15.5-24.2,15.5s0,87.9,0,87.9
		c-11.3,2.5-23.2,0.2-24.1,13.6c-0.2,3.5,0.2,7.2,0.3,10.8c0.5,11.6,1.4,23.1,2.1,34.7c1.7,27.6,3.4,55.2,5.1,82.8
		c0.1,1.1,0.4,1.9,0.5,3"/>
	<path fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M230.5,174v31.6c0,0-21,0-21,5.3c0,5.3,0,28.8,0,28.8V398"/>
	
		<line fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="182.5" y1="187" x2="182.5" y2="334"/>
	<path fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M154.5,242v54.4c0,0-13.7-3-13.7,25.7c0,0-7.3-3-7.3,12.1V419"/>
	
		<polyline fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
		102.5,201 102.5,276 98.3,282.9 100.4,330.5 	"/>
	
		<line fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="78.5" y1="461" x2="78.5" y2="555"/>
	
		<line fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="111.5" y1="406" x2="111.5" y2="423"/>
	
		<line fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="249.5" y1="215" x2="249.5" y2="342"/>
	
		<line fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="182.5" y1="394" x2="182.5" y2="647"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M618.4,528.1l44.6-31.6v-86.2l3-2.8v-9.7c0-7.7,11.9-11.7,11.9-11.7l24.6,1.4l9,4.5h24.3l10,6.5c10.3,0,10.2,6.9,10.2,6.9v38.7
		l11,2.3v6.7l6,3.3v-26l2-5V410h7.4l1.5,6.7l3,5.8v9l-3,7v21l4,7v32.7l25.1,20.9H659"/>
	
		<polyline fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
		665.8,407.1 679,417.8 679,453 	"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M681.1,386.6l0.8,13.7c0,0,9.1,0.1,9.1,2.5c0,2.3,0,12.7,0,12.7v9.5"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="711" y1="392" x2="711" y2="457"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M723,416v24.5c0,0,6.4-1.3,6.4,11.3c0,0,3.6-1.3,3.6,5.3V494"/>
	
		<polyline fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
		746,398 746,431.5 748.1,434.5 747.1,455.2 	"/>
	
		<polyline fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
		772.8,456.4 777,462 777,483 	"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="766" y1="480" x2="766" y2="494"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="757" y1="453" x2="757" y2="494"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="742" y1="488" x2="742" y2="496"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="670" y1="467" x2="670" y2="496"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="691" y1="442" x2="691" y2="496"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="711" y1="483" x2="711" y2="496"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="663" y1="497" x2="773" y2="497"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M628.8,538.8c0.6-0.6,180.6,0,180.6,0"/>
	
		<polyline fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
		553.4,658.4 602.3,664.8 627.5,690 627.5,709 	"/>
	
		<polyline fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
		273.7,749.1 252.9,675.5 307.6,673.3 	"/>
	<path fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M253.3,675.5c3.2,0,50.2,54.6,50.2,54.6v24.4l-29.4-5.4"/>
	
		<polyline fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
		303,754.5 441.4,754.5 627.2,709 	"/>
	
		<polyline fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
		395.3,717.7 344.9,723.9 302.9,730.1 	"/>
	
		<polyline fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
		543.9,729.8 412.7,730 399.5,716.8 395.3,717.7 	"/>
	
		<line fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="399.5" y1="716.8" x2="390.1" y2="755"/>
	
		<line fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="412.7" y1="730" x2="412.4" y2="755"/>
	
		<polyline fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
		764.8,648.5 641.7,606.8 574.8,596.1 	"/>
	
		<line fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="297.1" y1="624.2" x2="125.2" y2="658.6"/>
	<path fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M705.5,234.9c0-0.2,0.7-0.4,1.4-0.5c2.6-0.4,5.1-0.4,7.7,0c4.1,0.6,7.9,2.2,11.1,4.8c4.6,3.7,7.4,9.1,9,14.7c0,0,17.3-3,19.2,14
		c6.3,0,15.6-1.2,18.1,6.5c2.2,6.8-1.9,11.1-7,12.9c-2.2,0.8-4.6,1.2-6.7,1.1c-12.3-0.3-24.6-0.2-36.9,0c-21.3-0.2-54.4-0.6-75.7,0
		c-2.2,0.1-4.5-0.3-6.7-1.1c-5.1-1.9-9.2-6.1-7-12.9c2.5-7.6,11.8-6.5,18.1-6.5c1.9-17,19.8-14,19.8-14
		C677.1,229.4,705.5,234.9,705.5,234.9L705.5,234.9z"/>
	<path fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M670.4,271.7c3.4,2.3,6.9,4.2,11.3,3.5c1.6-0.3,3.2-1.9,4.7-2.1c1.4-0.1,0.8,0.1,2,0.5c1.9,0.6,3.6,2.2,5.7,2.8
		c2.9,0.8,6.2,1,9.2,0.8c4.7-0.4,12.7-1.7,16.1-4.9c2.9,1.9,4.8,4.5,8.6,5.2c5.1,1,10.8-1.1,15.1-2.5"/>
	<path fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M380.7,167.5c-0.7,0-5.5,0-6.3,0c-21.3-0.2-54.4-0.6-75.7,0c-2.2,0.1-4.5-0.3-6.7-1.1c-5.1-1.9-9.2-6.1-7-12.9
		c2.5-7.6,11.8-6.5,18.1-6.5c1.9-17,19.8-14,19.8-14c7.2-24.6,35.6-19,35.6-19v0c0-0.2,0.7-0.4,1.4-0.5c2.6-0.4,5.1-0.4,7.7,0
		c4.1,0.6,7.9,2.2,11.1,4.8c4.6,3.7,7.4,9.1,9,14.7c0,0,17.3-3,19.2,14c7.1-3.8,12.9-0.8,16.2,3.4"/>
	<path fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M323.4,150.7c3.4,2.3,6.9,4.2,11.3,3.5c1.6-0.3,3.2-1.9,4.7-2.1c1.4-0.1,0.8,0.1,2,0.5c1.9,0.6,3.6,2.2,5.7,2.8
		c2.9,0.8,6.2,1,9.2,0.8c4.7-0.4,12.7-1.7,16.1-4.9c2.9,1.9,4.8,4.5,8.6,5.2c2.5,0.5,8.7,0.9,11.2,0.4"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M599.3,332.3c2.3-11.3,15-9.9,15-9.9h0c0-0.1,0.4-0.2,0.8-0.3c1.2-0.3,2.3-0.4,3.5-0.3c1.9,0.1,3.6,0.7,5.2,1.7
		c2.2,1.5,3.7,3.8,4.6,6.2c0,0,7.6-2,9.1,5.5c2.8-0.2,6.9-1.1,8.3,2.2c1.3,3-0.4,5-2.6,6.1c-0.9,0.4-2,0.7-3,0.8
		c-5.5,0.3-11,0.9-16.5,1.4c-6,0.5-14.1,1.1-21.6,1.7"/>
	
		<path fill="none" stroke="#211915" stroke-width="1.3466" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M601.3,340.2c1.6,0.9,3.2,1.6,5.2,1.1c0.7-0.2,1.4-1,2-1.1c0.6-0.1,0.4,0,0.9,0.1c0.9,0.2,1.7,0.8,2.7,1c1.4,0.3,2.8,0.2,4.1,0
		c2.1-0.3,5.6-1.3,7-2.8c1.4,0.7,2.3,1.8,4.1,2c2.3,0.2,4.8-0.9,6.6-1.7"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M661.5,433
		c-1.9-0.2-3.7-0.4-5.6-0.6c-3.7-0.4-9.4-1.1-13.1-1.4c-0.4,0-0.8-0.1-1.1-0.3c-0.9-0.4-1.5-1.2-1-2.4c0.6-1.3,2.2-0.9,3.3-0.8
		c0.6-2.9,3.6-2.1,3.6-2.1c1.7-4.1,6.5-2.6,6.5-2.6l0,0c0,0,0.2-0.1,0.3-0.1c0.5,0,0.9,0,1.4,0.2c0.7,0.2,1.3,0.5,1.8,1
		c0.7,0.7,1.1,1.7,1.3,2.7c0,0,2.2-0.1,2.9,1.7"/>
	<path fill="none" stroke="#211915" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M647.4,428.5c0.5,0.5,1.1,0.9,1.9,0.8c0.3,0,0.6-0.3,0.9-0.3c0.2,0,0.1,0,0.3,0.1c0.3,0.1,0.6,0.4,0.9,0.6c0.5,0.2,1,0.3,1.6,0.3
		c0.8,0,2.2-0.1,2.9-0.6c0.5,0.4,0.8,0.9,1.4,1.1c0.9,0.3,1.9,0,2.7-0.2"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M762.5,559v-6.2
		c0,0-2.6-0.1-2.9-2.1c0,0-0.3-1.7,0.4-2.1c0,0,0.7-0.5,0.7,2c0,0,0.4,0.9,1,0.9l0-6.4c0,0,0.4-1.1,1.3-1.1c0,0,1.4-0.2,1.4,0.9
		c0,0,0,9.1,0,9.1c0,0.1,0.6-0.5,0.7-0.7c0.2-0.3-0.1-0.8,0-1.2c0-0.6-0.1-2.8,0.4-2.9c0.7-0.2,0.8,1.1,0.9,1.6
		c0.1,1.2,0.2,2.6-0.7,3.6c-0.1,0.2-1.3,1-1.3,0.9c0,0,0,3.7,0,3.7"/>
	
		<path fill="none" stroke="#211915" stroke-width="0.7513" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M759.1,559.8c0.6,0,1.4,0.2,1.9,0.1c0.5-0.1,0.8-0.4,1.4-0.4c1.5,0.1,3.1,0.2,4.6,0.3c0.8,0.1,1.9-0.1,2.7,0"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M754.5,626v-6.2
		c0,0-2.6-0.1-2.9-2.1c0,0-0.3-1.7,0.4-2.1c0,0,0.7-0.5,0.7,2c0,0,0.4,0.9,1,0.9l0-6.4c0,0,0.4-1.1,1.3-1.1c0,0,1.4-0.2,1.4,0.9
		c0,0,0,9.1,0,9.1c0,0.1,0.6-0.5,0.7-0.7c0.2-0.3-0.1-0.8,0-1.2c0-0.6-0.1-2.8,0.4-2.9c0.7-0.2,0.8,1.1,0.9,1.6
		c0.1,1.2,0.2,2.6-0.7,3.6c-0.1,0.2-1.3,1-1.3,0.9c0,0,0,3.7,0,3.7"/>
	
		<path fill="none" stroke="#211915" stroke-width="0.7513" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M751.1,626.8c0.6,0,1.4,0.2,1.9,0.1c0.5-0.1,0.8-0.4,1.4-0.4c1.5,0.1,3.1,0.2,4.6,0.3c0.8,0.1,1.9-0.1,2.7,0"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M676.5,564v-6.2
		c0,0,2-0.1,2.2-2.1c0,0,0-1.7-0.7-2.1c0,0-0.9-0.5-0.9,2c0,0-0.4,0.9-1,0.9l-0.1-6.4c0,0,0.2-1.1-0.6-1.1c0,0-0.8-0.2-0.8,0.9
		c0,0,0,9.1,0,9.1c0,0.1-1.3-0.5-1.4-0.7c-0.2-0.3-0.3-0.8-0.3-1.2c0-0.6,0-2.8-0.6-2.9c-0.7-0.2-0.9,1.1-1,1.6
		c-0.1,1.2,0.5,2.6,1.3,3.6c0.1,0.2,1.9,1,1.9,0.9c0,0,0,3.7,0,3.7"/>
	
		<path fill="none" stroke="#211915" stroke-width="0.7513" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M678.6,564.8c-0.6,0-1.4,0.2-1.9,0.1c-0.5-0.1-0.8-0.4-1.4-0.4c-1.5,0.1-3.1,0.2-4.6,0.3c-0.8,0.1-1.9-0.1-2.7,0"/>
	<path fill="none" stroke="#211915" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M593.5,590v-6.2
		c0,0,2-0.1,2.2-2.1c0,0,0-1.7-0.7-2.1c0,0-0.9-0.5-0.9,2c0,0-0.4,0.9-1,0.9l-0.1-6.4c0,0,0.2-1.1-0.6-1.1c0,0-0.8-0.2-0.8,0.9
		c0,0,0,9.1,0,9.1c0,0.1-1.3-0.5-1.4-0.7c-0.2-0.3-0.3-0.8-0.3-1.2c0-0.6,0-2.8-0.6-2.9c-0.7-0.2-0.9,1.1-1,1.6
		c-0.1,1.2,0.5,2.6,1.3,3.6c0.1,0.2,1.9,1,1.9,0.9c0,0,0,3.7,0,3.7"/>
	
		<path fill="none" stroke="#211915" stroke-width="0.7513" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M595.6,590.8c-0.6,0-1.4,0.2-1.9,0.1c-0.5-0.1-0.8-0.4-1.4-0.4c-1.5,0.1-3.1,0.2-4.6,0.3c-0.8,0.1-1.9-0.1-2.7,0"/>
	
		<polygon fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
		706.4,665 685.4,665 680.8,691 693.6,690.9 722.3,673.4 	"/>
	
		<polyline fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
		686.4,666 699,680 719.9,674.8 	"/>
	
		<line fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="699" y1="680" x2="697.1" y2="688.8"/>
	
		<line fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="601.8" y1="664.9" x2="560.5" y2="725.8"/>
	<path fill="none" stroke="#211915" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M136.4,187l-0.5-19.8c0,0-7.2-0.3-7.9-6.3c0,0-0.7-5.3,1.6-6.3c0,0,2.4-1.5,2.4,5.9c0,0,1.2,2.6,3,2.6l0.2-19.4
		c0,0,0.7-3.2,3.2-3.2c0,0,3.6-0.7,3.6,2.7c0,0,0,27.4,0,27.6c0,0.2,2.6-1.6,2.8-2c0.6-1,0.2-2.4,0.2-3.5c0.1-1.8-0.2-8.4,1.4-8.8
		c2.2-0.6,2.6,3.2,2.7,4.8c0.3,3.5-0.2,8-2.6,10.9c-0.4,0.5-4.7,3.1-4.7,2.7c0,0-0.8,11.9-0.8,11.9"/>
	<ellipse transform="matrix(0.128 -0.9918 0.9918 0.128 -74.1595 701.2955)" cx="361.7" cy="392.8" rx="14.3" ry="14.3"/>
	<ellipse transform="matrix(0.128 -0.9918 0.9918 0.128 45.52 835.4486)" cx="497.8" cy="391.8" rx="14.3" ry="14.3"/>
	<path d="M438.1,441.2c-5.7,0.6-12.5-0.3-15.2-1.8c-1.6-0.9-3.2-2-4.1-3.5c-2.6-4.1,3-6.4,6.5-7.4c3.2-0.9,6.4-1.1,9.7-0.8
		c2.7,0.2,5.3,0.7,7.7,1.8c2.4,1.1,3.6,1.6,4.3,3.9C448.5,438.4,443.8,440.6,438.1,441.2z"/>
	
		<ellipse transform="matrix(0.128 -0.9918 0.9918 0.128 45.5048 835.4331)" fill="none" stroke="#000000" stroke-width="3.6235" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" cx="497.8" cy="391.8" rx="49" ry="49"/>
	
		<ellipse transform="matrix(0.128 -0.9918 0.9918 0.128 -72.0028 704.4983)" fill="none" stroke="#000000" stroke-width="3.6235" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" cx="364.6" cy="393.2" rx="49" ry="49"/>
	<ellipse transform="matrix(0.128 -0.9918 0.9918 0.128 -74.1595 701.2955)" cx="361.7" cy="392.8" rx="14.3" ry="14.3"/>
	<ellipse transform="matrix(0.128 -0.9918 0.9918 0.128 45.52 835.4486)" cx="497.8" cy="391.8" rx="14.3" ry="14.3"/>
	<ellipse transform="matrix(0.1279 -0.9918 0.9918 0.1279 -5.0822 559.8978)" cx="315.8" cy="282.8" rx="7.1" ry="7.1"/>
	
		<ellipse transform="matrix(0.128 -0.9918 0.9918 0.128 7.9834 574.7572)" fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" cx="330.8" cy="282.8" rx="24.3" ry="24.3"/>
	
		<path fill="none" stroke="#000000" stroke-width="3.6235" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M405.8,440c4.4-2.8,10.1-6,15.5-5.8c5.1,0.1,10.2,1.1,15.3,1.6c4.8,0.5,9.3,0.3,14,1.9c4.7,1.6,9.2,4.4,12.5,8.2
		c3.8,4.3,2.1,11.3-3,14.7c-9.6,6.3-18.6-4.7-28.5-4.1c-9.5,0.5-17.6,9.9-27.2,2.6C398,454.3,400,443.7,405.8,440z"/>
	
		<path fill="none" stroke="#000000" stroke-width="3.6235" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M431.8,458c-0.3,3.9-0.5,9.8,0,11.9c1,3.6,5.5,3.8,10.6,3.4c6.1-0.5,5-4.5,5-5.7c0-0.8-0.4-4.6-0.3-5.5"/>
	
		<path fill="none" stroke="#000000" stroke-width="3.6235" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M412.4,462.5c-0.3,3.9-0.3,5.8,0.2,7.9c1,3.6,5.5,3.8,10.6,3.4c6.1-0.5,7.7-1.3,7.7-2.8c0-1,0.5-12.1,0.6-13"/>
	
		<path fill="none" stroke="#000000" stroke-width="3.6235" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M438.1,441.2c-5.7,0.6-12.5-0.3-15.2-1.8c-1.6-0.9-3.2-2-4.1-3.5c-2.6-4.1,3-6.4,6.5-7.4c3.2-0.9,6.4-1.1,9.7-0.8
		c2.7,0.2,5.3,0.7,7.7,1.8c2.4,1.1,3.6,1.6,4.3,3.9C448.5,438.4,443.8,440.6,438.1,441.2z"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M261,410.5c-3.1-12.4-4.1-25.4-2.9-37.2c2.6-24.3,12.9-46.9,21.9-69.3c11.1-27.8,25.4-58,52.2-73.8"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M330.9,222c-10.2,2.1-22.6,3.5-32-2.4c-6.5-4.1-11.5-18.1-20.6-15.8c-7.2,1.8-7.7,14-7.4,19.5c0.6,10.5,3.9,20.8,9,30
		c2.6,4.7,8.9,16.6,14.2,18.3"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M286,546.9c-2.6,33.1,6.4,70.2,20.1,100.1c6,13.2,15.1,24.5,28.5,30.6c21.6,9.8,46.8,10.5,70.1,11.8c9.1,0.5,21,1.2,29.2,1.4"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M259.4,518.3c-6.9,4.3-17,12.7-21.8,17.4c-2.6,2.6-11.7,10.6-13,14.2c-2.5,6.8,7.5,2.4,8.8,8.5c1.5,6.9-5.1,11,5.3,10.7
		c5.8-0.2,11.4-2.1,16.7-4.1c8.7-3.2,20.5-5.3,30.2-10.6"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M317.5,664.9c-8.3,6.8-16.6,13.6-20.8,23.6c-5,11.8-0.9,27.8,13.1,28.6c9.8,0.5,27-6.3,35.6-10.5c8.8-4.3,23.5-13.1,31.8-18.9"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M283.3,259c-6.4,0-28.9,8-30.7,14.9c-3.2,12.2,24.7,15.7,32.6,17.2"/>
	<path fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M234.4,538.8c6.3,2.1,10.2,8.2,12.3,12.6c2.2,4.7,3,10.8,0.9,16.2"/>
	<path fill="none" stroke="#211915" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M628.8,538.8c-6.3,2.1-10.2,8.2-12.3,12.6c-2.2,4.7-3,10.8-0.9,16.2"/>
	<ellipse transform="matrix(0.9918 -0.1279 0.1279 0.9918 -31.6962 72.2437)" cx="546.5" cy="282.8" rx="7.1" ry="7.1"/>
	
		<ellipse transform="matrix(0.9918 -0.128 0.128 0.9918 -31.8144 70.6109)" fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" cx="533.5" cy="282.8" rx="24.3" ry="24.3"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M476.3,312.8c77.4-0.2,109,83,89.2,149.3"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M388.3,313.8c-77.4-0.2-109,83-89.2,149.3"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M432.7,288.4c9.9-0.2,20.7-0.6,29.5,3.5c10.5,4.9,17.9,23.8,13,34.9c-4.8,11-16.3,17-27.5,19.7c-18.9,4.5-49.7,0.8-58.7-19.7
		c-4.9-11.1,2.5-30,13-34.9C411.1,287.7,422.6,288.1,432.7,288.4"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M532.2,230.2c24.2,14.3,33.4,34.7,45.8,58c20.4,38.4,34.4,73.7,24.8,118.8"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M533.5,222.1c10.2,2.1,22.6,3.5,32-2.3c6.5-4.1,11.6-18.1,20.6-15.8c7.2,1.8,7.7,14,7.3,19.6c-0.6,10.5-4,20.7-9,30
		c-2.6,4.7-9.7,17.1-15,18.7"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M578.3,546.9c2.6,33.1-6.4,70.2-20.1,100.1c-6,13.2-15.1,24.5-28.5,30.6c-21.6,9.8-46.8,10.5-70.1,11.8c-9.1,0.5-17.4,1.2-25.7,1.4
		"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M604.9,518.3c6.9,4.3,17,12.7,21.8,17.4c2.6,2.6,11.7,10.6,13,14.2c2.5,6.8-7.5,2.4-8.8,8.5c-1.5,6.9,5.1,11-5.3,10.7
		c-5.8-0.2-11.4-2.1-16.7-4.1c-8.7-3.2-20.5-5.3-30.2-10.6"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M546.8,664.9c8.3,6.8,16.6,13.6,20.8,23.6c5,11.8,0.9,27.8-13.1,28.6c-9.8,0.5-27-6.3-35.6-10.5c-8.8-4.3-23.5-13.1-31.8-18.9"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M581,259.2c6.4,0,28.9,8.1,30.6,15c3.2,12.2-24.5,14.1-32.4,15.6"/>
	<path fill="none" stroke="#211915" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M294.8,462.1c27.8-1.2,51.6,19.5,64.3,42l-7-10.7c14.2-2.2,32.2,4.7,41.5,16c10.4-3,16.9,0.6,20.6,5.3c10.2-6.8,25.1-3.8,35,0
		c3.6-5,10.5-8.4,21.4-5.3c9.3-11.4,27.3-18.2,41.5-16l-7,10.7c12.7-22.6,36.5-43.3,64.3-42c18.5-14.8,29.8-30.7,33-54.6l-0.5,3
		c31.9-6.5,36.9,118.6-6.8,107.6l3.5,0.6c-1.8,22.1-31.5,38.1-51.5,37.4l3.2,0.1c-5.7,28.7-35.4,46.9-59.5,42.9
		c0,0.6-3.7,8.1-13,7.7c-7.4-0.3-3.8-6.7-7.3-10.7c-0.2,2.7-3.1,10.9-6.7,11.8c-3.7,0.9-3.9,1.4-12.4-0.9
		c-0.7,11.1-7.6,27.4-16.7,34.5c-8.2-5.6-18-24.8-20.4-34.8c-4.6,3.2-7.8,3.6-12.1,0.1c-2.4-1.9-8.3-8.2-8.5-10.8
		c-3.5,4,0,10.4-7.3,10.7c-4.5,0.2-9.9-5.7-10-10c-26.2-1.2-56.8-11.9-62.6-40.6l3.2-0.1c-20,0.7-49.7-15.3-51.5-37.4l3.5-0.6
		c-43.7,11-40-114.1-8.1-107.6c2.3,9.1,5.7,17.9,10.3,25.5C277.3,446.1,285.6,454.7,294.8,462.1z"/>
	
		<path fill="none" stroke="#211915" stroke-width="2.8817" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M168.3,187c69-71.8,166-116.4,273.4-116.4C651.2,70.5,821,240.3,821,449.8C821,659.2,651.2,829,441.7,829
		C232.3,829,62.5,659.2,62.5,449.8"/>
	<path fill="none" stroke="#211915" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M508.6,255.4c0,1.2-0.3,2.8-0.5,4.2c-0.4,2.9-1.4,5.7-2.2,8.5c5.9-6.6,12.4-10.7,16.5-19.3c4.6-9.6,11.1-15.9,11.1-26.7
		c-0.9-11.9-7-31.3-22.1-31.3c-4.4-9.4-17-17.2-24.2-15.5c-0.9-15.8-22.3-25.7-38.8-21.3l-0.1,0c-8.3-6.8-20.1-8.6-31.2,0
		c-16.7-4.5-38.2,5.6-38.6,21.7c-6.9-2.6-20.9,5.2-25.5,15c-15.1,0-21.2,19.4-22.1,31.3c0,10.8,6.5,17.1,11.1,26.7
		c4.1,8.7,10.6,12.8,16.5,19.3c-0.8-2.7-1.8-5.6-2.2-8.5c-0.2-1.4-0.5-3-0.5-4.2l0,0.1c3.2,6.8,3.4,12.8,9.5,17.8
		c5.4,4.5,12,6.4,17.9,8.8c-1.2-2-2.4-10,0-13.3c1.4,7,20.1,16.5,27.3,15.3c-1.2,0-1.3-8.5-1.3-11.3c6.4,0,16.9,6.7,23.1,10.2
		c6.2-3.5,16.6-10.3,23.1-10.2c0,2.8-0.1,11.4-1.3,11.3c7.3,1.1,25.9-8.3,27.3-15.3c2.4,3.3,1.2,11.3,0,13.3
		c5.8-2.4,12.4-4.4,17.9-8.8C505.2,268.3,505.3,262.3,508.6,255.4"/>
	
		<path fill="none" stroke="#211915" stroke-width="2.0849" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M467.6,315c-6.7,0.9-6.8,7.5-6.3,12.9"/>
	
		<path fill="none" stroke="#211915" stroke-width="2.0849" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M399.2,315c6.7,0.9,6.8,7.5,6.3,12.9"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M572.4,277.7c0,0,24.9-1.8,28.9,2.2"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M261.4,277.7c0,0,24.9-1.8,28.9,2.2"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M524.6,709.2c10.4-10.9,26.9-24.7,41.5-22.8"/>
	<path fill="none" stroke="#211915" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
		M339.1,709.2c-10.4-10.9-26.9-24.7-41.5-22.8"/>
</g>
<path fill="none" stroke="#000000" stroke-width="3.6235" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
	M431.5,468.1"/>
<path fill="none" stroke="#000000" stroke-width="3.6235" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
	M425.1,474.6"/>
</svg>
