.container {
  min-width: 320px;
}

header {
  background-color: #62a5ee;
  padding: 10px 20px;
  box-sizing: border-box;
}

.logo {
  img {
    width: 80px;
  }
}

.titles {
  h1 {
    font-size: 30px;
    font-weight: 300;
    color: white;
    margin-bottom: 13px;
    margin-top: 5px;
  }

  h2 {
    font-weight: 300;
    font-size: 18px;
    display: inline-block;
    margin: 0;
  }

  a {
    color: white;
    text-decoration: underline;
  }

  i {
    margin-right: 5px;
    text-decoration: none;
  }

  .documentation {
    margin-left: 28px;
  }
}

.subtitle {
  color: white;
  margin: 0;
  padding: 13px 0;
  background-color: #2a3543;
  margin-bottom: 20px;

  h3 {
    font-size: 22px;
    font-weight: 400;
    margin: 0;
  }
}

table {
  font-size: 14px;

  &.table tbody tr td {
    border-top: 0;
    padding: 10px;
  }
}

.foot {
  text-align: right;
  color: #c5c5c5;
  font-weight: 300;

  a {
    color: #8b8b8b;
    text-decoration: underline;
  }
}

.centered {
  text-align: center;
}

@media all and (max-width: 770px) {
  .titles {
    h1 {
      font-size: 25px;
      margin: 15px 0 5px 0;
    }
  }
}

@media all and (max-width: 640px) {
  .titles {
    h1 {
      font-size: 23px;
      margin: 15px 0 5px 0;
    }

    h2 {
      font-size: 15px;
    }

    .documentation {
      margin-left: 10px;
    }
  }
}

@media all and (max-width: 530px) {
  .titles {
    h1 {
      font-size: 20px;
      margin: 5px 0;
    }

    .documentation {
      margin-left: 0px;
      margin-top: 5px;
      display: block;
    }
  }

  .logo {
    padding: 0;

    img {
      width: 100%
    }
  }
}
