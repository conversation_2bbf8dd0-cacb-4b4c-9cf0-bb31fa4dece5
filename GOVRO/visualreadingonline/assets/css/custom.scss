/* Alpine.js x-cloak directive to prevent flickering */
[x-cloak] {
  display: none !important;
}

/* Navigation styles */
.nav-link {
  @apply py-2 hover:bg-blue-700 transition-colors duration-200;
  min-width: 120px; /* Set minimum width for all nav links */
  text-align: center; /* Center the text */
  border-radius: 0; /* Remove rounded corners */
  display: flex;
  justify-content: center;
  align-items: center;
}

.nav-link-active {
  @apply bg-blue-700;
}

.nav-link-inactive {
  @apply bg-transparent;
}

.dropdown-item {
  @apply block px-4 py-2 hover:bg-blue-700 transition-colors duration-200;
}

.dropdown-item-active {
  @apply bg-blue-700;
}

/* Navigation container for even spacing */
.nav-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0; /* Remove any padding */
}

/* Fade-in animation for main content */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

/* Make all nav items take equal space */
.nav-container > a,
.nav-container > div {
  flex: 1;
  margin: 0; /* Remove margins between items */
}

/* Round the corners of the first and last items */
.nav-container > a:first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.nav-container > a:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}
