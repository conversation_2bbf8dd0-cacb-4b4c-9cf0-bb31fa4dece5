create_table("users") {
  t.<PERSON>n("id", "uuid", {primary: true})
  t.<PERSON><PERSON>("email", "string", {})
  t.<PERSON><PERSON><PERSON>("password_hash", "string", {null: true})
  t.<PERSON><PERSON>n("first_name", "string", {null: true})
  t.<PERSON><PERSON>("last_name", "string", {null: true})
  t.<PERSON>("provider", "string", {})
  t.<PERSON><PERSON>("provider_id", "string", {null: true})
  t.<PERSON><PERSON>("magic_link_token", "string", {null: true})
  t.<PERSON>umn("magic_link_expiry", "timestamp", {null: true})
  t.<PERSON>umn("created_at", "timestamp", {})
  t.<PERSON><PERSON><PERSON>("updated_at", "timestamp", {})
  t.<PERSON>("email", {"unique": true})
}

create_table("contact_messages") {
  t.<PERSON>umn("id", "uuid", {primary: true})
  t.<PERSON><PERSON>("name", "string", {})
  t.<PERSON>("email", "string", {})
  t.<PERSON>("subject", "string", {})
  t.<PERSON><PERSON>("message", "text", {})
  t.<PERSON><PERSON>("created_at", "timestamp", {})
  t.<PERSON>umn("updated_at", "timestamp", {})
}
