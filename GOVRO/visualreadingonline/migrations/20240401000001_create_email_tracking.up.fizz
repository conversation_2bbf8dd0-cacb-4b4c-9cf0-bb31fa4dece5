create_table("email_trackings") {
  t.<PERSON><PERSON>("id", "uuid", {primary: true})
  t.<PERSON><PERSON>("tracking_id", "string", {})
  t.<PERSON>n("email", "string", {})
  t.<PERSON>n("template_name", "string", {})
  t.<PERSON><PERSON>("sent_at", "timestamp", {})
  t.<PERSON>("opened_at", "timestamp", {null: true})
  t.<PERSON>("created_at", "timestamp", {})
  t.<PERSON><PERSON>("updated_at", "timestamp", {})
  t.<PERSON>("tracking_id", {"unique": true})
}
