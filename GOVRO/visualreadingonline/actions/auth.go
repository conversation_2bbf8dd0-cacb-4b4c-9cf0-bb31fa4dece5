package actions

import (
	"encoding/json"
	"net/http"
	"strings"
	"time"
	"visualreadingonline/models"
	"visualreadingonline/services"

	"github.com/gobuffalo/buffalo"
	"github.com/gobuffalo/pop/v6"
	"github.com/gofrs/uuid"
	"github.com/pkg/errors"
	oauth2 "golang.org/x/oauth2"
	google "golang.org/x/oauth2/google"
)

// SignupHandler renders the signup page
func SignupHandler(c buffalo.Context) error {
	c.Set("current_route", "signup")
	c.<PERSON>("pageTitle", "Join Us - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("auth/signup.plush.html"))
}

// SignupPost handles the signup form submission
func SignupPost(c buffalo.Context) error {
	// Get the DB connection from the context
	tx, ok := c.Value("tx").(*pop.Connection)
	if !ok {
		return errors.New("no transaction found")
	}

	// Allocate an empty User
	user := &models.User{
		Provider: "email",
	}

	// Get form data
	email := c.Request().FormValue("email")
	password1 := c.Request().FormValue("password1")
	password2 := c.Request().FormValue("password2")

	// Check if a user with this email already exists
	existingUser := &models.User{}
	err := tx.Where("email = ?", strings.ToLower(email)).First(existingUser)
	if err == nil {
		// User exists
		if existingUser.Provider == "google" && existingUser.PasswordHash == "" {
			// This is a Google user without a password, let's update it
			existingUser.Password = password1
			existingUser.PasswordConfirmation = password2
			existingUser.Provider = "email"

			verrs, err := existingUser.Update(tx)
			if err != nil {
				return errors.WithStack(err)
			}

			if verrs.HasAny() {
				c.Set("errors", verrs)
				c.Set("user", existingUser)
				return c.Render(http.StatusUnprocessableEntity, r.HTML("auth/signup.plush.html"))
			}

			// Store the user ID in the session
			c.Session().Set("user_id", existingUser.ID.String())
			c.Flash().Add("success", "Your account has been updated with a password!")
			return c.Redirect(http.StatusSeeOther, "/")
		} else {
			// User already exists with a password
			c.Flash().Add("danger", "An account with this email already exists. Please log in instead.")
			return c.Redirect(http.StatusSeeOther, "/login")
		}
	}

	// Set user fields for new user
	user.Email = email
	user.Password = password1
	user.PasswordConfirmation = password2

	// Create the user
	verrs, err := user.Create(tx)
	if err != nil {
		return errors.WithStack(err)
	}

	if verrs.HasAny() {
		c.Set("errors", verrs)
		c.Set("user", user)
		return c.Render(http.StatusUnprocessableEntity, r.HTML("auth/signup.plush.html"))
	}

	// Store the user ID in the session
	c.Session().Set("user_id", user.ID.String())
	c.Flash().Add("success", "Welcome to Visual Reading Online!")

	// Send welcome email
	emailService := services.NewEmailService()
	go emailService.SendWelcomeEmail(user) // Send asynchronously to not block the response

	// Redirect to the home page
	return c.Redirect(http.StatusSeeOther, "/")
}

// LoginHandler renders the login page
func LoginHandler(c buffalo.Context) error {
	c.Set("current_route", "login")
	c.Set("pageTitle", "Log In - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("auth/login.plush.html"))
}

// LoginPost handles the login form submission
func LoginPost(c buffalo.Context) error {
	// Get the DB connection from the context
	tx, ok := c.Value("tx").(*pop.Connection)
	if !ok {
		return errors.New("no transaction found")
	}

	// Allocate an empty User
	user := &models.User{}

	// Get the email and password from the form
	email := c.Request().FormValue("email")
	password := c.Request().FormValue("password")

	// Find the user by email (regardless of provider)
	err := tx.Where("email = ?", strings.ToLower(email)).First(user)
	if err != nil {
		c.Flash().Add("danger", "Invalid email or password")
		return c.Redirect(http.StatusSeeOther, "/login")
	}

	// If the user signed up with Google and doesn't have a password yet
	if user.Provider == "google" && user.PasswordHash == "" {
		c.Flash().Add("info", "You previously signed up with Google. Please use the 'Log in with Google' button or set a password using the 'Forgot Password' link.")
		return c.Redirect(http.StatusSeeOther, "/login")
	}

	// Check the password
	if !user.Authenticate(password) {
		c.Flash().Add("danger", "Invalid email or password")
		return c.Redirect(http.StatusSeeOther, "/login")
	}

	// Store the user ID in the session
	c.Session().Set("user_id", user.ID.String())
	c.Flash().Add("success", "Welcome back!")

	// Redirect to the home page
	return c.Redirect(http.StatusSeeOther, "/")
}

// LogoutHandler logs a user out
func LogoutHandler(c buffalo.Context) error {
	// Clear the session
	c.Session().Clear()
	c.Flash().Add("success", "You have been logged out successfully!")
	return c.Redirect(http.StatusSeeOther, "/")
}

// RequestMagicLinkHandler renders the request magic link page
func RequestMagicLinkHandler(c buffalo.Context) error {
	c.Set("current_route", "request_magic_link")
	c.Set("pageTitle", "Request Magic Link - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("auth/request_magic_link.plush.html"))
}

// RequestMagicLinkPostHandler handles the form submission for requesting a magic link
func RequestMagicLinkPostHandler(c buffalo.Context) error {
	// Get the DB connection from the context
	tx, ok := c.Value("tx").(*pop.Connection)
	if !ok {
		return errors.New("no transaction found")
	}

	// Get the email from the form
	email := c.Request().FormValue("email")
	if email == "" {
		c.Flash().Add("danger", "Email is required")
		return c.Redirect(http.StatusSeeOther, "/request-magic-link")
	}

	// Find the user by email
	user := &models.User{}
	err := tx.Where("email = ?", strings.ToLower(email)).First(user)
	if err == nil {
		// Generate a magic link token
		token, err := user.GenerateMagicLink(tx)
		if err == nil {
			// Send the magic link email
			err = sendMagicLinkEmail(user.Email, token)
		}
	}

	// Always show the same message to prevent email enumeration
	c.Flash().Add("success", "If your email is registered, you will receive a magic link shortly.")
	return c.Redirect(http.StatusSeeOther, "/login")
}

// MagicLinkHandler handles the magic link authentication
func MagicLinkHandler(c buffalo.Context) error {
	// Get the DB connection from the context
	tx, ok := c.Value("tx").(*pop.Connection)
	if !ok {
		return errors.New("no transaction found")
	}

	// Get the token from the URL
	token := c.Param("token")
	if token == "" {
		c.Flash().Add("danger", "Invalid magic link")
		return c.Redirect(http.StatusSeeOther, "/login")
	}

	// Find the user by token
	user := &models.User{}
	err := tx.Where("magic_link_token = ?", token).First(user)
	if err != nil || !user.ValidateMagicLink(token) {
		c.Flash().Add("danger", "Invalid or expired magic link")
		return c.Redirect(http.StatusSeeOther, "/login")
	}

	// Clear the token
	user.MagicLinkToken = ""
	user.MagicLinkExpiry = time.Time{}
	err = tx.Update(user)
	if err != nil {
		return errors.WithStack(err)
	}

	// Store the user ID in the session
	c.Session().Set("user_id", user.ID.String())
	c.Flash().Add("success", "You have been logged in successfully!")

	// Redirect to the home page
	return c.Redirect(http.StatusSeeOther, "/")
}

// GoogleAuthHandler initiates the Google OAuth flow
func GoogleAuthHandler(c buffalo.Context) error {
	// Create the Google OAuth2 config
	googleOauthConfig := &oauth2.Config{
		RedirectURL:  "http://localhost:8000/auth/google/callback", // Update for production
		ClientID:     "************-5r51hbu6gqkl5ps9ona179ub023d2lsn.apps.googleusercontent.com",
		ClientSecret: "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",
		Scopes:       []string{"https://www.googleapis.com/auth/userinfo.email", "https://www.googleapis.com/auth/userinfo.profile"},
		Endpoint:     google.Endpoint,
	}

	// Store the state in the session
	state := uuid.Must(uuid.NewV4()).String()
	c.Session().Set("oauth_state", state)

	// Redirect to the Google OAuth URL
	url := googleOauthConfig.AuthCodeURL(state)
	return c.Redirect(http.StatusTemporaryRedirect, url)
}

// GoogleCallbackHandler handles the Google OAuth callback
func GoogleCallbackHandler(c buffalo.Context) error {
	// Get the DB connection from the context
	tx, ok := c.Value("tx").(*pop.Connection)
	if !ok {
		return errors.New("no transaction found")
	}

	// Verify the state
	state := c.Param("state")
	sessionState := c.Session().Get("oauth_state")
	if state != sessionState || state == "" {
		c.Flash().Add("danger", "Invalid OAuth state")
		return c.Redirect(http.StatusSeeOther, "/login")
	}

	// Create the Google OAuth2 config
	googleOauthConfig := &oauth2.Config{
		RedirectURL:  "http://localhost:8000/auth/google/callback", // Update for production
		ClientID:     "************-5r51hbu6gqkl5ps9ona179ub023d2lsn.apps.googleusercontent.com",
		ClientSecret: "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",
		Scopes:       []string{"https://www.googleapis.com/auth/userinfo.email", "https://www.googleapis.com/auth/userinfo.profile"},
		Endpoint:     google.Endpoint,
	}

	// Exchange the code for a token
	code := c.Param("code")
	token, err := googleOauthConfig.Exchange(c, code)
	if err != nil {
		c.Flash().Add("danger", "Failed to exchange OAuth code")
		return c.Redirect(http.StatusSeeOther, "/login")
	}

	// Get the user info
	client := googleOauthConfig.Client(c, token)
	resp, err := client.Get("https://www.googleapis.com/oauth2/v2/userinfo")
	if err != nil || resp.StatusCode != http.StatusOK {
		c.Flash().Add("danger", "Failed to get user info from Google")
		return c.Redirect(http.StatusSeeOther, "/login")
	}
	defer resp.Body.Close()

	// Parse the user info
	var userInfo struct {
		ID            string `json:"id"`
		Email         string `json:"email"`
		VerifiedEmail bool   `json:"verified_email"`
		Name          string `json:"name"`
		GivenName     string `json:"given_name"`
		FamilyName    string `json:"family_name"`
		Picture       string `json:"picture"`
		Locale        string `json:"locale"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		c.Flash().Add("danger", "Failed to parse user info from Google")
		return c.Redirect(http.StatusSeeOther, "/login")
	}

	// First, check if a user with this email already exists (regardless of provider)
	user := &models.User{}
	err = tx.Where("email = ?", userInfo.Email).First(user)

	if err != nil {
		// No user with this email exists, create a new one
		user = &models.User{
			Email:      userInfo.Email,
			FirstName:  userInfo.GivenName,
			LastName:   userInfo.FamilyName,
			Provider:   "google",
			ProviderID: userInfo.ID,
		}

		verrs, err := tx.ValidateAndCreate(user)
		if err != nil || verrs.HasAny() {
			c.Flash().Add("danger", "Failed to create user account")
			return c.Redirect(http.StatusSeeOther, "/login")
		}
	} else {
		// User exists, update their Google credentials if needed
		updateNeeded := false

		// If they don't have Google provider info, add it
		if user.Provider != "google" && user.ProviderID == "" {
			user.Provider = "google"
			user.ProviderID = userInfo.ID
			updateNeeded = true
		}

		// Update name if it's not set
		if user.FirstName == "" && userInfo.GivenName != "" {
			user.FirstName = userInfo.GivenName
			updateNeeded = true
		}

		if user.LastName == "" && userInfo.FamilyName != "" {
			user.LastName = userInfo.FamilyName
			updateNeeded = true
		}

		// Save updates if needed
		if updateNeeded {
			err = tx.Update(user)
			if err != nil {
				c.Flash().Add("danger", "Failed to update user account")
				return c.Redirect(http.StatusSeeOther, "/login")
			}
		}
	}

	// Store the user ID in the session
	c.Session().Set("user_id", user.ID.String())
	c.Flash().Add("success", "You have been logged in successfully!")

	// Redirect to the home page
	return c.Redirect(http.StatusSeeOther, "/")
}

// Helper function to send a magic link email
func sendMagicLinkEmail(email, token string) error {
	// Create a new email service
	emailService := services.NewEmailService()

	// Find the user by email
	user := &models.User{Email: email}

	// Send the magic link email
	return emailService.SendMagicLinkEmail(user, token)
}
