package actions

import (
	"net/http"

	"github.com/gobuffalo/buffalo"
)

// LearnMoreHandler renders the learn more page
func LearnMoreHandler(c buffalo.Context) error {
	// Set the current route for the template
	c.Set("current_route", "learn_more")
	// Set the page title
	c.Set("pageTitle", "Learn More - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("learn_more/index.plush.html"))
}
