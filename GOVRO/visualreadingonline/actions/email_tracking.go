package actions

import (
	"net/http"
	"visualreadingonline/models"

	"github.com/gobuffalo/buffalo"
	"github.com/gobuffalo/pop/v6"
	"github.com/pkg/errors"
)

// EmailTrackingHandler handles email tracking pixel requests
func EmailTrackingHandler(c buffalo.Context) error {
	// Get the tracking ID from the URL
	trackingID := c.Param("tracking_id")
	if trackingID == "" {
		return c.Error(http.StatusBadRequest, errors.New("tracking ID is required"))
	}

	// Get the DB connection from the context
	tx := c.Value("tx").(*pop.Connection)

	// Find the email tracking record
	emailTracking := &models.EmailTracking{}
	err := tx.Where("tracking_id = ?", trackingID).First(emailTracking)
	if err != nil {
		// Return a 1x1 transparent pixel even if the tracking ID is not found
		// This prevents leaking information about valid tracking IDs
		c.Response().Header().Set("Content-Type", "image/gif")
		c.Response().WriteHeader(http.StatusOK)
		c.Response().Write(transparentPixel())
		return nil
	}

	// Mark the email as opened
	err = emailTracking.MarkAsOpened(tx)
	if err != nil {
		// Log the error but still return the pixel
		// This prevents the user from seeing an error
		c.Logger().Error(err)
	}

	// Return a 1x1 transparent pixel
	c.Response().Header().Set("Content-Type", "image/gif")
	c.Response().WriteHeader(http.StatusOK)
	c.Response().Write(transparentPixel())
	return nil
}

// transparentPixel returns a 1x1 transparent GIF
func transparentPixel() []byte {
	// This is a 1x1 transparent GIF
	return []byte{
		0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x01, 0x00, 0x01, 0x00, 0x80, 0x00, 0x00, 0xff, 0xff, 0xff,
		0x00, 0x00, 0x00, 0x21, 0xf9, 0x04, 0x01, 0x00, 0x00, 0x00, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x00,
		0x01, 0x00, 0x01, 0x00, 0x00, 0x02, 0x02, 0x44, 0x01, 0x00, 0x3b,
	}
}
