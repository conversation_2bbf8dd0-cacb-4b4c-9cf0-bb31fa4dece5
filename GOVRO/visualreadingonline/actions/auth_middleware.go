package actions

import (
	"net/http"
	"visualreadingonline/models"

	"github.com/gobuffalo/buffalo"
	"github.com/gobuffalo/pop/v6"
	"github.com/gofrs/uuid"
)

// SetCurrentUser attempts to find a user based on the user_id in the session.
// If one is found it is set on the context.
func SetCurrentUser(next buffalo.Handler) buffalo.Handler {
	return func(c buffalo.Context) error {
		// Attempt to get the user ID from the session
		userID := c.Session().Get("user_id")
		if userID == nil {
			return next(c)
		}

		// Try to convert the user ID to a UUID
		uid, err := uuid.FromString(userID.(string))
		if err != nil {
			c.Session().Clear()
			return next(c)
		}

		// Get the DB connection from the context
		tx := c.Value("tx").(*pop.Connection)

		// Find the user by ID
		user := &models.User{}
		err = tx.Find(user, uid)
		if err != nil {
			c.Session().Clear()
			return next(c)
		}

		// Set the user on the context
		c.Set("current_user", user)
		return next(c)
	}
}

// Authorize requires a user to be logged in before accessing a route.
func Authorize(next buffalo.Handler) buffalo.Handler {
	return func(c buffalo.Context) error {
		// Get the current user from the context
		user, ok := c.Value("current_user").(*models.User)
		if !ok || user == nil {
			// If there's no user, redirect to the login page
			c.Flash().Add("danger", "You must be logged in to access that page")
			return c.Redirect(http.StatusSeeOther, "/login")
		}

		// If there is a user, call the next handler
		return next(c)
	}
}
