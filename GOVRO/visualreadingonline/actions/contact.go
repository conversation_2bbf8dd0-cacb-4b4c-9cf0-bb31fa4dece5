package actions

import (
	"net/http"
	"visualreadingonline/models"

	"github.com/gobuffalo/buffalo"
	"github.com/gobuffalo/pop/v6"
	"github.com/pkg/errors"
)

// ContactHandler renders the contact page
func ContactHandler(c buffalo.Context) error {
	c.Set("current_route", "contact")
	c.<PERSON>("pageTitle", "Contact Us - Visual Reading Online")
	return c.<PERSON>(http.StatusOK, r.HTML("support/contact.plush.html"))
}

// ContactMessageCreate processes the contact form submission
func ContactMessageCreate(c buffalo.Context) error {
	// Create a new empty ContactMessage
	contactMessage := &models.ContactMessage{}

	// Get form data
	firstName := c.Request().FormValue("first_name")
	lastName := c.Request().FormValue("last_name")
	email := c.Request().FormValue("email")
	subject := c.Request().FormValue("subject")
	message := c.Request().FormValue("message")

	// Combine first and last name
	contactMessage.Name = firstName + " " + lastName
	contactMessage.Email = email
	contactMessage.Subject = subject
	contactMessage.Message = message

	// Get the DB connection from the context
	tx := c.Value("tx").(*pop.Connection)

	// Validate the data from the form
	verrs, err := tx.ValidateAndCreate(contactMessage)
	if err != nil {
		return errors.WithStack(err)
	}

	if verrs.HasAny() {
		// Make the errors available inside the html template
		c.Set("errors", verrs)
		c.Set("contact_message", contactMessage)

		// Render the form again
		return c.Render(http.StatusUnprocessableEntity, r.HTML("support/contact.plush.html"))
	}

	// If there are no errors, redirect to the contact success page
	c.Flash().Add("success", "Your message has been sent. We'll get back to you soon!")
	return c.Redirect(http.StatusSeeOther, "/contact-success")
}

// ContactSuccessHandler renders the contact success page
func ContactSuccessHandler(c buffalo.Context) error {
	c.Set("current_route", "contact_success")
	c.Set("pageTitle", "Message Sent - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("support/contact_success.plush.html"))
}
