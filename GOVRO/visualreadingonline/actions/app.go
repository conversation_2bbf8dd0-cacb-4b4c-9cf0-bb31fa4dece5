package actions

import (
"net/http"

"visualreadingonline/locales"
"visualreadingonline/models"
"visualreadingonline/public"

"github.com/gobuffalo/buffalo"
"github.com/gobuffalo/buffalo-pop/v3/pop/popmw"
"github.com/gobuffalo/envy"
"github.com/gobuffalo/middleware/csrf"
"github.com/gobuffalo/middleware/forcessl"
"github.com/gobuffalo/middleware/i18n"
"github.com/gobuffalo/middleware/paramlogger"
"github.com/unrolled/secure"
)

// ENV is used to help switch settings based on where the
// application is being run. Default is "development".
var ENV = envy.Get("GO_ENV", "development")
var app *buffalo.App
var T *i18n.Translator

// App is where all routes and middleware for buffalo
// should be defined. This is the nerve center of your
// application.
//
// Routing, middleware, groups, etc... are declared TOP -> DOWN.
// This means if you add a middleware to `app` *after* declaring a
// group, that group will NOT have that new middleware. The same
// is true of resource declarations as well.
//
// It also means that routes are checked in the order they are declared.
// `ServeFiles` is a CATCH-ALL route, so it should always be
// placed last in the route declarations, as it will prevent routes
// declared after it to never be called.
func App() *buffalo.App {
if app == nil {
app = buffalo.New(buffalo.Options{
Env:         ENV,
SessionName: "_visualreadingonline_session",
})

// Automatically redirect to SSL
app.Use(forceSSL())

// Log request parameters (filters apply).
app.Use(paramlogger.ParameterLogger)

// Protect against CSRF attacks. https://www.owasp.org/index.php/Cross-Site_Request_Forgery_(CSRF)
// Remove to disable this.
app.Use(csrf.New)

// Setup and use translations:
app.Use(translations())

// Setup database middleware
app.Use(popmw.Transaction(models.DB))

// Setup authentication middleware
app.Use(SetCurrentUser)

// Main navigation routes
app.GET("/", HomeHandler)
app.GET("/learn-more", LearnMoreHandler)
app.GET("/get-started", GetStartedHandler)

// Get Started sub-routes
app.GET("/get-started/private-students", GetStartedPrivateStudentsHandler)
app.GET("/get-started/private-consultation", GetStartedPrivateConsultationHandler)
app.GET("/get-started/gift-a-visual-reading-course", GetStartedGiftHandler)
app.GET("/get-started/organisations", GetStartedOrganisationsHandler)
app.GET("/get-started/coaches", GetStartedCoachesHandler)
app.GET("/get-started/dsa-students", GetStartedDSAStudentsHandler)
app.GET("/pay-per-view", PayPerViewHandler)

// Resources routes
app.GET("/dr-cooper", DrCooperHandler)
app.GET("/articles-and-blogs", ArticlesAndBlogsHandler)
app.GET("/articles", ArticlesHandler)
app.GET("/blog-posts", BlogPostsHandler)
app.GET("/faqs", FAQsHandler)
app.GET("/our-results", OurResultsHandler)
app.GET("/products", ProductsHandler)
app.GET("/student-feedback", StudentFeedbackHandler)
app.GET("/useful-links", UsefulLinksHandler)

// Support routes
app.GET("/support", SupportHandler)
app.GET("/contact", ContactHandler)
app.POST("/contact", ContactMessageCreate)
app.GET("/contact-success", ContactSuccessHandler)

// Auth routes
app.GET("/signup", SignupHandler)
app.POST("/signup", SignupPost)
app.GET("/login", LoginHandler)
app.POST("/login", LoginPost)
app.GET("/logout", LogoutHandler)
app.GET("/request-magic-link", RequestMagicLinkHandler)
app.POST("/request-magic-link", RequestMagicLinkPostHandler)
app.GET("/auth/magic-link/{token}", MagicLinkHandler)
app.GET("/auth/google", GoogleAuthHandler)
app.GET("/auth/google/callback", GoogleCallbackHandler)

// Legal routes
app.GET("/terms-of-service", TermsOfServiceHandler)
app.GET("/privacy-policy", PrivacyPolicyHandler)

// Email tracking route
app.GET("/email-tracking/{tracking_id}", EmailTrackingHandler)

app.ServeFiles("/", http.FS(public.FS())) // serve files from the public directory
}

return app
}

// translations will load locale files, set up the translator `actions.T`,
// and will return a middleware to use to load the correct locale for each
// request.
// for more information: https://gobuffalo.io/en/docs/localization
func translations() buffalo.MiddlewareFunc {
var err error
if T, err = i18n.New(locales.FS(), "en-US"); err != nil {
app.Stop(err)
}
return T.Middleware()
}

// forceSSL will return a middleware that will redirect an incoming request
// if it is not HTTPS. "http://example.com" => "https://example.com".
// This middleware does **not** enable SSL. for your application. To do that
// we recommend using a proxy: https://gobuffalo.io/en/docs/proxy
// for more information: https://github.com/unrolled/secure/
func forceSSL() buffalo.MiddlewareFunc {
return forcessl.Middleware(secure.Options{
SSLRedirect:     ENV == "production",
SSLProxyHeaders: map[string]string{"X-Forwarded-Proto": "https"},
})
}
