package actions

import (
	"net/http"

	"github.com/gobuffalo/buffalo"
)

// DrCooperHandler renders the Dr<PERSON> page
func DrCooperHandler(c buffalo.Context) error {
	c.Set("current_route", "dr_cooper")
	c.<PERSON>("pageTitle", "About Dr. Cooper - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("resources/dr_cooper.plush.html"))
}

// ArticlesAndBlogsHandler renders the Articles and Blogs page
func ArticlesAndBlogsHandler(c buffalo.Context) error {
	c.Set("current_route", "articles_and_blogs")
	c.<PERSON>("pageTitle", "Articles & Blogs - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("resources/articles_and_blogs.plush.html"))
}

// FAQsHandler renders the FAQs page
func FAQsHandler(c buffalo.Context) error {
	c.Set("current_route", "faqs")
	c.<PERSON>("pageTitle", "FAQs - Visual Reading Online")
	return c.Render(http.Status<PERSON>, r.HTML("resources/faqs.plush.html"))
}

// OurResultsHandler renders the Our Results page
func OurResultsHandler(c buffalo.Context) error {
	c.Set("current_route", "our_results")
	c.Set("pageTitle", "Our Results - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("resources/our_results.plush.html"))
}

// ProductsHandler renders the Products page
func ProductsHandler(c buffalo.Context) error {
	c.Set("current_route", "products")
	c.Set("pageTitle", "Products & Services - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("resources/products.plush.html"))
}

// StudentFeedbackHandler renders the Student Feedback page
func StudentFeedbackHandler(c buffalo.Context) error {
	c.Set("current_route", "student_feedback")
	c.Set("pageTitle", "Student Feedback - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("resources/student_feedback.plush.html"))
}

// UsefulLinksHandler renders the Useful Links page
func UsefulLinksHandler(c buffalo.Context) error {
	c.Set("current_route", "useful_links")
	c.Set("pageTitle", "Useful Links - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("resources/useful_links.plush.html"))
}

// ArticlesHandler renders the Articles index page
func ArticlesHandler(c buffalo.Context) error {
	c.Set("current_route", "articles")
	c.Set("pageTitle", "Articles - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("resources/articles.plush.html"))
}

// BlogPostsHandler renders the Blog Posts index page
func BlogPostsHandler(c buffalo.Context) error {
	c.Set("current_route", "blog_posts")
	c.Set("pageTitle", "Blog Posts - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("resources/blog_posts.plush.html"))
}
