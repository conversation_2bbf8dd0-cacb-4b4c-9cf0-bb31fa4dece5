package actions

import (
	"net/http"

	"github.com/gobuffalo/buffalo"
)

// LegalStatementHandler renders the legal statement page
func LegalStatementHandler(c buffalo.Context) error {
	c.Set("current_route", "legal_statement")
	c.<PERSON>("pageTitle", "Legal Statement - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTM<PERSON>("legal/legal_statement.plush.html"))
}

// TermsOfServiceHandler renders the terms of service page
func TermsOfServiceHandler(c buffalo.Context) error {
	c.Set("current_route", "terms_of_service")
	c.<PERSON>("pageTitle", "Terms of Service - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("legal/terms_of_service.plush.html"))
}

// PrivacyPolicyHandler renders the privacy policy page
func PrivacyPolicyHandler(c buffalo.Context) error {
	c.Set("current_route", "privacy_policy")
	c.<PERSON>("pageTitle", "Privacy Policy - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("legal/privacy_policy.plush.html"))
}
