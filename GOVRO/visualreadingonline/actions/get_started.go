package actions

import (
	"net/http"

	"github.com/gobuffalo/buffalo"
)

// GetStartedHandler renders the get started page
func GetStartedHandler(c buffalo.Context) error {
	// Set the current route for the template
	c.Set("current_route", "get_started")
	// Set the page title
	c.Set("pageTitle", "Get Started - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("get_started/index.plush.html"))
}
