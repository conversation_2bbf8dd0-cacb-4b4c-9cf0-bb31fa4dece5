package actions

import (
	"net/http"

	"github.com/gobuffalo/buffalo"
)

// HomeHandler is a default handler to serve up
// a home page.
func HomeHandler(c buffalo.Context) error {
	// Set the current route for the template
	c.Set("current_route", "index")
	// Set the page title
	c.Set("pageTitle", "Home - Visual Reading Online")
	return c<PERSON>der(http.StatusOK, r.HTML("home/index.plush.html"))
}
