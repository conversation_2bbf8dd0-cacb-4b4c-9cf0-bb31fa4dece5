package actions

import (
	"net/http"

	"github.com/gobuffalo/buffalo"
)

// GetStartedPrivateStudentsHandler is a handler for the /get-started/private-students page
func GetStartedPrivateStudentsHandler(c buffalo.Context) error {
	c.Set("current_route", "get_started_private_students")
	c.<PERSON>("pageTitle", "Private Students - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("get_started/private_students.plush.html"))
}

// GetStartedPrivateConsultationHandler is a handler for the /get-started/private-consultation page
func GetStartedPrivateConsultationHandler(c buffalo.Context) error {
	c.Set("current_route", "get_started_private_consultation")
	c.Set("pageTitle", "Private Consultation - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("get_started/private_consultation.plush.html"))
}

// GetStartedGiftHandler is a handler for the /get-started/gift-a-visual-reading-course page
func GetStartedGiftHandler(c buffalo.Context) error {
	c.Set("current_route", "get_started_gift")
	c.<PERSON>("pageTitle", "Gift a Visual Reading Course - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("get_started/gift.plush.html"))
}

// GetStartedOrganisationsHandler is a handler for the /get-started/organisations page
func GetStartedOrganisationsHandler(c buffalo.Context) error {
	c.Set("current_route", "get_started_organisations")
	c.Set("pageTitle", "Organisations - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("get_started/organisations.plush.html"))
}

// GetStartedCoachesHandler is a handler for the /get-started/coaches page
func GetStartedCoachesHandler(c buffalo.Context) error {
	c.Set("current_route", "get_started_coaches")
	c.Set("pageTitle", "Coaches - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("get_started/coaches.plush.html"))
}

// GetStartedDSAStudentsHandler is a handler for the /get-started/dsa-students page
func GetStartedDSAStudentsHandler(c buffalo.Context) error {
	c.Set("current_route", "get_started_dsa_students")
	c.Set("pageTitle", "DSA Students - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("get_started/dsa_students.plush.html"))
}

// PayPerViewHandler is a handler for the /pay-per-view page
func PayPerViewHandler(c buffalo.Context) error {
	c.Set("current_route", "pay_per_view")
	c.Set("pageTitle", "Pay Per View - Visual Reading Online")
	return c.Render(http.StatusOK, r.HTML("resources/pay_per_view.plush.html"))
}
