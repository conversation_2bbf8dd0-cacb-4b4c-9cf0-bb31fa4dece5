package actions

import (
	"time"
	"visualreadingonline/public"
	"visualreadingonline/templates"

	"github.com/gobuffalo/buffalo"
	"github.com/gobuffalo/buffalo/render"
)

var r *render.Engine

func init() {
	r = render.New(render.Options{
		// HTML layout to be used for all HTML requests:
		HTMLLayout: "application.plush.html",

		// fs.FS containing templates
		TemplatesFS: templates.FS(),

		// fs.FS containing assets
		AssetsFS: public.FS(),

		// Add template helpers here:
		Helpers: render.Helpers{
			// for non-bootstrap form helpers uncomment the lines
			// below and import "github.com/gobuffalo/helpers/forms"
			// forms.FormKey:     forms.Form,
			// forms.FormForKey:  forms.FormFor,

			// Custom helper for current year
			"current_year": func() int {
				return time.Now().Year()
			},

			// Helper to get the current route name
			"current_route_name": func(c buffalo.Context) string {
				if c.Value("current_route") != nil {
					return c.Value("current_route").(string)
				}
				return ""
			},

			// Helper to check if current route matches a name
			"is_current_route": func(c buffalo.Context, name string) bool {
				if c.Value("current_route") != nil {
					return c.Value("current_route").(string) == name
				}
				return false
			},

			// Helper to check if a value is in an array
			"contains": func(value string, array []string) bool {
				for _, v := range array {
					if v == value {
						return true
					}
				}
				return false
			},
		},
	})
}
