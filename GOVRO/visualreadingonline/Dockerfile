# This is a multi-stage Dockerfile and requires >= Docker 17.05
# https://docs.docker.com/engine/userguide/eng-image/multistage-build/
FROM golang:1.21-alpine as builder

ENV GOPROXY http://proxy.golang.org
ENV GO111MODULE=on
ENV CGO_ENABLED=0

RUN apk add --no-cache build-base nodejs npm git

RUN mkdir -p /src/visualreadingonline
WORKDIR /src/visualreadingonline

# this will cache the npm install step, unless package.json changes
COPY package.json .
RUN npm install --no-progress

# Copy the Go Modules manifests
COPY go.mod go.mod
COPY go.sum go.sum
# cache deps before building and copying source so that we don't need to re-download as much
# and so that source changes don't invalidate our downloaded layer
RUN go mod download

# Copy the rest of the application
COPY . .

# Build the application
RUN go mod tidy && go build -v -tags development -o /bin/app ./cmd/app

FROM alpine:latest
RUN apk add --no-cache bash ca-certificates

# Create the application directory structure
RUN mkdir -p /app/visualreadingonline
WORKDIR /app/visualreadingonline

# Copy the compiled binary
COPY --from=builder /bin/app /app/visualreadingonline/app

# Copy configuration files
COPY --from=builder /src/visualreadingonline/database.yml /app/visualreadingonline/database.yml
COPY --from=builder /src/visualreadingonline/config /app/visualreadingonline/config
COPY --from=builder /src/visualreadingonline/public /app/visualreadingonline/public
COPY --from=builder /src/visualreadingonline/templates /app/visualreadingonline/templates
COPY --from=builder /src/visualreadingonline/locales /app/visualreadingonline/locales

# Set environment variables
ENV GO_ENV=production
ENV SESSION_SECRET=your-secret-key-here
ENV ADDR=0.0.0.0:8000
ENV POP_PATH=/app/visualreadingonline
ENV NO_POP=0

# Expose port 8000
EXPOSE 8000

# Run migrations and start the application
CMD exec ./app
