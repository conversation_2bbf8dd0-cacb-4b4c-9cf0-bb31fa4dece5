{"name": "buffalo", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"dev": "webpack --watch", "build": "webpack --mode production --progress"}, "repository": "github.com/gobuffalo/buffalo", "dependencies": {"@fortawesome/fontawesome-free": "^5.12.0", "@popperjs/core": "^2.0.0", "alpinejs": "^3.14.9", "autoprefixer": "^10.4.7", "bootstrap": "^5.0.0", "htmx.org": "^1.9.12", "jquery": "^3.6.0", "jquery-ujs": "^1.2.2", "postcss": "^8.4.14", "tailwindcss": "^3.1.8"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "babel-loader": "^8.1.0", "copy-webpack-plugin": "~6.4.1", "css-loader": "~3.5.1", "expose-loader": "^3.0.0", "file-loader": "~6.0.0", "glob": "^7.2.0", "gopherjs-loader": "^0.0.1", "mini-css-extract-plugin": "^2.0.0", "npm-install-webpack-plugin": "4.0.5", "postcss-loader": "^6.2.1", "sass": "^1.0.0", "sass-loader": "~10.2.0", "style-loader": "~1.1.3", "terser-webpack-plugin": "~2.3.1", "url-loader": "~4.1.0", "webpack": "~5.65.0", "webpack-cli": "^4.0.0", "webpack-livereload-plugin": "^3.0.0", "webpack-manifest-plugin": "^4.0.0"}}