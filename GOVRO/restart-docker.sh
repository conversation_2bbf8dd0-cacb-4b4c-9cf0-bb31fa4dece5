#!/bin/bash

# Stop and restart the Docker container
echo "Stopping and restarting the GOVROv1 container..."

# Navigate to the project directory
cd "$(dirname "$0")"

# Stop the container
echo "Stopping container..."
docker-compose down

# Build and start the container
echo "Building and starting container..."
docker-compose up -d --build

# Check if the container is running
if [ "$(docker ps -q -f name=GOVROv1)" ]; then
    echo "Container GOVROv1 is now running on **********:8000"
    echo "You can check the logs with: docker logs GOVROv1"
    echo "You can stop the container with: docker-compose down"
else
    echo "Error: Container failed to start. Checking logs..."
    docker-compose logs
    echo "You can try to fix the issues and run this script again."
fi
