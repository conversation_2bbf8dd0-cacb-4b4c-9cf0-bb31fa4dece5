# Visual Reading Online - Application Overview

## Starting the Application

### Development Mode
1. Using Buffalo CLI:
   ```
   export ADDR="**********:8000"
   export GO_ENV="development"
   export SESSION_SECRET="your-secret-key-here"
   cd visualreadingonline
   buffalo dev
   ```

2. Using direct build:
   ```
   export ADDR="**********:8000"
   export GO_ENV="development"
   export SESSION_SECRET="your-secret-key-here"
   cd visualreadingonline
   go build -v -tags development -o tmp/visualreadingonline-build ./cmd/app
   ./tmp/visualreadingonline-build
   ```

3. Using provided scripts:
   - `./run-buffalo.sh` - Builds and runs the app directly
   - `./start-buffalo.sh` - Similar to run-buffalo.sh
   - `./run-server.sh` - Uses buffalo dev command

### Production Mode (Docker)
1. Using docker-compose:
   ```
   ./run-docker.sh
   ```

2. Using direct Docker command:
   ```
   ./run-docker-direct.sh
   ```

## Configuration
- ADDR: Server address (default: "**********:8000")
- GO_ENV: Environment (development/production/test)
- SESSION_SECRET: Secret key for session encryption
- NO_POP: Set to 1 to disable Pop ORM
- POP_PATH: Path for Pop ORM database files

## Routes and Pages

### Main Navigation
- `/` - Home page (HomeHandler)
- `/learn-more` - Learn More page (LearnMoreHandler)
- `/get-started` - Get Started main page (GetStartedHandler)

### Get Started Section
- `/get-started/private-students` - Information for private students
- `/get-started/private-consultation` - Private consultation information
- `/get-started/gift-a-visual-reading-course` - Gift course information
- `/get-started/organisations` - Information for organizations
- `/get-started/coaches` - Information for coaches
- `/get-started/dsa-students` - Information for DSA students
- `/pay-per-view` - Pay-per-view options

### Resources
- `/dr-cooper` - Information about Dr. Cooper
- `/articles-and-blogs` - Articles and blogs landing page
- `/articles` - Articles listing
- `/blog-posts` - Blog posts listing
- `/faqs` - Frequently asked questions
- `/our-results` - Results and testimonials
- `/products` - Products information
- `/student-feedback` - Student feedback and testimonials
- `/useful-links` - Useful links collection

### Support
- `/support` - Support main page
- `/contact` - Contact form
- `/contact-success` - Contact form submission success page

### Authentication
- `/signup` - User registration
- `/login` - User login
- `/logout` - User logout
- `/request-magic-link` - Request passwordless login
- `/auth/magic-link/{token}` - Magic link authentication
- `/auth/google` - Google OAuth authentication
- `/auth/google/callback` - Google OAuth callback

### Legal
- `/terms-of-service` - Terms of service
- `/privacy-policy` - Privacy policy

### Utility
- `/email-tracking/{tracking_id}` - Email tracking endpoint

## Technology Stack
- Backend: Go Buffalo framework
- Frontend: Tailwind CSS 3.1.8, Alpine.js, HTMX
- Templates: Buffalo's Plush templates
- Database: Optional Pop ORM integration
- Authentication: Built-in session-based auth with Google OAuth option